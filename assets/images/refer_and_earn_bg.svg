<svg width="414" height="896" viewBox="0 0 414 896" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f)">
<circle cx="11" cy="292" r="134" fill="#DBC4F9"/>
</g>
<g filter="url(#filter1_f)">
<circle cx="455.388" cy="205.089" r="106.145" fill="#56CCF2"/>
</g>
<g filter="url(#filter2_b)">
<rect width="414" height="1290" fill="white" fill-opacity="0.4"/>
</g>
<path d="M326.776 -5.80444C326.776 -5.80444 348.709 58.4928 302.69 104.892C284.18 123.579 265.598 131.781 193.627 137.436C160.22 140.054 114.955 155.01 85.7596 216.833C85.7596 216.833 68.8527 250.595 73.7943 320.177C74.6567 331.292 73.4973 342.472 70.3723 353.174C64.3896 373.379 49.3014 399.646 11.9218 407.429C11.9218 407.429 -33.4745 414.315 -95.9573 380.266C-95.9573 380.266 -141.581 359.846 -175 362.105V-7L326.776 -5.80444Z" fill="#EDF1FF"/>
<defs>
<filter id="filter0_f" x="-157.309" y="123.691" width="336.619" height="336.619" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="17.1547" result="effect1_foregroundBlur"/>
</filter>
<filter id="filter1_f" x="314.934" y="64.6349" width="280.908" height="280.908" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="17.1547" result="effect1_foregroundBlur"/>
</filter>
<filter id="filter2_b" x="-214.434" y="-214.434" width="842.868" height="1718.87" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImage" stdDeviation="107.217"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur" result="shape"/>
</filter>
</defs>
</svg>
