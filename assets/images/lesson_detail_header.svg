<svg width="414" height="259" viewBox="0 0 414 259" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0" mask-type="alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="414" height="259">
<path d="M0 0H414V229C414 245.569 400.569 259 384 259H30C13.4315 259 0 245.569 0 229V0Z" fill="url(#paint0_linear)"/>
</mask>
<g mask="url(#mask0)">
<rect x="306" y="113" width="97" height="95" rx="10" fill="white" fill-opacity="0.1"/>
<rect x="-10" y="189" width="97" height="95" rx="10" fill="white" fill-opacity="0.1"/>
<rect x="269" y="84" width="97" height="95" rx="10" fill="white" fill-opacity="0.1"/>
<rect x="-47" y="160" width="97" height="95" rx="10" fill="white" fill-opacity="0.1"/>
<rect x="270" y="191" width="58" height="51" rx="10" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<linearGradient id="paint0_linear" x1="207" y1="0" x2="207" y2="259" gradientUnits="userSpaceOnUse">
<stop stop-color="#7B57FB"/>
<stop offset="1" stop-color="#909BFB"/>
</linearGradient>
</defs>
</svg>
