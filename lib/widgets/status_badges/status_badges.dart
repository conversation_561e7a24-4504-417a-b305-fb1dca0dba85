import 'package:flutter/material.dart';
import 'package:flutter_screen_scaling/flutter_screen_scaling.dart';

class StatusBadgeModel {
  String? text;
  Color? backgroundColor;
  Color? textColor;
  String? iconPath;

  StatusBadgeModel(
      {this.text, this.backgroundColor, this.textColor, this.iconPath});
}

class StatusBadge extends StatelessWidget {
  final Color backgroundColor;
  final String text;
  final Color textColor;
  final int textSize;
  final String? iconPath;
  final int borderRadius;
  final EdgeInsets? padding;
  final Color borderColor;
  final int minWidth;

  StatusBadge({
    required this.text,
    this.iconPath,
    this.borderRadius = 99,
    this.backgroundColor = const Color(0xffddf0dd),
    this.textColor = const Color(0xff27ae60),
    this.textSize = 16,
    this.padding,
    this.borderColor = Colors.transparent,
    this.minWidth = 50,
  });

  @override
  Widget build(BuildContext context) {
    final iconVisible = iconPath != null;

    return Container(
      margin: EdgeInsets.only(
        right: 5,
        left: 5,
      ),
      constraints: BoxConstraints(minWidth: minWidth * 1, maxWidth: 140),
      padding: padding ?? EdgeInsets.only(left: 7, right: 9, top: 2, bottom: 2),
      decoration: BoxDecoration(
          color: backgroundColor,
          border: Border.all(color: borderColor, width: 1),
          borderRadius:
              BorderRadius.circular(ScreenScale.convertHeight(borderRadius))),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Visibility(
            child: Container(
                width: 14,
                height: 14,
                child: iconVisible
                    ? Image.asset(
                        iconPath!,
                        fit: BoxFit.fill,
                      )
                    : Container()),
            visible: iconVisible,
          ),
          if (text.isNotEmpty)
            Container(
              margin: EdgeInsets.only(left: 5),
              child: Text(
                text,
                style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: textSize * 1,
                    color: textColor,
                    height: 20.0 / textSize.toDouble()),
                textAlign: TextAlign.center,
                textHeightBehavior: TextHeightBehavior(
                    applyHeightToFirstAscent: false,
                    applyHeightToLastDescent: false),
              ),
            )
        ],
      ),
    );
  }
}
