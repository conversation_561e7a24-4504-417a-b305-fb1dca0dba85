import 'package:flutter/material.dart';
import 'package:tutorO/constants/assets.dart';
import 'package:tutorO/constants/colors.dart';

class InDevelopmentWidget extends StatelessWidget {
  const InDevelopmentWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        mainAxisSize: MainAxisSize.max,
        children: [
          Container(
            decoration: BoxDecoration(
                border: Border.all(
                    color: AppColors.mainBorderColor,
                    style: BorderStyle.solid,
                    width: 1),
                boxShadow: [
                  BoxShadow(
                      blurRadius: 15,
                      color: AppColors.mainShadowColor,
                      offset: Offset(2, 2))
                ],
                borderRadius: BorderRadius.circular(10),
                color: Colors.yellow),
            width: 400,
            height: 400,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: Image.asset(
                Assets.inDevelopment,
                fit: BoxFit.fill,
              ),
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Tính năng',
                style: TextStyle(
                    fontSize: 45,
                    fontWeight: FontWeight.w600,
                    color: AppColors.secondTextColor),
              ),
              Text(
                'Đang được đội ngũ phát triển',
                style: TextStyle(
                    fontSize: 30,
                    fontWeight: FontWeight.w600,
                    color: Color(0xff43A6DD)),
              ),
              Text(
                'Bạn hãy quay lại sau nhé !',
                style: TextStyle(
                    fontSize: 25,
                    fontWeight: FontWeight.w400,
                    color: AppColors.yellowNegativeTextColor),
              ),
            ],
          )
        ],
      ),
    );
  }
}
