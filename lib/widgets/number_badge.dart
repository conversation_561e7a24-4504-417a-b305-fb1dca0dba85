import 'package:flutter/material.dart';
import '../../constants/colors.dart';

class NumberBadge extends StatelessWidget {
  final Color backgroundColor;
  final int value;

  NumberBadge(
      {required this.value, this.backgroundColor = AppColors.orangeBtnColor});

  @override
  Widget build(BuildContext context) {
    if (value == 0) return Container();
    return Container(
      padding: EdgeInsets.only(left: 8, right: 8),
      decoration: BoxDecoration(
          color: backgroundColor, borderRadius: BorderRadius.circular(20)),
      alignment: Alignment.center,
      child: Text(
        value > 0 ? value.toString().padLeft(2, '0') : '0',
        style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w700,
            fontSize: 16,
            height: 1.42),
        textHeightBehavior: TextHeightBehavior(
            applyHeightToFirstAscent: false, applyHeightToLastDescent: false),
      ),
    );
  }
}
