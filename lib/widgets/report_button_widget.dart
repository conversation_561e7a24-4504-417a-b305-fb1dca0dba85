import 'package:flutter/material.dart';
import '../constants/colors.dart';

class ViewReportButtonWidget extends StatefulWidget {
  ViewReportButtonWidget({
    Key? key,
    required this.onTap,
  }) : super(key: key);
  final VoidCallback onTap;

  @override
  State<ViewReportButtonWidget> createState() => _ViewReportButtonWidgetState();
}

class _ViewReportButtonWidgetState extends State<ViewReportButtonWidget> {
  Color? backgroundColor = AppColors.mainBgColor;
  Color? hoverColor = AppColors.mainColor;
  Color? textColor = AppColors.mainColor;
  bool? isHover = false;
  @override
  Widget build(BuildContext context) {
    final hoveredTransform = Matrix4.identity()..scale(1.1);
    final transform1 = isHover == true ? hoveredTransform : Matrix4.identity();
    return MouseRegion(
      onHover: (event) {
        setState(() {
          isHover = true;
        });
      },
      onExit: (event) {
        setState(() {
          isHover = false;
        });
      },
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: widget.onTap,
        child: AnimatedContainer(
          transform: transform1,
          duration: const Duration(milliseconds: 250),
          margin: EdgeInsets.symmetric(vertical: 5),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                margin: EdgeInsets.all(5),
                padding: EdgeInsets.symmetric(horizontal: 25, vertical: 8),
                decoration: BoxDecoration(
                    color: backgroundColor,
                    boxShadow: [
                      BoxShadow(
                          color: AppColors.mainShadowColor,
                          offset: Offset(1, 2),
                          blurRadius: 3)
                    ],
                    borderRadius: BorderRadius.all(Radius.circular(10)),
                    border: Border.all(width: 0.05, color: Colors.blueGrey)),
                child: Text(
                  "Xem báo cáo",
                  style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w700,
                      color: AppColors.mainTextColor),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
