import 'package:flutter/material.dart';
import 'package:tutorO/models/reason_off/reason_off.dart';
import 'package:tutorO/stores/lesson_store/lesson_store.dart';
import 'package:tutorO/ui/common/confirm_label.dart';
import 'package:tutorO/ui/session/widget/confirm_off_dialog.dart';
import 'package:tutorO/utils/utils.dart';

class SessionDetailPrimaryButtonWidget extends StatefulWidget {
  final String? text;

  final Color? buttonColor;
  final LessonStore lessonStore;
  final int sessionId;
  SessionDetailPrimaryButtonWidget(
      {Key? key,
      this.text = "",
      this.buttonColor,
      required this.lessonStore,
      required this.sessionId})
      : super(key: key);

  @override
  State<SessionDetailPrimaryButtonWidget> createState() =>
      _SessionDetailPrimaryButtonWidgetState();
}

class _SessionDetailPrimaryButtonWidgetState
    extends State<SessionDetailPrimaryButtonWidget> {
  bool isHover = false;
  @override
  Widget build(BuildContext context) {
    final hoveredTransform = Matrix4.identity()..scale(1.1);
    final transform1 = isHover == true ? hoveredTransform : Matrix4.identity();
    var classSession = widget.lessonStore.classSession;
    if (classSession.requestOffSession == null ||
        classSession.checkInStatus == null) return Container();
    if (classSession.isCancelSessionSuccess()) {
      return Container(
          margin: EdgeInsets.symmetric(vertical: 32),
          child: ConfirmLabel(
            title: "Đơn xin nghỉ của bạn đã được duyệt",
            enable: false,
            colorText: Colors.green.shade400,
          ));
    }

    if (classSession.isCancelSessionFail()) {
      return Container(
          margin: EdgeInsets.symmetric(vertical: 32),
          child: ConfirmLabel(
              title: "Đơn xin nghỉ của bạn đã không được duyệt",
              enable: false,
              colorText: Colors.red.shade400));
    }
    if (classSession.isCheckinSuccess() ||
        classSession.isMissCheckin() ||
        classSession.isCheckinLate() ||
        Utils.isStartLesson(classSession)) {
      return Container();
    }

    return MouseRegion(
      onHover: (event) {
        setState(() {
          isHover = true;
        });
      },
      onExit: (event) {
        setState(() {
          isHover = false;
        });
      },
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          if (classSession.requestOffSession!.isAvailableCancelSession()) {
            showConfirmOffClassDialog(context);
          } else {
            widget.lessonStore.cancelPermissionForm(classSession.sessionId ?? 0,
                classSession.requestOffSession!.reason!);
          }
        },
        child: AnimatedContainer(
          transform: transform1,
          duration: const Duration(milliseconds: 250),
          child: Container(
            margin: EdgeInsets.symmetric(vertical: 20),
            padding: EdgeInsets.symmetric(horizontal: 40, vertical: 15),
            decoration: BoxDecoration(
              color: widget.buttonColor,
              borderRadius: BorderRadius.all(
                Radius.circular(15),
              ),
            ),
            child: Center(
              child: Text("${widget.text}",
                  style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w700,
                      color: Colors.white)),
            ),
          ),
        ),
      ),
    );
  }

  void showConfirmOffClassDialog(BuildContext context) {
    showDialog(
        context: context,
        builder: (dialogContext) {
          return ConfirmOffClassDialog(
            needCodeOff: true,
            dialogContext: dialogContext,
            btnConfirmCallback: (text, code) {
              if (code != null && code.isNotEmpty) {
                widget.lessonStore.cancelTeachSession(
                    widget.sessionId, ReasonOff(code: code, text: text));
              }
            },
          );
        });
  }
}
