import 'package:flutter/material.dart';
import 'package:flutter_screen_scaling/flutter_screen_scaling.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:image_picker/image_picker.dart';
import '../../constants/assets.dart';
import '../../utils/file/file.dart';
import '../../utils/locale/app_localization.dart';

class MediaPickedFile {
  int id;
  MediaTypePicked type;
  String path;
  bool? isPickFromGallery;

  MediaPickedFile(
      {required this.path,
      required this.type,
      this.isPickFromGallery = true,
      required this.id});

  @override
  String toString() {
    return "${type.toString()}-path-"
        "${DateTime.now().microsecondsSinceEpoch / 1000}";
  }
}

class MediaPickerModal extends StatelessWidget {
  final Function(MediaPickedFile pickedMedia)? callback;
  final picker = ImagePicker();

  MediaPickerModal({this.callback});

  Future getImage(ImageSource source) async {
    final pickedFile = await picker.pickImage(source: source);
    if (pickedFile != null && callback != null) {
      callback!(MediaPickedFile(
          path: pickedFile.path, type: MediaTypePicked.Picture, id: 0));
    }
  }

  Future getVideo(ImageSource source) async {
    final pickedFile = await picker.pickVideo(source: source);
    if (pickedFile != null && callback != null) {
      callback!(MediaPickedFile(
          path: pickedFile.path, type: MediaTypePicked.Video, id: 0));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        height: ScreenScale.convertHeight(170),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: const Radius.circular(20),
            topRight: const Radius.circular(20),
          ),
        ),
        child: Center(
            child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: <Widget>[
            Container(
                margin: EdgeInsets.only(top: ScreenScale.convertHeight(8)),
                height: ScreenScale.convertHeight(5),
                width: ScreenScale.convertWidth(62),
                color: Color(0xffD9D9D9)),
            Container(
              margin: EdgeInsets.only(left: ScreenScale.convertWidth(28)),
              child: Column(
                children: [
                  Container(
                    margin: EdgeInsets.only(top: ScreenScale.convertHeight(28)),
                    child: _buildItem(Assets.uploadImageIcon,
                        AppLocalizations.of(context).translate('image_gallery'),
                        () {
                      //todo pick image
                      getImage(ImageSource.gallery);
                    }),
                  ),
                  Container(
                      color: Colors.green,
                      child: Divider(
                        color: Color(0xffF0F0F0),
                        thickness: 1,
                        height: 0,
                      )),
                  Container(
                    margin: EdgeInsets.only(top: ScreenScale.convertHeight(20)),
                    child: _buildItem(Assets.cameraIcon,
                        AppLocalizations.of(context).translate('image_camera'),
                        () {
                      getImage(ImageSource.camera);
                    }),
                  ),
//                  Container(
//                      color: Colors.green,
//                      child: Divider(
//                        color: Color(0xffF0F0F0),
//                        thickness: 1,
//                        height: 0,
//                      )),
//                  Container(
//                    margin: EdgeInsets.only(top: ScreenScale.convertHeight(20)),
//                    child: _buildItem(Assets.uploadImageIcon,
//                        AppLocalizations.of(context).translate('video_gallery'),
//                        () {
//                      getVideo(ImageSource.gallery);
//                    }),
//                  ),
//                  Container(
//                      color: Colors.green,
//                      child: Divider(
//                        color: Color(0xffF0F0F0),
//                        thickness: 1,
//                        height: 0,
//                      )),
//                  Container(
//                    margin: EdgeInsets.only(top: ScreenScale.convertHeight(20)),
//                    child: _buildItem(Assets.cameraIcon,
//                        AppLocalizations.of(context).translate('video_camera'),
//                        () {
//                      getVideo(ImageSource.camera);
//                    }),
//                  )
                ],
              ),
            ),
          ],
        )));
  }

  Widget _buildItem(String assets, String text, Function callback) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () => callback(),
      child: Container(
        height: ScreenScale.convertHeight(54),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: ScreenScale.convertWidth(36),
                  height: ScreenScale.convertHeight(36),
                  decoration: BoxDecoration(
                    color: Color(0xffE5E5E5),
                    shape: BoxShape.circle,
                  ),
                  padding: EdgeInsets.all(ScreenScale.convertWidth(8)),
                  child: SvgPicture.asset(assets),
                ),
                Container(
                    margin: EdgeInsets.only(left: ScreenScale.convertWidth(17)),
                    child: Text(
                      text,
                      style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: ScreenScale.convertFontSize(16)),
                    )),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
