// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDvs7eeh69Y2XP7--eKYyJUF4g1Ayztyfk',
    appId: '1:527932202397:android:8731f09f03113c0215e374',
    messagingSenderId: '527932202397',
    projectId: 'teky-tutoro',
    storageBucket: 'teky-tutoro.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDEhBgBTBM4Es_Lj482vwZfwKh4w6IJ52A',
    appId: '1:527932202397:ios:8231407525b2769e15e374',
    messagingSenderId: '527932202397',
    projectId: 'teky-tutoro',
    storageBucket: 'teky-tutoro.appspot.com',
    androidClientId: '527932202397-4mrj7dccv275u7daqu1bpkpn3mdh1s1c.apps.googleusercontent.com',
    iosClientId: '527932202397-1vejg61uk32bih6dfis9ogfmkkps7vrp.apps.googleusercontent.com',
    iosBundleId: 'com.teky.tutoro',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDEhBgBTBM4Es_Lj482vwZfwKh4w6IJ52A',
    appId: '1:527932202397:ios:8231407525b2769e15e374',
    messagingSenderId: '527932202397',
    projectId: 'teky-tutoro',
    storageBucket: 'teky-tutoro.appspot.com',
    androidClientId: '527932202397-4mrj7dccv275u7daqu1bpkpn3mdh1s1c.apps.googleusercontent.com',
    iosClientId: '527932202397-1vejg61uk32bih6dfis9ogfmkkps7vrp.apps.googleusercontent.com',
    iosBundleId: 'com.teky.tutoro',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyASj6iSsvWQeM7jFSuiAHKhzqzQ62Ek_Vs',
    appId: '1:527932202397:web:c1bc15874527fc9d15e374',
    messagingSenderId: '527932202397',
    projectId: 'teky-tutoro',
    authDomain: 'teky-tutoro.firebaseapp.com',
    storageBucket: 'teky-tutoro.appspot.com',
  );
}
