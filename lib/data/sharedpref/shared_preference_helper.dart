import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';

import 'constants/preferences.dart';

class SharedPreferenceHelper {
  // shared pref instance
  final Future<SharedPreferences> _sharedPreference;

  // constructor
  SharedPreferenceHelper(this._sharedPreference);

  // General Methods: ----------------------------------------------------------
  Future<String?> get authToken async {
    return _sharedPreference.then((preference) {
      return preference.getString(Preferences.auth_token);
    });
  }

  Future<void> saveAuthToken(String authToken) async {
    return _sharedPreference.then((preference) {
      preference.setString(Preferences.auth_token, authToken);
    });
  }

  Future<void> removeAuthToken() async {
    return _sharedPreference.then((preference) {
      preference.remove(Preferences.auth_token);
    });
  }

  Future<String?> get fcmToken async {
    return _sharedPreference.then((preference) {
      return preference.getString(Preferences.fcmToken);
    });
  }

  Future saveFCMToken(String fcmToken) async {
    return _sharedPreference.then((preference) {
      preference.setString(Preferences.fcmToken, fcmToken);
    });
  }

  Future<String?> get deviceId async {
    return _sharedPreference.then((preference) {
      return preference.getString(Preferences.deviceId);
    });
  }

  Future<void> saveDeviceId(String deviceId) async {
    return _sharedPreference.then((preference) {
      preference.setString(Preferences.deviceId, deviceId);
    });
  }

  Future setTab(int navTab) async {
    return _sharedPreference.then((preference) {
      preference.setInt(Preferences.navTab, navTab);
    });
  }

  // Login:---------------------------------------------------------------------
  Future<bool> get isLoggedIn async {
    return _sharedPreference.then((preference) {
      return preference.getBool(Preferences.is_logged_in) ?? false;
    });
  }

  // ignore: avoid_positional_boolean_parameters
  Future<void> saveIsLoggedIn(bool value) async {
    return _sharedPreference.then((preference) {
      preference.setBool(Preferences.is_logged_in, value);
    });
  }

  // Theme:------------------------------------------------------
  Future<bool> get isDarkMode {
    return _sharedPreference.then((prefs) {
      return prefs.getBool(Preferences.is_dark_mode) ?? false;
    });
  }

  // ignore: avoid_positional_boolean_parameters
  Future<void> changeBrightnessToDark(bool value) {
    return _sharedPreference.then((prefs) {
      return prefs.setBool(Preferences.is_dark_mode, value);
    });
  }

  // Language:---------------------------------------------------
  Future<String> get currentLanguage {
    return _sharedPreference.then((prefs) {
      return prefs.getString(Preferences.current_language) ?? 'vi';
    });
  }

  // tab
  Future<int> get currenTabIndex {
    return _sharedPreference.then((prefs) {
      return prefs.getInt(Preferences.navTab) ?? 0;
    });
  }

  Future<void> changeLanguage(String language) {
    return _sharedPreference.then((prefs) {
      return prefs.setString(Preferences.current_language, language);
    });
  }
}
