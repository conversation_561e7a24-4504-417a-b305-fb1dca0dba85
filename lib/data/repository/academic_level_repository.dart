import 'dart:io';

import '../../models/academic_level/academic_level.dart';
import '../network/apis/academic_level/academic_level_api.dart';

class AcademicLevelRepository {
  final AcademicLevelAPI _academicLevelAPI;

  AcademicLevelRepository({required AcademicLevelAPI academicLevelAPI})
      : _academicLevelAPI = academicLevelAPI;

  Future<List<AcademicLevel>> fetchAcademicLevels() async {
    return await _academicLevelAPI.fetchAcademicLevels();
  }

  Future<AcademicLevel> addAcademicLevel(
      AcademicLevel academicLevel, File file) async {
    return await _academicLevelAPI
        .addAcademicLevel(await academicLevel.toFormData(file));
  }

  Future<AcademicLevel> editAcademicLevel(
      AcademicLevel academicLevel, File? file) async {
    return await _academicLevelAPI.editAcademicLevel(
        await academicLevel.toFormData(file), academicLevel.id!);
  }

  Future<bool> deleteAcademicLevel(int id) async {
    return await _academicLevelAPI.removeAcademicLevel(id);
  }
}
