import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:tutorO/data/network/apis/firebase/firebase_api.dart';
import 'package:tutorO/data/network/apis/global/global_api.dart';
import 'package:tutorO/data/sharedpref/shared_preference_helper.dart';

class Repository {
  // api objects
  // final PostApi _postApi;
  final GlobalAppAPI _appVersionAPI;
  final FirebaseAPI _firebaseAPI;

  // shared pref object
  final SharedPreferenceHelper _sharedPrefsHelper;

  // shared connectivity
  final Connectivity _connectivity =
      Connectivity(); // Connectivity already works as Singleton

  // constructor
  Repository(this._firebaseAPI, this._appVersionAPI, this._sharedPrefsHelper);

  // Connectivity
  Future<ConnectivityResult> checkConnectivity() async {
    var connectivityResult = await _connectivity.checkConnectivity();
    return connectivityResult;
  }

  Connectivity getConnectivity() {
    return _connectivity;
  }

  // Firebase
  Future<bool> initFirebase() async {
    return await _firebaseAPI.initFirebase().then((success) {
      return success;
    }).catchError((error) => throw error);
  }

  // FCM
  Future<void> saveFCMToken(String token) =>
      _sharedPrefsHelper.saveFCMToken(token);

  // Device id
  Future<void> saveDeviceId(String deviceId) =>
      _sharedPrefsHelper.saveDeviceId(deviceId);

  // Theme: --------------------------------------------------------------------
  Future<void> changeBrightnessToDark({required bool isDarkMode}) =>
      _sharedPrefsHelper.changeBrightnessToDark(isDarkMode);

  Future<bool> get isDarkMode => _sharedPrefsHelper.isDarkMode;

  // Language: -----------------------------------------------------------------
  Future<void> changeLanguage(String value) {
    _appVersionAPI.changeLanguage(value);
    return _sharedPrefsHelper.changeLanguage(value);
  }

  Future<String> get currentLanguage => _sharedPrefsHelper.currentLanguage;
}
