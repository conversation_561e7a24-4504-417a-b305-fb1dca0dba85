import 'package:tutorO/data/network/apis/salary/salary_api.dart';
import 'package:tutorO/models/salary/salary_model.dart';

class SalaryRepository {
  final SalaryAPI _salaryAPI;
  SalaryRepository({required SalaryAPI salaryAPI}) : _salaryAPI = salaryAPI;
  Future<DataAttendanceInfo> getSalaryInfo(
      String startSearchTime,
      String endSearchTime,
      int pageNumber,
      String? attendanceState,
      String? checkInState) async {
    return await _salaryAPI.getSalaryInfo(startSearchTime, endSearchTime,
        pageNumber, attendanceState, checkInState);
  }
}
