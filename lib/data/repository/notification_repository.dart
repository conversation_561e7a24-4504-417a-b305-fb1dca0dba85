import 'package:tutorO/data/network/apis/notification/notification_api.dart';
import 'package:tutorO/models/notification/notification_list_model.dart';

class NotificationRepository {
  final NotificationAPI _notificationAPI;

  NotificationRepository({required NotificationAPI notificationAPI})
      : _notificationAPI = notificationAPI;

  Future<NotificationListModel> fetchNotifications(
      Map<String, dynamic>? nextCursor) async {
    return await _notificationAPI.fetchNotifications(nextCursor);
  }

  Future<bool> deleteNotification(int notificationId) async {
    return await _notificationAPI.deleteNotification(notificationId);
  }

  Future<bool> markNotificationsAsRead(List<int> notificationIds) async {
    return await _notificationAPI.markNotificationsAsRead(notificationIds);
  }
}
