import 'dart:io';

import 'package:dio/dio.dart';
import 'package:quiver/collection.dart';
import 'package:tutorO/data/network/apis/profile/profile_api.dart';
import 'package:tutorO/models/profile/image_self.dart';
import 'package:tutorO/models/profile/job_info.dart';
import 'package:tutorO/models/profile/personal_info_model.dart';
import 'package:tutorO/models/recommendation/recommendation.dart';
import 'package:tutorO/utils/file/file.dart';
import 'package:tutorO/utils/image/image.dart';

class ProfileRepository {
  final ProfileAPI profileApi;

  ProfileRepository({required this.profileApi});

  Future<JobInfoModel> fetchJobInfo() async {
    return await profileApi.fetchJobInfoModel();
  }

  Future<PersonalInfoModel> fetchPersonalInfo() async {
    return await profileApi.fetchPersonalInfo();
  }

  Future<PersonalInfoModel> uploadImage(File file, String key) async {
    var formData = FormData.fromMap({
      key: await MultipartFile.fromFile(file.path, filename: 'image_$key.png')
    });
    return await profileApi.uploadImage(formData);
  }

  Future<bool> setEmail(String email) async {
    return await profileApi.setEmail(email);
  }

  Future<PersonalInfoModel> updateAllInfo(PersonalInfoModel value) async {
    return await profileApi.updateAllInfo(value.toJsonForEditText());
  }

  Future<PersonalInfoModel> updateAnInfo(Map<String, dynamic> value) async {
    return await profileApi.updateAnInfo(value);
  }

  Future getOtpUpdatePhone(String phone, String token) async {
    return await profileApi.getOtpUpdatePhone(phone, token);
  }

  Future<List<ImageSelf>> fetchImages() async {
    return await profileApi.fetchImagesSelf();
  }

  Future<List<ImageSelf>> uploadImages(List<ImageSelf> images) async {
    var list = Multimap<String, dynamic>();

    await Future.forEach<ImageSelf>(images, (image) async {
      if (image.fileImage != null) {
        print(image.fileImage!.path);
        var imageCompress = await ImageConvert.compressFile(image.fileImage!);
        print(imageCompress.path);
        list.add(
            "images",
            await MultipartFile.fromFile(imageCompress.path,
                filename: 'image${FileHelper.getFileName(imageCompress)}'));
      }
    });
    var formData = FormData.fromMap(list.asMap());
    return await profileApi.uploadImages(formData);
  }

  Future<bool> removeImage(ImageSelf image) async {
    return await profileApi.removeImage(image.id!);
  }

  Future<bool> cancelUpdateProfile() async {
    return await profileApi.cancelUpdateProfile();
  }

  Future<bool> submitProfile() async {
    return await profileApi.submitProfile();
  }

  Future<Recommendations> fetchRecommendationsList() async {
    return await profileApi.fetchRecommendationsList();
  }
}
