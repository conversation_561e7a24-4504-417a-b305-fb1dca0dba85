import 'package:tutorO/data/network/apis/class/class_evaluation_api.dart';
import 'package:tutorO/models/class_session_evaluation/class_session_evaluation.dart';
import 'package:tutorO/models/evaluation_info/evaluation_info_model.dart';
import 'package:tutorO/models/evaluation_template/evaluation_template_model.dart';

class ClassEvaluationRepository {
  final ClassEvaluationAPI _classEvaluationAPI;

  ClassEvaluationRepository({required ClassEvaluationAPI classEvaluationAPI})
      : _classEvaluationAPI = classEvaluationAPI;

  Future<ClassSessionEvaluation> fetchSessionEvaluations(int sessionId) async {
    return await _classEvaluationAPI.fetchSessionEvaluations(sessionId);
  }

  Future<List<EvaluationTemplateModel>> fetchEvaluationTemplates(
      int sessionId) async {
    return await _classEvaluationAPI.fetchEvaluationTemplates(sessionId);
  }

  Future<bool> submitEvaluation(
      {required int sessionId,
      required int studentId,
      required EvaluationTemplateModel evaluation}) async {
    return await _classEvaluationAPI.submitEvaluation(
        sessionId: sessionId, studentId: studentId, evaluation: evaluation);
  }

  Future<EvaluationInfoModel> fetchEvaluationInfo(
      {required int sessionId, required int studentId}) async {
    return await _classEvaluationAPI.fetchEvaluationInfo(
        sessionId: sessionId, studentId: studentId);
  }
}
