import 'package:dio/dio.dart';
import 'package:http_parser/http_parser.dart';
import 'package:tutorO/constants/values.dart';
import 'package:tutorO/models/attendance/attendance.dart';
import 'package:tutorO/models/check_in/check_in.dart';
import 'package:tutorO/models/class/available_classes/available_classes.dart';
import 'package:tutorO/models/class_session/class_session.dart';
import 'package:tutorO/models/class_session/request_off_session/request_off_session.dart';
import 'package:tutorO/models/lesson_image/lesson_image.dart';
import 'package:tutorO/models/lesson_plan/lesson_plan.dart';
import 'package:tutorO/models/lesson_product/lesson_product.dart';
import 'package:tutorO/models/lesson_product/lesson_product_content.dart';
import 'package:tutorO/models/reason_off/reason_off.dart';
import 'package:tutorO/models/report/report.dart';
import 'package:tutorO/models/report_student/report_student.dart';
import 'package:tutorO/models/student/student.dart';
import 'package:tutorO/utils/file/file.dart';
import 'package:tutorO/widgets/media_picker_modal/media_picker_modal.dart';

import '../../models/class_session/class_session_media_plan/class_session_media_plan.dart';
import '../network/apis/class/class_schedule_api.dart';
import '../network/constants/endpoints.dart';

class ClassRepository {
  final ClassAPI _classScheduleAPI;

  ClassRepository({required ClassAPI classScheduleAPI})
      : _classScheduleAPI = classScheduleAPI;

  Future<AvailableClasses> fetchAvailableClasses(
      Map<String, dynamic>? filter) async {
    return await _classScheduleAPI.fetchAvailableClasses(filter: filter);
  }

  Future<int> fetchCountClassToday() async {
    return await _classScheduleAPI.fetchCountClassToday();
  }

  Future<Map<String, dynamic>> requestRegisterClass(int idClass) async {
    return await _classScheduleAPI.requestRegisterClass(idClass);
  }

  Future<Map<String, dynamic>> requestCancelRegisterClass(int idClass) async {
    return await _classScheduleAPI.requestCancelRegisterClass(idClass);
  }

  Future<ClassSessions> fetchClassSession(Map<String, dynamic> query) async {
    return await _classScheduleAPI.fetchClassByDay(query);
  }

  Future<ClassSession> fetchDetailSession(int idSession) async {
    return await _classScheduleAPI.fetchDetailSession(idSession);
  }

  Future<CheckInModel> fetchCheckinStatus(int idSession) async {
    return await _classScheduleAPI.fetchCheckinStatus(idSession);
  }

  Future<RequestOffSession> fetchRequestOffStatus(int idSession) async {
    return await _classScheduleAPI.fetchRequestOffStatus(idSession);
  }

  Future<RequestOffSession> cancelPermissionForm(
      int idSession, ReasonOff reasonOff) async {
    return await _classScheduleAPI.cancelPermissionForm(
        idSession, reasonOff.toJson());
  }

  Future<Map<String, dynamic>> cancelTeachClass(
      int idClass, CancelClassActionType type) async {
    return await _classScheduleAPI.cancelTeachClass(idClass, type);
  }

  Future<RequestOffSession> cancelTeachSession(
      int sessionId, ReasonOff reasonOff) async {
    return await _classScheduleAPI.cancelTeachSession(
        sessionId, reasonOff.toJson());
  }

  Future<CheckInModel> checkInLesson(int idClass) async {
    return await _classScheduleAPI.checkInLesson(idClass);
  }

  Future<bool> reportLessonPlan(int lessonPlanId) async {
    return await _classScheduleAPI.reportLessonPlan(lessonPlanId);
  }

  Future<bool> sendLessonPlanToMail(int lessonPlanId) async {
    return await _classScheduleAPI.sendLessonPlanToMail(lessonPlanId);
  }

  Future<Report> fetchReport(int id, ReportType type) async {
    var endpoint = "";
    if (type == ReportType.lessonReport) {
      endpoint = Endpoints.reportLesson.replaceAll("{session_id}", "$id");
    } else if (type == ReportType.classReport) {
      endpoint = Endpoints.reportClass.replaceAll("{idClass}", "$id");
    }
    return await _classScheduleAPI.fetchReport(endpoint);
  }

  Future<List<LessonPlan>> fetchLessonPlan(int sessionId) async {
    return await _classScheduleAPI.fetchLessonPlan(sessionId);
  }

  Future<ReportStudent> fetchReportStudent(int classId, int studentId) async {
    return await _classScheduleAPI.fetchReportStudent(classId, studentId);
  }

  Future<Map<String, dynamic>> fetchCancelClassStatus(int classId) async {
    return await _classScheduleAPI.fetchCancelClassStatus(classId);
  }

  Future<AvailableClasses> fetchClassTeaching() async {
    return await _classScheduleAPI.fetchClassTeaching();
  }

  Future<List<Student>> fetchStudents(int idClass) async {
    return await _classScheduleAPI.fetchStudents(idClass);
  }

  Future<ListAttendance> fetchAttendances(
      int sessionId, String languageCode) async {
    return await _classScheduleAPI.fetchAttendances(sessionId, languageCode);
  }

  Future<List<LessonImage>> fetchListLessonAndVideo(int sessionId) async {
    return await _classScheduleAPI.fetchListLessonAndVideo(sessionId);
  }

  Future<StudentAttendance> attendanceStudent(int sessionId, int studentId,
      {required bool status}) async {
    return await _classScheduleAPI.attendanceStudent(
        sessionId: sessionId, studentId: studentId, status: status);
  }

  Future<LessonProducts> fetchLessonProducts(int sessionId) async {
    return await _classScheduleAPI.fetchLessonProducts(sessionId);
  }

  Future<ProductContent> fetchDetailProduct(
      int sessionId, int studentId) async {
    return await _classScheduleAPI.fetchDetailProduct(sessionId, studentId);
  }

  Future<Product> createOrUpdateDetailProduct(ProductContent product,
      {required bool isUpdate, required int sessionId}) async {
    var formData = FormData.fromMap(await product.toMap());
    if (isUpdate) {
      return await _classScheduleAPI.updateDetailProduct(formData, sessionId);
    } else {
      return await _classScheduleAPI.createDetailProduct(formData, sessionId);
    }
  }

  Future<bool> deleteDetailProduct(Product product,
      {required int sessionId}) async {
    return await _classScheduleAPI.deleteDetailProduct(
        sessionId, await product.toMapDeleteProduct());
  }

  Future<bool> removeImageOrVideo(
      int sessionId, Map<String, dynamic> data) async {
    return await _classScheduleAPI.removeImageOrVideo(sessionId, data);
  }

  Future<List<LessonImage>> uploadImageOrVideo(
      MediaPickedFile mediaFile, int sessionId) async {
    var fileName = mediaFile.path.split("/").last;
    var contentType;
    if (mediaFile.type == MediaTypePicked.Picture) {
      contentType = MediaType("image", fileName.split(".").last);
    } else if (mediaFile.type == MediaTypePicked.Video) {
      contentType = MediaType("video", "mp4");
    }
    var formData = FormData.fromMap({
      "file_url": await MultipartFile.fromFile(mediaFile.path,
          filename: fileName, contentType: contentType),
      "file_type":
          FileHelper.getTypeFileFormatByServer(mediaFile.type, fileName)
    });
    return await _classScheduleAPI.uploadImageOrVideo(formData, sessionId);
  }

  Future<ListSessionMedialPlanResponse> fetchListMediaPlan(
      int sessionId) async {
    return await _classScheduleAPI.fetchSessionMediaPlan(sessionId);
  }
  // Future<BorrowMaterial> fetchBorrowMaterialRequest(int sessionId) async {
  //   return await _classScheduleAPI.borrowMaterialRequest(sessionId);
  // }

  // Future<String> sendRequestMaterial(
  //     List<MaterialItem> materials, String requestNote, int sessionId) async {
  //   return await _classScheduleAPI.sendRequestMaterial(
  //       materials, requestNote, sessionId);
  // }

  // Future<ListMaterialsResponse?> getListMaterial() {
  //   return _classScheduleAPI.getListMaterials();
  // }
  Future<String> fetchStudentPortfolioUuid(int sessionId, int studentId) async {
    return await _classScheduleAPI.studentPortfolioUuid(sessionId, studentId);
  }
  
  Future<bool> fetchPortfolioState(String studentUuid) async {
    return await _classScheduleAPI.PortfolioState(studentUuid);
  }
}
