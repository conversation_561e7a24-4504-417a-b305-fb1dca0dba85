import 'dart:io';
import 'package:tutorO/data/network/apis/cerificate/certificate_api.dart';
import 'package:tutorO/models/cerificate/certificate_model.dart';

class CertificationRepository {
  final CertificationAPI _certificationAPI;

  CertificationRepository({required CertificationAPI certificationAPI})
      : _certificationAPI = certificationAPI;

  Future<List<CertificationModel>> fetchCertifications() async {
    return await _certificationAPI.fetchCertifications();
  }

  Future<CertificationModel> addCertification(
      CertificationModel newCertificate, File file) async {
    return await _certificationAPI
        .addCertification(await newCertificate.toFormData(file));
  }

  Future<CertificationModel> editCertification(
      CertificationModel editCertificate, File? file) async {
    return await _certificationAPI.editCertification(
        await editCertificate.toFormData(file), editCertificate.id!);
  }

  Future<bool> deleteCertificate(int id) async {
    return await _certificationAPI.deleteCertificate(id);
  }
}
