import 'package:tutorO/data/network/apis/master_data/master_data_api.dart';
import 'package:tutorO/models/academic_title/academic_title.dart';
import 'package:tutorO/models/grade/grade.dart';
import 'package:tutorO/models/master_data/country.dart';
import 'package:tutorO/models/master_data/district.dart';
import 'package:tutorO/models/master_data/province.dart';
import 'package:tutorO/models/master_data/ward.dart';
import 'package:tutorO/models/reason_off/reason_off.dart';
import 'package:tutorO/models/subject/subject.dart';
import 'package:tutorO/models/training_unit/training_unit.dart';

class MasterDataRepository {
  final MasterDataApi masterDataApi;

  MasterDataRepository({required this.masterDataApi});

  Future<List<Country>> fetchCountries() async {
    return await masterDataApi.fetchCountries();
  }

  Future<List<District>> fetchDistricts({Map<String, dynamic>? param}) async {
    return await masterDataApi.fetchDistrict(param: param);
  }

  Future<List<Province>> fetchProvinces({Map<String, dynamic>? param}) async {
    return await masterDataApi.fetchProvince(param: param);
  }

  Future<List<Ward>> fetchWards({Map<String, dynamic>? param}) async {
    return await masterDataApi.fetchWard(param: param);
  }

  Future<List<TrainingUnit>> fetchTrainingUnits() async {
    return await masterDataApi.fetchTrainingUnits();
  }

  Future<List<AcademicTitle>> fetchAcademicTitles() async {
    return await masterDataApi.fetchAcademicTitles();
  }

  Future<String> fetchTermAndRules(String languageCode) async {
    return await masterDataApi.fetchTermAndRules(languageCode);
  }

  Future<List<Subject>> fetchSubjects(String languageCode) async {
    return await masterDataApi.fetchSubjects(languageCode);
  }

  Future<List<Grade>> fetchGrades(String languageCode) async {
    return await masterDataApi.fetchGrades(languageCode);
  }

  Future<List<ReasonOff>> fetchReasonOffLesson() async {
    return await masterDataApi.fetchReasonOffLesson();
  }
}
