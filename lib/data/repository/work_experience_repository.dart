import 'dart:io';

import 'package:tutorO/data/network/apis/work_experience/work_experience_api.dart';
import 'package:tutorO/models/profile/professional_experience.dart';

class WorkExpRepository {
  final WorkExpApi _workExpApi;

  WorkExpRepository({required WorkExpApi workExpApi})
      : _workExpApi = workExpApi;

  Future<WorkExp> updateProfessionalExperience(
      WorkExp value, File? file) async {
    return await _workExpApi.updateProfessionalExperience(
        await value.toFormData(file), value.id ?? -1);
  }

  Future<WorkExp> addProfessionalExperience(WorkExp value, File file) async {
    return await _workExpApi
        .addProfessionalExperience(await value.toFormData(file));
  }

  Future<List<WorkExp>> fetchProfessionalEx() async {
    return await _workExpApi.fetchProfessionalEx();
  }

  Future<bool> deleteProfessionalExp(int id) async {
    return await _workExpApi.removeProfessionalExperience(id);
  }
}
