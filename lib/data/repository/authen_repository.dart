import 'package:tutorO/data/network/apis/authen/authen_api.dart';
import 'package:tutorO/data/sharedpref/shared_preference_helper.dart';
import 'package:tutorO/models/user/authen_model.dart';

class AuthenticationRepository {
  final AuthenApi _authenApi;
  final SharedPreferenceHelper _sharedPrefsHelper;

  AuthenticationRepository(this._sharedPrefsHelper, this._authenApi);
  Future<String?> get authToken => _sharedPrefsHelper.authToken;
  Future<void> saveAuthToken(String token) =>
      _sharedPrefsHelper.saveAuthToken(token);

  Future<void> removeAuthToken() => _sharedPrefsHelper.removeAuthToken();
  Future<AuthenticationModel?> login(
      String phoneNumber, String password) async {
    final deviceId = await _sharedPrefsHelper.deviceId;
    final fcmToken = await _sharedPrefsHelper.fcmToken;

    return await _authenApi.login(
        phoneNumber, password, deviceId ?? "empty_deviceId", fcmToken);
  }

  Future<String> forgot(String email) async {
    return await _authenApi.forgot(email);
  }
}
