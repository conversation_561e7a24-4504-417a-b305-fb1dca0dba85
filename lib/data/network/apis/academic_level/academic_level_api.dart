import 'package:dio/dio.dart';

import '../../../../models/academic_level/academic_level.dart';
import '../../../../models/common_response/common_response.dart';
import '../../constants/endpoints.dart';
import '../../dio_client.dart';

/// APIs for CRUD academic level
class AcademicLevelAPI {
  // dio instance
  final DioClient _dioClient;

  ///
  AcademicLevelAPI({required DioClient dioClient}) : _dioClient = dioClient;

  /// Fetch academic levels
  Future<List<AcademicLevel>> fetchAcademicLevels() async {
    try {
      final res = await _dioClient.get(Endpoints.getAcademicLevels);
      final commonResponseJSON = res["message"] as Map<String, dynamic>;
      var commonResponse = CommonResponse.fromJson(json: commonResponseJSON);
      if (commonResponse.statusCode == 200) {
        final data = res["data"] as List;
        return data.map((json) => AcademicLevel.fromJson(json)).toList();
      } else {
        return List.empty();
      }
    } on Exception catch (_) {
      return List.empty();
    }
  }

  /// Add academic level
  Future<AcademicLevel> addAcademicLevel(FormData data) async {
    try {
      final res = await _dioClient.post(Endpoints.addAcademicLevel, data: data);
      final commonResponseJSON = res["message"] as Map<String, dynamic>;
      var commonResponse = CommonResponse.fromJson(json: commonResponseJSON);
      if (commonResponse.statusCode == 200) {
        return AcademicLevel.fromJson(res["data"]);
      } else {
        final errorJSON = res["error"] as Map<String, dynamic>;
        return Future.error(AcademicLevelError.fromJsonMap(errorJSON));
      }
    } on Exception catch (e) {
      print(e);
      return Future.error(e);
    }
  }

  ///
  Future<AcademicLevel> editAcademicLevel(
      FormData data, int academicLevelId) async {
    try {
      final res = await _dioClient
          .put("${Endpoints.editAcademicLevel}/$academicLevelId", data: data);
      final commonResponseJSON = res["message"] as Map<String, dynamic>;
      var commonResponse = CommonResponse.fromJson(json: commonResponseJSON);
      if (commonResponse.statusCode == 200) {
        return AcademicLevel.fromJson(res["data"]);
      } else {
        final errorJSON = res["error"] as Map<String, dynamic>;
        return Future.error(AcademicLevelError.fromJsonMap(errorJSON));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  ///
  Future<bool> removeAcademicLevel(int academicLevelId) async {
    try {
      final res = await _dioClient
          .delete("${Endpoints.editAcademicLevel}/$academicLevelId");
      final commonResponseJSON = res["message"] as Map<String, dynamic>;
      var commonResponse = CommonResponse.fromJson(json: commonResponseJSON);
      if (commonResponse.statusCode == 200) {
        return true;
      } else {
        final errorJSON = res["error"] as Map<String, dynamic>;
        return Future.error(AcademicLevelError.fromJsonMap(errorJSON));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }
}
