import 'package:dio/dio.dart';
import 'package:tutorO/models/cerificate/certificate_model.dart';
import '../../../../models/common_response/common_response.dart';
import '../../constants/endpoints.dart';
import '../../dio_client.dart';

///
class CertificationAPI {
  // dio instance
  final DioClient _dioClient;

  ///
  CertificationAPI({required DioClient dioClient}) : _dioClient = dioClient;

  Future<List<CertificationModel>> fetchCertifications() async {
    try {
      final res = await _dioClient.get(Endpoints.getCertifications);
      var commonResponse =
          CommonResponse.fromJson(json: res["message"] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        final data = res["data"] as List;
        return data.map((json) => CertificationModel.fromJson(json)).toList();
      } else {
        return List.empty();
      }
    } on Exception catch (_) {
      return List.empty();
    }
  }

  Future<CertificationModel> addCertification(FormData data) async {
    try {
      final res =
          await _dioClient.post(Endpoints.addCertifications, data: data);
      var commonResponse =
          CommonResponse.fromJson(json: res["message"] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return CertificationModel.fromJson(res["data"]);
      } else {
        return Future.error(CertificationError.fromJsonMap(
            res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<CertificationModel> editCertification(FormData data, int id) async {
    try {
      final res = await _dioClient.put("${Endpoints.addCertifications}/$id",
          data: data);
      var commonResponse =
          CommonResponse.fromJson(json: res["message"] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return CertificationModel.fromJson(res["data"]);
      } else {
        return Future.error(CertificationError.fromJsonMap(
            res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<bool> deleteCertificate(int id) async {
    try {
      final res = await _dioClient.delete("${Endpoints.addCertifications}/$id");
      var commonResponse =
          CommonResponse.fromJson(json: res["message"] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return true;
      } else {
        return Future.error(CertificationError.fromJsonMap(
            res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }
}
