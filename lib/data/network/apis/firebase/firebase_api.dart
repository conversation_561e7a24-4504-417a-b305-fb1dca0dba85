import 'dart:async';

import 'package:firebase_core/firebase_core.dart';
import 'package:tutorO/constants/strings.dart';
import 'package:tutorO/firebase_options.dart';

// Name class as API to make its name consistent with other class in package
class FirebaseAPI {
  Future<bool> initFirebase() async {
    await Firebase.initializeApp(
        name: Strings.appName, options: DefaultFirebaseOptions.currentPlatform);
    return true;
  }
}
