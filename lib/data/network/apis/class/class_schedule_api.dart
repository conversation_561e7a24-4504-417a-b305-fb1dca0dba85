import 'package:dio/dio.dart';
import 'package:tutorO/constants/values.dart';
import 'package:tutorO/models/check_in/check_in.dart';
import 'package:tutorO/models/class/available_classes/available_classes.dart';
import 'package:tutorO/models/class/class.dart';
import 'package:tutorO/models/class_session/request_off_session/request_off_session.dart';
import 'package:tutorO/models/lesson_product/lesson_product.dart';
import 'package:tutorO/models/lesson_product/lesson_product_content.dart';
import 'package:tutorO/models/report_student/report_student.dart';
import '../../../../models/attendance/attendance.dart';
import '../../../../models/class_session/class_session.dart';
import '../../../../models/class_session/class_session_media_plan/class_session_media_plan.dart';
import '../../../../models/common_response/common_response.dart';
import '../../../../models/lesson_image/lesson_image.dart';
import '../../../../models/lesson_plan/lesson_plan.dart';
import '../../../../models/report/report.dart';
import '../../../../models/student/student.dart';
import '../../constants/endpoints.dart';
import '../../dio_client.dart';

///
class ClassAPI {
  final DioClient _dioClient;

  ///
  ClassAPI({required DioClient dioClient}) : _dioClient = dioClient;

  Future<AvailableClasses> fetchAvailableClasses(
      {Map<String, dynamic>? filter}) async {
    try {
      // var targetAPI = Uri.https("api-test.tutoro.vn", Endpoints.availableClasses, filter);
      // var decodedQueryParams = Uri.decodeQueryComponent(targetAPI.query);
      final res = await _dioClient.get(Endpoints.availableClasses,
          queryParameters: filter);
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return AvailableClasses.fromJson(res["data"]);
      } else {
        return Future.error(AvailableClassesError.fromJsonMap(
            res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<int> fetchCountClassToday() async {
    try {
      final res = await _dioClient.get(Endpoints.countClassToday);
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return res["data"]["count_session"] as int;
      } else {
        return 0;
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<Map<String, dynamic>> requestRegisterClass(int idClass) async {
    try {
      final res = await _dioClient.post("${Endpoints.registerClass}/$idClass");
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return res["data"];
      } else {
        return Future.error(ClassScheduleError.fromJsonMap(
            res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<Map<String, dynamic>> requestCancelRegisterClass(int idClass) async {
    try {
      final res =
          await _dioClient.put("${Endpoints.cancelRegisterClass}/$idClass");
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return res["data"];
      } else {
        return Future.error(ClassScheduleError.fromJsonMap(
            res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<ClassSessions> fetchClassByDay(Map<String, dynamic> query) async {
    try {
      final res =
          await _dioClient.get(Endpoints.classSessions, queryParameters: query);
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return ClassSessions.fromJson(res["data"]);
      } else {
        final err = ClassSessionsError.fromJsonMap(
            res["error"] as Map<String, dynamic>);
         
        return Future.error(err);
      }
    } on Exception catch (e) {
      print(e);
      return Future.error(e);
    }
  }

  Future<ClassSession> fetchDetailSession(int idSession) async {
    try {
      final res = await _dioClient.get(
          Endpoints.detailSession.replaceAll("{session_id}", "$idSession"));
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return ClassSession.fromJson(res["data"]);
      } else {
        return Future.error(
            ClassSessionError.fromJson(res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      print(e);
      return Future.error(e);
    }
  }

  Future<CheckInModel> fetchCheckinStatus(int idSession) async {
    try {
      final res = await _dioClient.get(
          Endpoints.checkinStatus.replaceAll("{session_id}", "$idSession"));
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return CheckInModel.fromJson(res["data"]);
      } else {
        return Future.error(
            CheckInModelError.fromJson(res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      print(e);
      return Future.error(e);
    }
  }

  Future<RequestOffSession> fetchRequestOffStatus(int idSession) async {
    try {
      final res = await _dioClient.get(Endpoints.cancelTeachingSession
          .replaceAll("{session_id}", "$idSession"));
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return RequestOffSession.fromJson(res["data"]);
      } else {
        return Future.error(
            CheckInModelError.fromJson(res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      print(e);
      return Future.error(e);
    }
  }

  Future<RequestOffSession> cancelPermissionForm(
      int idSession, Map<String, dynamic> data) async {
    try {
      final res = await _dioClient.delete(
          Endpoints.cancelTeachingSession
              .replaceAll("{session_id}", "$idSession"),
          queryParameters: data);
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return RequestOffSession.fromJson(res["data"]);
      } else {
        return Future.error(
            CheckInModelError.fromJson(res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      print(e);
      return Future.error(e);
    }
  }

  Future<Map<String, dynamic>> cancelTeachClass(
      int idClass, CancelClassActionType method) async {
    try {
      var endpoint =
          Endpoints.cancelTeaching.replaceAll("{idClass}", "$idClass");
      final res = method == CancelClassActionType.submitCancellationForm
          ? await _dioClient.post(endpoint)
          : await _dioClient.delete(endpoint);
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return res["data"];
      } else {
        return Future.error(ClassSessionsError.fromJsonMap(
            res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      print(e);
      return Future.error(e);
    }
  }

  Future<RequestOffSession> cancelTeachSession(
      int sessionId, Map<String, dynamic> data) async {
    try {
      final res = await _dioClient.post(
          Endpoints.cancelTeachingSession
              .replaceAll("{session_id}", "$sessionId"),
          queryParameters: data);
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return RequestOffSession.fromJson(res["data"]);
      } else {
        return Future.error(ClassSessionsError.fromJsonMap(
            res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      print(e);
      return Future.error(e);
    }
  }

  Future<CheckInModel> checkInLesson(int sessionId) async {
    try {
      final res = await _dioClient.post(
          Endpoints.checkInClass.replaceAll("{session_id}", "$sessionId"));
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return CheckInModel.fromJson(res["data"]);
      } else {
        return Future.error(
            CheckInModelError.fromJson(res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      print(e);
      return Future.error(e);
    }
  }

  Future<Report> fetchReport(String endpoint) async {
    try {
      final res = await _dioClient.get(endpoint);
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return Report.fromJson(res["data"]);
      } else {
        return Future.error(
            ReportError.fromJsonMap(res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      print(e);
      return Future.error(e);
    }
  }

  Future<AvailableClasses> fetchClassTeaching() async {
    try {
      final res = await _dioClient.get(Endpoints.classTeaching);
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return AvailableClasses.fromJson(res["data"]);
      } else {
        return Future.error(AvailableClassesError.fromJsonMap(
            res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      print(e);
      return Future.error(e);
    }
  }

  Future<List<Student>> fetchStudents(int idClass) async {
    try {
      final res = await _dioClient
          .get(Endpoints.studentsInClass.replaceAll("{idClass}", "$idClass"));
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return (res["data"] as List)
            .map((e) => Student.fromJsonMap(e as Map<String, dynamic>))
            .toList();
      } else {
        return Future.error(
            StudentError.fromJsonMap(res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<ListAttendance> fetchAttendances(
      int sessionId, String languageCode) async {
    try {
      var endpoint = Endpoints.mapArgs(Endpoints.getAttendances,
          {"session_id": "$sessionId", "language_code": "$languageCode"});
      final res = await _dioClient.get(endpoint);
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return ListAttendance.fromJson(res["data"]);
      } else {
        return Future.error(
            AttendanceError.fromJsonMap(res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<StudentAttendance> attendanceStudent(
      {required int sessionId,
      required int studentId,
      required bool status}) async {
    try {
      var endpoint = Endpoints.mapArgs(Endpoints.doAttention,
          {"session_id": "$sessionId", "student_id": "$studentId"});
      final res = await _dioClient
          .post('$endpoint?attending_types=${status ? "YES" : "NO"}');
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return StudentAttendance.fromJson(res["data"]);
      } else {
        return Future.error(
            AttendanceError.fromJsonMap(res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<LessonProducts> fetchLessonProducts(int sessionId) async {
    try {
      final res = await _dioClient.get(
          Endpoints.getLessonProducts.replaceAll("{session_id}", "$sessionId"));
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return LessonProducts.fromJson(res["data"]);
      } else {
        return Future.error(LessonProductsError.fromJsonMap(
            res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<ProductContent> fetchDetailProduct(
      int sessionId, int studentId) async {
    try {
      final res = await _dioClient.get(Endpoints.mapArgs(
          Endpoints.getDetailProduct,
          {"session_id": "$sessionId", "student_id": "$studentId"}));
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return ProductContent.fromJson(res["data"]);
      } else {
        return Future.error(ProductContentError.fromJsonMap(
            res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<Product> createDetailProduct(FormData data, int sessionId) async {
    try {
      final res = await _dioClient.post(
          Endpoints.createLessonProduct
              .replaceAll("{session_id}", "$sessionId"),
          data: data);
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return Product.fromJson(res["data"]);
      } else {
        return Future.error(LessonProductsError.fromJsonMap(
            res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      print(e);
      return Future.error(e);
    }
  }

  Future<Product> updateDetailProduct(FormData data, int sessionId) async {
    try {
      final res = await _dioClient.put(
          Endpoints.updateLessonProduct
              .replaceAll("{session_id}", "$sessionId"),
          data: data);
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return Product.fromJson(res["data"]);
      } else {
        return Future.error(LessonProductsError.fromJsonMap(
            res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      print(e);
      return Future.error(e);
    }
  }

  Future<List<LessonPlan>> fetchLessonPlan(int sessionId) async {
    try {
      final res = await _dioClient.get(
          Endpoints.getLessonPlans.replaceAll("{session_id}", "$sessionId"));
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return (res["data"] as List)
            .map((e) => LessonPlan.fromJson(e))
            .toList();
      } else {
        return Future.error(
            LessonPlanError.fromJsonMap(res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<ReportStudent> fetchReportStudent(int classId, int studentId) async {
    try {
      final res = await _dioClient.get(Endpoints.mapArgs(
          Endpoints.fetchReportStudentInLesson,
          {"class_id": "$classId", "student_id": "$studentId"}));
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return ReportStudent.fromJson(res["data"]);
      } else {
        return Future.error(ReportStudentError.fromJsonMap(
            res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<bool> reportLessonPlan(int lessonPlanId) async {
    try {
      final res = await _dioClient.post(Endpoints.reportLessonPlan
          .replaceAll("{lesson_plan_id}", "$lessonPlanId"));
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return true;
      } else {
        return Future.error(
            LessonPlanError.fromJsonMap(res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      print(e);
      return Future.error(e);
    }
  }

  Future<bool> sendLessonPlanToMail(int lessonPlanId) async {
    try {
      final res = await _dioClient.post(Endpoints.sendLessonPlanToEmail
          .replaceAll("{lesson_plan_id}", "$lessonPlanId"));
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return true;
      } else {
        return Future.error(
            LessonPlanError.fromJsonMap(res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<List<LessonImage>> fetchListLessonAndVideo(int sessionId) async {
    try {
      final res = await _dioClient.get(Endpoints.getListImagesAndVideos
          .replaceAll("{session_id}", "$sessionId"));
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return (res["data"] as List)
            .map((e) => LessonImage.fromJson(e))
            .toList();
      } else {
        return Future.error(
            LessonImageError.fromJsonMap(res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      print(e);
      return Future.error(e);
    }
  }

  Future<bool> removeImageOrVideo(
      int sessionId, Map<String, dynamic> data) async {
    try {
      final res = await _dioClient.delete(
          Endpoints.removeImagesOrVideos
              .replaceAll("{session_id}", "$sessionId"),
          data: data);
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return true;
      } else {
        return Future.error(
            LessonImageError.fromJsonMap(res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      print(e);
      return Future.error(e);
    }
  }

  Future<List<LessonImage>> uploadImageOrVideo(
      FormData data, int sessionId) async {
    try {
      final res = await _dioClient.post(
          Endpoints.uploadLessonImageOrVideo
              .replaceAll("{session_id}", "$sessionId"),
          data: data);
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return (res["data"] as List)
            .map((e) => LessonImage.fromJson(e))
            .toList();
      } else {
        return Future.error(
            LessonImageError.fromJsonMap(res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<Map<String, dynamic>> fetchCancelClassStatus(int classId) async {
    try {
      final res = await _dioClient.get(Endpoints.fetchCancelClassStatus
          .replaceAll("{class_id}", "$classId"));
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return res["data"];
      } else {
        return Future.error("Unknown error");
      }
    } on Exception catch (e) {
      print(e);
      return Future.error(e);
    }
  }

  Future<bool> deleteDetailProduct(
      int sessionId, Map<String, dynamic> data) async {
    try {
      final res = await _dioClient.delete(
          Endpoints.deleteLessonProduct
              .replaceAll("{session_id}", "$sessionId"),
          data: data);
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return true;
      } else {
        return Future.error(LessonProductsError.fromJsonMap(
            res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<ListSessionMedialPlanResponse> fetchSessionMediaPlan(int session_id) {
    {
      return _dioClient
          .get(
              Endpoints.listMediaPlan.replaceAll("{session_id}", "$session_id"))
          .then((dynamic res) {
        return ListSessionMedialPlanResponse.fromJson(res);
      });
    }

    // Future<List<BorrowMaterial>> borrowMaterial() async {
    //   try {
    //     final res = await _dioClient.get(Endpoints.borrowMaterial);
    //     final commonResponse =
    //         CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
    //     if (commonResponse.statusCode == 200) {
    //       return (res['data'] as List)
    //           .map((e) => BorrowMaterial.fromJson(e))
    //           .toList();
    //     } else {
    //       return Future.error(BorrowMaterialDataError.fromJsonMap(
    //           res["error"] as Map<String, dynamic>));
    //     }
    //   } on Exception catch (e) {
    //     return Future.error(e);
    //   }
    // }

    // Future<BorrowMaterial> borrowMaterialRequest(int sessionId) async {
    //   try {
    //     // final response = await rootBundle.loadString('assets/test.json');
    //     // final res = json.decode(response);
    //     final res = await _dioClient.get(Endpoints.borrowMaterialRequest
    //         .replaceAll("{session_id}", "$sessionId"));
    //     final commonResponse =
    //         CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
    //     if (commonResponse.statusCode == 200) {
    //       // print(res['data']);
    //       final response = BorrowMaterial.fromJson(res['data']);
    //       print(response);
    //       print('hahahaha');
    //       return response;
    //     } else {
    //       return Future.error(BorrowMaterialDataError.fromJsonMap(
    //           res["error"] as Map<String, dynamic>));
    //     }
    //   } on Exception catch (e) {
    //     print(e);
    //     return Future.error(e);
    //   }
    // }

    // Future<ListMaterialsResponse?> getListMaterials() async {
    //   // final response =
    //   //     await rootBundle.loadString('assets/testlistmaterial.json');
    //   // final res = json.decode(response);
    //   try {
    //     final res = await _dioClient.get(Endpoints.listMaterial);
    //     final commonResponse =
    //         CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
    //     if (commonResponse.statusCode == 200) {
    //       return ListMaterialsResponse.fromJson(res);
    //     }
    //   } on Exception catch (e) {
    //     return Future.error(e);
    //   }
    // }

    // Future<String> sendRequestMaterial(
    //     List<MaterialItem> materials, String requestNote, int sessionId) async {
    //   try {
    //     // final response = await rootBundle.loadString('assets/test.json');
    //     // final res = json.decode(response);
    //     final res = await _dioClient.post(
    //         Endpoints.sendborrowMaterialRequest
    //             .replaceAll("{session_id}", "$sessionId"),
    //         data: {
    //           "devices": materials.map((e) => e.toJson()).toList(),
    //           "note": requestNote
    //         });

    //     final commonResponse =
    //         CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
    //     if (commonResponse.statusCode == 200) {
    //       return commonResponse.message;
    //     } else if (commonResponse.statusCode == 201) {
    //       throw commonResponse.message;
    //     }
    //     {
    //       throw Exception();
    //     }
    //   } on Exception catch (e) {
    //     print(e);
    //     return Future.error(e);
    //   }
    // }
  }

    // Portfolio
  static const String getStudentPortfolioUuidUrl = 'https://student-portfolio.teky.vn/student-portfolios/?student_id={student_id}&session_id={session_id}';
  static const String checkValidStudentPortfolioUrl = 'https://student-portfolio.teky.vn/student-portfolios/{student_portfolio_uuid}/check_valid';

  Future<String> studentPortfolioUuid(int sessionId, int studentId) async {
    try {
      final res = await _dioClient.get(getStudentPortfolioUuidUrl
          .replaceAll("{session_id}", "$sessionId")
          .replaceAll("{student_id}", "$studentId"));
        return res ["data"]["student_portfolio_uuid"] as String;
    }catch (e) {
      print(e);
      return "";
    }
  }

  Future<bool> PortfolioState(String studentPortfolioUuid) async {
    try {
      final res = await _dioClient.get(checkValidStudentPortfolioUrl
          .replaceAll("{student_portfolio_uuid}", studentPortfolioUuid));

      if (res["data"]["message"] == "invalid") {
        return false;
      } else {
        return true;
      }
    }catch (e) {
      print(e);
      return false;
    }
  }
}
