import 'package:tutorO/data/network/constants/endpoints.dart';
import 'package:tutorO/data/network/dio_client.dart';
import 'package:tutorO/models/class_session_evaluation/class_session_evaluation.dart';
import 'package:tutorO/models/common_response/common_response.dart';
import 'package:tutorO/models/error_common/error_common.dart';
import 'package:tutorO/models/evaluation_info/evaluation_info_model.dart';
import 'package:tutorO/models/evaluation_template/evaluation_template_model.dart';

class ClassEvaluationAPI {
  final DioClient _dioClient;

  ClassEvaluationAPI({required DioClient dioClient}) : _dioClient = dioClient;

  Future<ClassSessionEvaluation> fetchSessionEvaluations(int sessionId) async {
    try {
      final res = await _dioClient.get(Endpoints.getClassSessionEvaluations
          .replaceAll("{session_id}", "$sessionId"));
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return ClassSessionEvaluation.fromJson(res["data"]);
      } else {
        return Future.error(UnspecifiedError.fromJsonMap(
            errInfo: res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<List<EvaluationTemplateModel>> fetchEvaluationTemplates(
      int sessionId) async {
    try {
      final res = await _dioClient.get(Endpoints.getEvaluationTemplates
          .replaceAll("{session_id}", "$sessionId"));
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        final data = res["data"] as List;
        return data
            .map((item) => EvaluationTemplateModel.fromJson(item))
            .toList();
      } else {
        return Future.error(UnspecifiedError.fromJsonMap(
            errInfo: res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<bool> submitEvaluation(
      {required int sessionId,
      required int studentId,
      required EvaluationTemplateModel evaluation}) async {
    try {
      final res = await _dioClient.post(
          Endpoints.sendStudentEvaluationForClassSession
              .replaceAll("{session_id}", "$sessionId")
              .replaceAll("{student_id}", "$studentId"),
          data: evaluation.toJson());
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return true;
      } else {
        return Future.error(EvaluationTemplateErrModel.fromJsonMap(
            res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future fetchEvaluationInfo(
      {required int sessionId, required int studentId}) async {
    try {
      final res = await _dioClient.get(Endpoints.getEvaluationInfo
          .replaceAll("{session_id}", "$sessionId")
          .replaceAll("{student_id}", "$studentId"));
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return EvaluationInfoModel.fromJson(res["data"]);
      } else {
        return Future.error(UnspecifiedError.fromJsonMap(
            errInfo: res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }
}
