import 'package:tutorO/data/network/constants/endpoints.dart';
import 'package:tutorO/data/network/dio_client.dart';
import 'package:tutorO/models/common_response/common_response.dart';
import 'package:tutorO/models/user/authen_model.dart';

class AuthenApi {
  final DioClient _dioClient;

  AuthenApi(this._dioClient);
  Future<AuthenticationModel?> login(String phoneNumber, String password,
      String deviceId, String? fcmToken) async {
    final data = {
      "mobile_number": phoneNumber,
      "password": password,
      "device_id": deviceId,
      "firebase_token": fcmToken
    };
    try {
      final res = await _dioClient.post(Endpoints.login, data: data);
      final commonResponseJSON = res["message"] as Map<String, dynamic>;
      final commonResponse = CommonResponse.fromJson(json: commonResponseJSON);
      if (commonResponse.statusCode == 200) {
        final dataJSON = res["data"] as Map<String, dynamic>;
        return AuthenticationModel.fromJsonMap(dataJSON);
      } else {
        final errorJSON = res["error"] as Map<String, dynamic>;
        final serverResError = AuthenticationErrorModel.fromJsonMap(errorJSON);
        return Future.error(serverResError);
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  ///
  Future<String> forgot(String email) async {
    try {
      final res = await _dioClient
          .post(Endpoints.forgotPassword, data: {"email": email});
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return res['message']['text'];
      } else if (commonResponse.statusCode >= 400) {
        // final serverResError = ForgotPasswordErrorModel.fromJsonMap(
        //     res['error'] as Map<String, dynamic>);
        throw res['error']["email"] != null
            ? res['error']["email"]
            : res['error'];
      }
      return res['message']['text'];
    } catch (e) {
      rethrow;
    }
  }
}
