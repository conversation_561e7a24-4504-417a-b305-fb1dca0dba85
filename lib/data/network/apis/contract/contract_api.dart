import 'package:tutorO/utils/my_logger/my_logger.dart';

import '../../../../models/common_response/common_response.dart';
import '../../../../models/profile/contract_model.dart';

import '../../constants/endpoints.dart';
import '../../dio_client.dart';

/// API relates to contract
class ContractAPI {
  final DioClient _dioClient;

  /// constructor
  ContractAPI({required DioClient dioClient}) : _dioClient = dioClient;

  Future<List<ContractModel>> fetchContracts() async {
    try {
      final res = await _dioClient.get(Endpoints.getContracts);
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        final data = res["data"] as List;
        return data.map((json) => ContractModel.fromJson(json)).toList();
      } else {
        return List.empty();
      }
    } on Exception catch (e) {
      MyLogger.getLoggerInstance().e(e.toString());
      return List.empty();
    }
  }
}
