import '../../constants/endpoints.dart';
import '../../dio_client.dart';

///
class GlobalAppAPI {
  // dio instance
  final DioClient _dioClient;

  /// Constructor
  GlobalAppAPI(this._dioClient);

  /// Check have app version update
  // Future<AppVersionUpdate> checkVersionUpdate() async {
  //   var version = '1.0.0';
  //   var osCode = Platform.isIOS ? 'ios' : 'android';
  //   try {
  //     final resvice_version}", "$deviceVersion"))
  //         .then((dynamic value = await _dioClient.post(Endpoints.checkUpdateVersion,
  //         data: {"version": version, "os": osCode});
  //     return AppVersionUpdate.fromMap(res["data"] as Map<String, dynamic>);
  //   } on Exception catch (e) {
  //     print(e.toString());
  //     return Future.error(e);
  //   }
  // }
  // Future<CheckUpdateResponse?> checkVersionApp(String deviceVersion) async {
  //   try {
  //     final res = await _dioClient
  //         .get(Endpoints.checkVersion
  //             .replaceAll("{de) {
  //       return CheckUpdateResponse.fromJson(value);
  //     });
  //   } on Exception catch (e) {
  //     print(e.toString());
  //     return Future.error(e);
  //   }
  // }

  /// Change app language
  Future<bool> changeLanguage(String languageCode) async {
    try {
      final _ = await _dioClient
          .put(Endpoints.changeLanguage, data: {"language_code": languageCode});
      return true;
    } on Exception catch (e) {
      print(e.toString());
      return Future.error(e);
    }
  }
}
