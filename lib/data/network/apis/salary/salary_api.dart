import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/services.dart';
import 'package:tutorO/data/network/constants/endpoints.dart';
import 'package:tutorO/data/network/dio_client.dart';
import 'package:tutorO/models/common_response/common_response.dart';
import 'package:tutorO/models/salary/salary_model.dart';

class SalaryAPI {
  final DioClient _dioClient;

  SalaryAPI({required DioClient dioClient}) : _dioClient = dioClient;
  Future<DataAttendanceInfo> getSalaryInfo(
      String startSearchTime,
      String endSearchTime,
      int pageNumber,
      String? attendanceState,
      String? checkInState) async {
    var data = {
      "checkin_state": checkInState ?? null,
      "attendance_state": attendanceState ?? null,
      "start_search_time": startSearchTime,
      "end_search_time": endSearchTime,
      "per_page": 10,
      "page": pageNumber
    };
    final dio = Dio();
    try {
      final response = await _dioClient.post(Endpoints.salary, data: data);
      final commonResponse = CommonResponse.fromJson(
          json: response["message"] as Map<String, dynamic>);
      print(response["data"]);
      if (commonResponse.statusCode == 200) {
        return DataAttendanceInfo.fromJson(response["data"]);
      } else {
        return Future.error(response["error"]);
      }
    } catch (e) {
      return Future.error(e);
    }
  }
}
