import 'package:tutorO/data/network/constants/endpoints.dart';
import 'package:tutorO/data/network/dio_client.dart';
import 'package:tutorO/models/common_response/common_response.dart';
import 'package:tutorO/models/error_common/error_common.dart';
import 'package:tutorO/models/notification/notification_list_model.dart';
import 'package:tutorO/utils/utils.dart';

class NotificationAPI {
  // dio instance
  final DioClient _dioClient;

  /// Constructor
  NotificationAPI({required DioClient dioClient}) : _dioClient = dioClient;

  Future<NotificationListModel> fetchNotifications(
      Map<String, dynamic>? nextCursor) async {
    try {
      final res = await _dioClient.get(Endpoints.getNotifications,
          queryParameters: nextCursor);
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        final data = res["data"] as Map<String, dynamic>;
        return NotificationListModel.fromJson(data);
      } else {
        return Future.error(UnspecifiedError.fromJsonMap(
            errInfo: res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<bool> deleteNotification(int notificationId) async {
    try {
      final res = await _dioClient.delete(Endpoints.deleteNotification
          .replaceAll("{notification_id}", "$notificationId"));
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (Utils.isSuccessHttpStatus(commonResponse.statusCode)) {
        return true;
      } else {
        return Future.error(UnspecifiedError.fromJsonMap(
            errInfo: res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<bool> markNotificationsAsRead(List<int> notificationIds) async {
    try {
      final res = await _dioClient.put(Endpoints.markNotificationsAsRead,
          data: {'notification_ids': notificationIds});
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (Utils.isSuccessHttpStatus(commonResponse.statusCode)) {
        return true;
      } else {
        return Future.error(UnspecifiedError.fromJsonMap(
            errInfo: res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }
}
