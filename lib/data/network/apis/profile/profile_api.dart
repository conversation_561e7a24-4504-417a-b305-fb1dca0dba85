import 'package:dio/dio.dart';
import 'package:tutorO/models/otp/otp_model.dart';
import 'package:tutorO/models/recommendation/recommendation.dart';
import '../../../../models/common_response/common_response.dart';
import '../../../../models/profile/email.dart';
import '../../../../models/profile/image_self.dart';
import '../../../../models/profile/job_info.dart';
import '../../../../models/profile/personal_info_model.dart';
import '../../constants/endpoints.dart';
import '../../dio_client.dart';

class ProfileAPI {
  // dio instance
  final DioClient _dioClient;

  // injecting dio instance
  ProfileAPI(this._dioClient);

  Future<JobInfoModel> fetchJobInfoModel() async {
    try {
      final res = await _dioClient.get(Endpoints.jobInfo);
      var commonResponse = CommonResponse.fromJson(json: res["message"]);
      if (commonResponse.statusCode == 200) {
        return JobInfoModel.fromJsonMap(res["data"]);
      } else {
        return Future.error(JobInfoModel.fromJsonMap(res['error']));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<PersonalInfoModel> fetchPersonalInfo() async {
    try {
      final res = await _dioClient.get(Endpoints.personalInfo);
      var commonResponse = CommonResponse.fromJson(json: res["message"]);
      if (commonResponse.statusCode == 200) {
        return PersonalInfoModel.fromJsonMap(res["data"]);
      } else {
        return Future.error(
            PersonalInfoModelErrorModel.fromJsonMap(res['error']));
      }
    } on Exception catch (e) {
      print(e);
      return Future.error(e);
    }
  }

  Future<PersonalInfoModel> uploadImage(FormData formData) async {
    try {
      final res = await _dioClient.put(Endpoints.personalInfo, data: formData);
      var commonResponse = CommonResponse.fromJson(json: res["message"]);
      if (commonResponse.statusCode == 200) {
        return PersonalInfoModel.fromJsonMap(res["data"]);
      } else {
        return Future.error(
            PersonalInfoModelErrorModel.fromJsonMap(res['error']));
      }
    } on Exception catch (e) {
      print(e.toString());
      return Future.error(e);
    }
  }

  Future<String> getOtpUpdatePhone(String phone, String token) async {
    try {
      final res = await _dioClient.put(Endpoints.otpUpdatePhone,
          data: {"mobile_number": phone, "recaptcha": token});
      var commonResponse = CommonResponse.fromJson(json: res["message"]);
      if (commonResponse.statusCode == 200) {
        return res["data"]["mobile_number"];
      } else {
        return Future.error(OTPErrorModel.fromJsonMap(res["error"]));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<bool> setEmail(String email) async {
    try {
      final res =
          await _dioClient.put(Endpoints.setEmail, data: {"email": email});
      final commonResponse = CommonResponse.fromJson(json: res['message']);
      if (commonResponse.statusCode == 200) {
        return true;
      } else {
        return Future.error(EmailErrorModel.fromJsonMap(res['error']));
      }
    } on Exception catch (e) {
      print(e.toString());
      return Future.error(e);
    }
  }

  Future<PersonalInfoModel> updateAllInfo(Map<String, dynamic> value) async {
    try {
      final res = await _dioClient.put(Endpoints.personalInfo, data: value);
      final commonResponse = CommonResponse.fromJson(json: res['message']);
      if (commonResponse.statusCode == 200) {
        return PersonalInfoModel.fromJsonMap(res["data"]);
      } else {
        return Future.error(
            PersonalInfoModelErrorModel.fromJsonMap(res['error']));
      }
    } on Exception catch (e) {
      print(e.toString());
      return Future.error(e);
    }
  }

  Future<PersonalInfoModel> updateAnInfo(Map<String, dynamic> value) async {
    try {
      final res = await _dioClient.patch(Endpoints.personalInfo, data: value);
      final commonResponse = CommonResponse.fromJson(json: res['message']);
      if (commonResponse.statusCode == 200) {
        return PersonalInfoModel.fromJsonMap(res["data"]);
      } else {
        return Future.error(
            PersonalInfoModelErrorModel.fromJsonMap(res['error']));
      }
    } on Exception catch (e) {
      print(e.toString());
      return Future.error(e);
    }
  }

  Future<List<ImageSelf>> fetchImagesSelf() async {
    try {
      final res = await _dioClient.get(Endpoints.imagesSelf);
      var commonResponse = CommonResponse.fromJson(json: res["message"]);
      if (commonResponse.statusCode == 200) {
        final data = res["data"] as List;
        return data.map((item) => ImageSelf.fromJsonMap(item)).toList();
      } else {
        return Future.error(ImageSelf.fromJsonMap(res['error']));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<List<ImageSelf>> uploadImages(FormData data) async {
    try {
      final res = await _dioClient.post(Endpoints.imagesSelf, data: data);
      var commonResponse = CommonResponse.fromJson(json: res["message"]);
      if (commonResponse.statusCode == 201) {
        final data = res["data"] as List;
        return data.map((item) => ImageSelf.fromJsonMap(item)).toList();
      } else {
        return Future.error(ImageSelf.fromJsonMap(res['error']));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<bool> removeImage(int id) async {
    try {
      final res = await _dioClient.delete("${Endpoints.imagesSelf}/$id");
      var commonResponse = CommonResponse.fromJson(json: res["message"]);
      if (commonResponse.statusCode == 200) {
        return true;
      } else {
        return Future.error(ImageSelf.fromJsonMap(res['error']));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<bool> cancelUpdateProfile() async {
    try {
      final res = await _dioClient.delete("${Endpoints.cancelUpdateProfile}");
      var commonResponse = CommonResponse.fromJson(json: res["message"]);
      if (commonResponse.statusCode == 200) {
        return true;
      } else {
        return Future.error(
            PersonalInfoModelErrorModel.fromJsonMap(res['error']));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<bool> submitProfile() async {
    try {
      final res = await _dioClient.post("${Endpoints.submitProfile}");
      var commonResponse = CommonResponse.fromJson(json: res["message"]);
      if (commonResponse.statusCode == 200) {
        return true;
      } else {
        return Future.error(
            PersonalInfoModelErrorModel.fromJsonMap(res['error']));
      }
    } on Exception catch (e) {
      print(e);
      return Future.error(e);
    }
  }

  Future<Recommendations> fetchRecommendationsList() async {
    try {
      final res = await _dioClient.get(Endpoints.recommendationList);
      var commonResponse = CommonResponse.fromJson(json: res["message"]);
      if (commonResponse.statusCode == 200) {
        return Recommendations.fromJson(res["data"]);
      } else {
        return Future.error(RecommendationsError.fromJsonMap(res['error']));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }
}
