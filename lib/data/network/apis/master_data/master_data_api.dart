import 'dart:async';

import 'package:tutorO/models/academic_title/academic_title.dart';
import 'package:tutorO/models/common_response/common_response.dart';
import 'package:tutorO/models/grade/grade.dart';
import 'package:tutorO/models/master_data/country.dart';
import 'package:tutorO/models/master_data/district.dart';
import 'package:tutorO/models/master_data/province.dart';
import 'package:tutorO/models/master_data/ward.dart';
import 'package:tutorO/models/reason_off/reason_off.dart';
import 'package:tutorO/models/subject/subject.dart';
import 'package:tutorO/models/training_unit/training_unit.dart';

import '../../constants/endpoints.dart';
import '../../dio_client.dart';

///
class MasterDataApi {
  // dio instance
  final DioClient _dioClient;

  /// Constructor
  MasterDataApi(this._dioClient);

  Future<List<Country>> fetchCountries() async {
    try {
      final res = await _dioClient.get(Endpoints.countries);
      var commonResponse =
          CommonResponse.fromJson(json: res["message"] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        final data = res["data"] as List;
        return data
            .map((json) => Country.fromJsonMap(json as Map<String, dynamic>))
            .toList();
      } else {
        return Future.error(
            CountryError.fromJsonMap(res['error'] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<List<District>> fetchDistrict({Map<String, dynamic>? param}) async {
    try {
      final res =
          await _dioClient.get(Endpoints.districts, queryParameters: param);
      var commonResponse =
          CommonResponse.fromJson(json: res["message"] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        final data = res["data"] as List;
        return data
            .map((json) => District.fromJsonMap(json as Map<String, dynamic>))
            .toList();
      } else {
        return Future.error(
            DistrictError.fromJsonMap(res['error'] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<List<Province>> fetchProvince({Map<String, dynamic>? param}) async {
    try {
      final res =
          await _dioClient.get(Endpoints.provinces, queryParameters: param);
      var commonResponse =
          CommonResponse.fromJson(json: res["message"] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        final data = res["data"] as List;
        return data
            .map((json) => Province.fromJsonMap(json as Map<String, dynamic>))
            .toList();
      } else {
        return Future.error(
            ProvinceError.fromJsonMap(res['error'] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<List<Ward>> fetchWard({Map<String, dynamic>? param}) async {
    try {
      final res = await _dioClient.get(Endpoints.wards, queryParameters: param);
      var commonResponse =
          CommonResponse.fromJson(json: res["message"] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        final data = res["data"] as List;
        return data
            .map((json) => Ward.fromJsonMap(json as Map<String, dynamic>))
            .toList();
      } else {
        return Future.error(
            WardError.fromJsonMap(res['error'] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  Future<List<TrainingUnit>> fetchTrainingUnits() async {
    try {
      final res = await _dioClient.get(Endpoints.trainingUnits);
      var commonResponse =
          CommonResponse.fromJson(json: res["message"] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        final data = res["data"] as List;
        return data.map((json) => TrainingUnit.fromJson(json)).toList();
      } else {
        return List.empty();
      }
    } on Exception catch (_) {
      return List.empty();
    }
  }

  Future<List<AcademicTitle>> fetchAcademicTitles() async {
    try {
      final res = await _dioClient.get(Endpoints.academicTitles);
      var commonResponse =
          CommonResponse.fromJson(json: res["message"] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        final data = res["data"] as List;
        return data.map((json) => AcademicTitle.fromJson(json)).toList();
      } else {
        return List.empty();
      }
    } on Exception catch (_) {
      return List.empty();
    }
  }

  Future<String> fetchTermAndRules(String languageCode) async {
    try {
      final res = await _dioClient.get(
          Endpoints.termAndRules.replaceAll("{languageCode}", languageCode));
      var commonResponse =
          CommonResponse.fromJson(json: res["message"] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return res["data"]["description"] as String;
      } else {
        return "";
      }
    } on Exception catch (_) {
      return "";
    }
  }

  Future<String> fetchRequestOffReason() async {
    try {
      final res = await _dioClient.get(Endpoints.requestOffReason);
      var commonResponse =
          CommonResponse.fromJson(json: res["message"] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return res["data"] as String;
      } else {
        return "";
      }
    } on Exception catch (_) {
      return "";
    }
  }

  Future<List<Subject>> fetchSubjects(String languageCode) async {
    try {
      final res = await _dioClient
          .get(Endpoints.subjects.replaceAll("{languageCode}", languageCode));
      var commonResponse =
          CommonResponse.fromJson(json: res["message"] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        final data = res["data"] as List;
        return data.map((json) => Subject.fromJson(json)).toList();
      } else {
        return List.empty();
      }
    } on Exception catch (_) {
      return List.empty();
    }
  }

  Future<List<Grade>> fetchGrades(String languageCode) async {
    try {
      final res = await _dioClient
          .get(Endpoints.grades.replaceAll("{languageCode}", languageCode));
      var commonResponse =
          CommonResponse.fromJson(json: res["message"] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        final data = res["data"] as List;
        return data.map((json) => Grade.fromJson(json)).toList();
      } else {
        return List.empty();
      }
    } on Exception catch (_) {
      return List.empty();
    }
  }

  Future<List<ReasonOff>> fetchReasonOffLesson() async {
    try {
      final res = await _dioClient.get(Endpoints.reasonOff);
      final commonResponse =
          CommonResponse.fromJson(json: res['message'] as Map<String, dynamic>);
      if (commonResponse.statusCode == 200) {
        return (res["data"] as List).map((e) => ReasonOff.fromJson(e)).toList();
      } else {
        return Future.error(
            ReasonOffError.fromJsonMap(res["error"] as Map<String, dynamic>));
      }
    } on Exception catch (e) {
      print(e);
      return Future.error(e);
    }
  }
}
