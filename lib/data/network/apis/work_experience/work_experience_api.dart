import 'package:dio/dio.dart';
import 'package:tutorO/models/profile/professional_experience.dart';
import 'package:tutorO/utils/utils.dart';
import '../../../../models/common_response/common_response.dart';
import '../../constants/endpoints.dart';
import '../../dio_client.dart';

/// APIs for CRUD academic level
class WorkExpApi {
  // dio instance
  final DioClient _dioClient;

  ///
  WorkExpApi({required DioClient dioClient}) : _dioClient = dioClient;

  Future<List<WorkExp>> fetchProfessionalEx() async {
    try {
      final res = await _dioClient.get(Endpoints.professionalExp);
      var commonResponse = CommonResponse.fromJson(json: res["message"]);
      if (commonResponse.statusCode == 200) {
        final data = res["data"] as List;
        return data.map((item) => WorkExp.fromJsonMap(item)).toList();
      } else {
        return Future.error(WorkExpError.fromJsonMap(res['error']));
      }
    } on Exception catch (e) {
      print(e.toString());
      return Future.error(e);
    }
  }

  Future<WorkExp> addProfessionalExperience(FormData data) async {
    try {
      final res = await _dioClient.post(Endpoints.professionalExp, data: data);
      var commonResponse = CommonResponse.fromJson(json: res["message"]);
      if (commonResponse.statusCode == 200) {
        return WorkExp.fromJsonMap(res["data"]);
      } else {
        return Future.error(WorkExpError.fromJsonMap(res['error']));
      }
    } on Exception catch (e) {
      print(e.toString());
      return Future.error(e);
    }
  }

  Future<WorkExp> updateProfessionalExperience(FormData data, int id) async {
    try {
      final res =
          await _dioClient.put("${Endpoints.professionalExp}/$id", data: data);
      var commonResponse = CommonResponse.fromJson(json: res["message"]);
      if (Utils.isSuccessHttpStatus(commonResponse.statusCode)) {
        return WorkExp.fromJsonMap(res["data"]);
      } else {
        return Future.error(WorkExpError.fromJsonMap(res['error']));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }

  ///
  Future<bool> removeProfessionalExperience(int id) async {
    try {
      final res = await _dioClient.delete("${Endpoints.professionalExp}/$id");
      final commonResponseJSON = res["message"] as Map<String, dynamic>;
      var commonResponse = CommonResponse.fromJson(json: commonResponseJSON);
      if (Utils.isSuccessHttpStatus(commonResponse.statusCode)) {
        return true;
      } else {
        final errorJSON = res["error"] as Map<String, dynamic>;
        return Future.error(WorkExpError.fromJsonMap(errorJSON));
      }
    } on Exception catch (e) {
      return Future.error(e);
    }
  }
}
