import 'package:dio/dio.dart';

// Authen Handling
class AuthenApiError extends DioError {
  final DioError dioError;

  AuthenApiError({
    required this.dioError,
  }) : super(
          requestOptions: dioError.requestOptions,
          response: dioError.response,
          error: dioError.error,
          type: DioErrorType.response,
        );
}

class ConnectTimeoutError extends DioError {
  final DioError dioError;

  ConnectTimeoutError({
    required this.dioError,
  }) : super(
          requestOptions: dioError.requestOptions,
          response: dioError.response,
          error: dioError.error,
          type: DioErrorType.connectTimeout,
        );
}

class ReceiveTimeOut extends DioError {
  final DioError dioError;

  ReceiveTimeOut({
    required this.dioError,
  }) : super(
          requestOptions: dioError.requestOptions,
          response: dioError.response,
          error: dioError.error,
          type: DioErrorType.receiveTimeout,
        );
}

class SendTimeOut extends DioError {
  final DioError dioError;

  SendTimeOut({
    required this.dioError,
  }) : super(
          requestOptions: dioError.requestOptions,
          response: dioError.response,
          error: dioError.error,
          type: DioErrorType.sendTimeout,
        );
}

// Bad Network Handling
class BadNetworkApiError extends DioError {
  final DioError dioError;

  BadNetworkApiError({
    required this.dioError,
  }) : super(
          requestOptions: dioError.requestOptions,
          response: dioError.response,
          error: 'Kết nối không ổn định, vui lòng thử lại sau',
          type: DioErrorType.connectTimeout,
        );
}

// Bad Request Handling
class BadRequestError extends DioError {
  final DioError dioError;

  BadRequestError({
    required this.dioError,
  }) : super(
          requestOptions: dioError.requestOptions,
          response: dioError.response,
          error: dioError.error,
          type: dioError.type,
        );
}

// Internal server error Handling
class InternalServerError extends DioError {
  final DioError dioError;

  InternalServerError({
    required this.dioError,
  }) : super(
          requestOptions: dioError.requestOptions,
          response: dioError.response,
          // error: dioError.error,
          error:
              'Kết nối không thành công, vui lòng kiểm tra lại kết nối của bạn',
          type: DioErrorType.other,
        );
}
// 404 Handling

class NotFoundError extends DioError {
  final DioError dioError;

  NotFoundError({
    required this.dioError,
  }) : super(
          requestOptions: dioError.requestOptions,
          response: dioError.response,
          error: 'The requested information could not be found',
          type: dioError.type,
        );
}

// No Internet Error
class NoInternetError extends DioError {
  final DioError dioError;

  NoInternetError({
    required this.dioError,
  }) : super(
          requestOptions: dioError.requestOptions,
          response: dioError.response,
          error: 'Có lỗi không xác định xảy ra',
          type: dioError.type,
        );
}
