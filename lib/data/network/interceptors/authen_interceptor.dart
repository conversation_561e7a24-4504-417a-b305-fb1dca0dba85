import 'package:dio/dio.dart';
import 'package:tutorO/data/network/exceptions/dio_error.dart';

class AuthenInterceptor extends Interceptor {
  @override
  onError(DioError err, ErrorInterceptorHandler handler) {
    if (err.response != null) {
      if (err.response!.statusCode! >= 400 || err.response!.statusCode! < 500) {
        final error = DioError(
            requestOptions: err.requestOptions,
            response: err.response,
            type: err.type,
            error: getMessage(err.response));
        return super.onError(AuthenApiError(dioError: error), handler);
      }
    }

    return super.onError(err, handler);
  }

  String? getMessage(Response? response) {
    if (response != null && response.data != null) {
      String? responseMessage;
      try {
        final data = response.data as Map<String, dynamic>;
        if (data.containsKey('error')) {
          responseMessage = data['error']['message'].toString();
        } else {
          responseMessage = response.statusMessage;
        }
      } catch (e) {
        rethrow;
      }
      return responseMessage;
    }
    return 'Vui lòng đăng nhập lại';
  }
}
