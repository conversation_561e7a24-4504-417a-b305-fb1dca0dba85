import 'package:dio/dio.dart';
import 'package:tutorO/data/network/exceptions/dio_error.dart';

class AppInterceptor extends Interceptor {
  @override
  void onError(DioError err, ErrorInterceptorHandler handler) {
    switch (err.type) {
      case DioErrorType.connectTimeout:
      case DioErrorType.sendTimeout:
      case DioErrorType.receiveTimeout:
        throw BadNetworkApiError(dioError: err);
      case DioErrorType.response:
        switch (err.response?.statusCode) {
          case 400:
            throw BadRequestError(dioError: err);
          case 401:
            throw AuthenApiError(dioError: err);
          case 404:
            throw NotFoundError(dioError: err);

          case 500:
            throw InternalServerError(dioError: err);
        }
        break;
      case DioErrorType.cancel:
        break;
      case DioErrorType.other:
        throw NoInternetError(dioError: err);
    }

    return handler.next(err);
  }
}
