// Copyright 2019 Aleksander W<PERSON>źniak
// SPDX-License-Identifier: Apache-2.0

import 'package:flutter/material.dart';
import '../customization/header_style.dart';
import '../shared/utils.dart' show CalendarFormat, DayBuilder;
import 'custom_icon_button.dart';
import 'format_button.dart';

class CalendarHeader extends StatelessWidget {
  final dynamic locale;
  final DateTime focusedMonth;
  final CalendarFormat calendarFormat;
  final HeaderStyle headerStyle;
  final VoidCallback onLeftChevronTap;
  final VoidCallback onRightChevronTap;
  final VoidCallback onHeaderTap;
  final VoidCallback onHeaderLongPress;
  final ValueChanged<CalendarFormat> onFormatButtonTap;
  final Map<CalendarFormat, String> availableCalendarFormats;
  final DayBuilder? headerTitleBuilder;

  const CalendarHeader({
    Key? key,
    this.locale,
    required this.focusedMonth,
    required this.calendarFormat,
    required this.headerStyle,
    required this.onLeftChevronTap,
    required this.onRightChevronTap,
    required this.onHeaderTap,
    required this.onHeaderLongPress,
    required this.onFormatButtonTap,
    required this.availableCalendarFormats,
    this.headerTitleBuilder,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // final text = 'Tháng ${focusedMonth.month} / ${focusedMonth.year}';
    final text = 'Lịch giảng dạy';

    return Container(
      decoration: headerStyle.decoration,
      margin: headerStyle.headerMargin,
      padding: headerStyle.headerPadding,
      height: 80,
      child: Row(
        mainAxisSize: MainAxisSize.max,
        children: [
          Expanded(
            child: headerTitleBuilder?.call(context, focusedMonth) ??
                GestureDetector(
                  onTap: onHeaderTap,
                  onLongPress: onHeaderLongPress,
                  child: Text(
                    text,
                    style: headerStyle.titleTextStyle,
                    textAlign: headerStyle.titleCentered
                        ? TextAlign.center
                        : TextAlign.start,
                  ),
                ),
          ),
          if (headerStyle.formatButtonVisible &&
              availableCalendarFormats.length > 1)
            Padding(
              padding: const EdgeInsets.only(left: 8.0),
              child: InkWell(
                child: FormatButton(
                  onTap: onFormatButtonTap,
                  availableCalendarFormats: availableCalendarFormats,
                  calendarFormat: calendarFormat,
                  decoration: headerStyle.formatButtonDecoration,
                  padding: headerStyle.formatButtonPadding,
                  textStyle: headerStyle.formatButtonTextStyle,
                  showsNextFormat: headerStyle.formatButtonShowsNext,
                ),
              ),
            ),
          SizedBox(
            width: 10,
          ),
          Container(
            decoration: BoxDecoration(
                color: Color(0xffF5B544).withOpacity(0.3),
                borderRadius: BorderRadius.circular(16)),
            child: Row(children: [
              if (headerStyle.leftChevronVisible)
                CustomIconButton(
                  icon: headerStyle.leftChevronIcon,
                  onTap: onLeftChevronTap,
                  margin: headerStyle.leftChevronMargin,
                  padding: headerStyle.leftChevronPadding,
                  borderRadius: BorderRadius.horizontal(
                    left: Radius.circular(20),
                  ),
                )
              else
                Padding(
                  padding: EdgeInsets.symmetric(vertical: 8),
                ),
              if (headerStyle.rightChevronVisible)
                CustomIconButton(
                  icon: headerStyle.rightChevronIcon,
                  onTap: onRightChevronTap,
                  margin: headerStyle.rightChevronMargin,
                  padding: headerStyle.rightChevronPadding,
                  borderRadius: BorderRadius.horizontal(
                    right: Radius.circular(20),
                  ),
                )
              else
                Padding(
                  padding: EdgeInsets.symmetric(vertical: 8),
                )
            ]),
          )
        ],
      ),
    );
  }
}
