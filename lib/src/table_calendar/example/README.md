# Table Calendar example

Demonstrates how to use [table_calendar](https://pub.dartlang.org/packages/table_calendar) package.
Displays **Table Calendar** widget with a `ListView` underneath it.

| ![Image](https://raw.githubusercontent.com/aleksanderwozniak/table_calendar/assets/table_calendar_styles.gif) | ![Image](https://raw.githubusercontent.com/aleksanderwozniak/table_calendar/assets/table_calendar_builders.gif) |
| :------------: | :------------: |
| **Table Calendar** with custom styles | **Table Calendar** with Builders |

**Table Calendar** offers a lot of customization:
* by using custom Styles
* by using custom Builders (accompanied by custom Styles)

Using just Styles is a great way to get nice results with little effort.
That being said, using Builders will give you full control over Calendar's UI.

This example project will show you both of aforementioned methods.

For more info please refer to [API docs](https://pub.dartlang.org/documentation/table_calendar/latest/table_calendar/table_calendar-library.html).
