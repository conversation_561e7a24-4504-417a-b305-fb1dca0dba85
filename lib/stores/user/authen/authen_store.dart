import 'package:mobx/mobx.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tutorO/data/repository/authen_repository.dart';
import 'package:tutorO/data/sharedpref/constants/preferences.dart';
import 'package:tutorO/models/forgot_password/forgot_password_model.dart';
import 'package:tutorO/models/user/authen_model.dart';
import 'package:tutorO/stores/error/error_store.dart';
part 'authen_store.g.dart';

class AuthenStore = _AuthenStoreBase with _$AuthenStore;

abstract class _AuthenStoreBase with Store {
  final AuthenticationRepository _repository;

  _AuthenStoreBase(AuthenticationRepository repository)
      : _repository = repository {
    // setting up disposers
    _setupDisposers();

    // checking if user is logged in
    repository.authToken.then((value) {
      isLoggedIn = value != null;
    });
  }
  final ErrorStore errorStore = ErrorStore();
  bool isLoggedIn = false;
  @observable
  ObservableFuture<AuthenticationModel?> authenticationFuture =
      ObservableFuture.value(null);
  late List<ReactionDisposer> _disposers;
  @observable
  AuthenticationErrorModel? errAuthentication;
  @observable
  bool authenticationCompleted = false;
  @observable
  bool authenticated = false;
  @observable
  bool isLoading = false;

  String countryCode = '+84';
// -----login
  @action
  Future login(String phoneNumber, String password) async {
    errAuthentication = null;
    authenticationCompleted = false;
    authenticated = false;
    isLoading = true;
    String numberField = countryCode + phoneNumber;
    final future = _repository.login(numberField, password);
    authenticationFuture = ObservableFuture(future);
    await future.then((authModel) {
      _repository.saveAuthToken(authModel!.token);
      print(authModel.token);
      authenticated = true;

      SharedPreferences.getInstance().then((preference) {
        preference.setBool(Preferences.is_logged_in, true);
      });
    }).catchError((error) {
      if (error is AuthenticationErrorModel) {
        errAuthentication = error;
        print("errAuthentication phoneNumber ${errAuthentication.toString()}");
      } else {
        errorStore.setErrorMessage("Something went wrong");
      }
   
      _repository.removeAuthToken();
      authenticated = false;
         throw errAuthentication.toString();
    }).whenComplete(() {
      authenticationCompleted = true;
      Future.delayed(Duration(milliseconds: 450))
          .then((_) => {isLoading = false});
    });
  }

  // ------ Forgot password ------------
  @observable
  bool newPasswordSent = false;
  @observable
  bool requestNewPassCompleted = false;
  @observable
  String forgotResponseMessage = '';
  @observable
  ForgotPasswordErrorModel? errForgotPwd;
  // @observable
  // ObservableFuture<bool> requestNewPassFuture = ObservableFuture.value(false);

  @action
  Future forgot(String email) async {
    errForgotPwd = null;
    newPasswordSent = false;
    requestNewPassCompleted = false;
    isLoading = true;

    final future = _repository.forgot(email);
    // requestNewPassFuture = ObservableFuture(future);
    await future.then((value) {
      newPasswordSent = true;
      forgotResponseMessage = value;
    }).whenComplete(() {
      isLoading = false;
      return requestNewPassCompleted = true;
    });
  }

  @action
  Future logout() async {
    await _repository.removeAuthToken();
    isLoggedIn = false;
  }

  void _setupDisposers() {
    _disposers = [
      reaction((_) => success, (_) => success = false, delay: 200),
      reaction((_) => authenticationCompleted, (_) => {}),
    ];
  }

  @observable
  bool success = false;
  @observable
  bool authenicationComplete = false;
}
