// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'authen_store.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$AuthenStore on _AuthenStoreBase, Store {
  late final _$authenticationFutureAtom =
      Atom(name: '_AuthenStoreBase.authenticationFuture', context: context);

  @override
  ObservableFuture<AuthenticationModel?> get authenticationFuture {
    _$authenticationFutureAtom.reportRead();
    return super.authenticationFuture;
  }

  @override
  set authenticationFuture(ObservableFuture<AuthenticationModel?> value) {
    _$authenticationFutureAtom.reportWrite(value, super.authenticationFuture,
        () {
      super.authenticationFuture = value;
    });
  }

  late final _$errAuthenticationAtom =
      Atom(name: '_AuthenStoreBase.errAuthentication', context: context);

  @override
  AuthenticationErrorModel? get errAuthentication {
    _$errAuthenticationAtom.reportRead();
    return super.errAuthentication;
  }

  @override
  set errAuthentication(AuthenticationErrorModel? value) {
    _$errAuthenticationAtom.reportWrite(value, super.errAuthentication, () {
      super.errAuthentication = value;
    });
  }

  late final _$authenticationCompletedAtom =
      Atom(name: '_AuthenStoreBase.authenticationCompleted', context: context);

  @override
  bool get authenticationCompleted {
    _$authenticationCompletedAtom.reportRead();
    return super.authenticationCompleted;
  }

  @override
  set authenticationCompleted(bool value) {
    _$authenticationCompletedAtom
        .reportWrite(value, super.authenticationCompleted, () {
      super.authenticationCompleted = value;
    });
  }

  late final _$authenticatedAtom =
      Atom(name: '_AuthenStoreBase.authenticated', context: context);

  @override
  bool get authenticated {
    _$authenticatedAtom.reportRead();
    return super.authenticated;
  }

  @override
  set authenticated(bool value) {
    _$authenticatedAtom.reportWrite(value, super.authenticated, () {
      super.authenticated = value;
    });
  }

  late final _$isLoadingAtom =
      Atom(name: '_AuthenStoreBase.isLoading', context: context);

  @override
  bool get isLoading {
    _$isLoadingAtom.reportRead();
    return super.isLoading;
  }

  @override
  set isLoading(bool value) {
    _$isLoadingAtom.reportWrite(value, super.isLoading, () {
      super.isLoading = value;
    });
  }

  late final _$newPasswordSentAtom =
      Atom(name: '_AuthenStoreBase.newPasswordSent', context: context);

  @override
  bool get newPasswordSent {
    _$newPasswordSentAtom.reportRead();
    return super.newPasswordSent;
  }

  @override
  set newPasswordSent(bool value) {
    _$newPasswordSentAtom.reportWrite(value, super.newPasswordSent, () {
      super.newPasswordSent = value;
    });
  }

  late final _$requestNewPassCompletedAtom =
      Atom(name: '_AuthenStoreBase.requestNewPassCompleted', context: context);

  @override
  bool get requestNewPassCompleted {
    _$requestNewPassCompletedAtom.reportRead();
    return super.requestNewPassCompleted;
  }

  @override
  set requestNewPassCompleted(bool value) {
    _$requestNewPassCompletedAtom
        .reportWrite(value, super.requestNewPassCompleted, () {
      super.requestNewPassCompleted = value;
    });
  }

  late final _$forgotResponseMessageAtom =
      Atom(name: '_AuthenStoreBase.forgotResponseMessage', context: context);

  @override
  String get forgotResponseMessage {
    _$forgotResponseMessageAtom.reportRead();
    return super.forgotResponseMessage;
  }

  @override
  set forgotResponseMessage(String value) {
    _$forgotResponseMessageAtom.reportWrite(value, super.forgotResponseMessage,
        () {
      super.forgotResponseMessage = value;
    });
  }

  late final _$errForgotPwdAtom =
      Atom(name: '_AuthenStoreBase.errForgotPwd', context: context);

  @override
  ForgotPasswordErrorModel? get errForgotPwd {
    _$errForgotPwdAtom.reportRead();
    return super.errForgotPwd;
  }

  @override
  set errForgotPwd(ForgotPasswordErrorModel? value) {
    _$errForgotPwdAtom.reportWrite(value, super.errForgotPwd, () {
      super.errForgotPwd = value;
    });
  }

  late final _$successAtom =
      Atom(name: '_AuthenStoreBase.success', context: context);

  @override
  bool get success {
    _$successAtom.reportRead();
    return super.success;
  }

  @override
  set success(bool value) {
    _$successAtom.reportWrite(value, super.success, () {
      super.success = value;
    });
  }

  late final _$authenicationCompleteAtom =
      Atom(name: '_AuthenStoreBase.authenicationComplete', context: context);

  @override
  bool get authenicationComplete {
    _$authenicationCompleteAtom.reportRead();
    return super.authenicationComplete;
  }

  @override
  set authenicationComplete(bool value) {
    _$authenicationCompleteAtom.reportWrite(value, super.authenicationComplete,
        () {
      super.authenicationComplete = value;
    });
  }

  late final _$loginAsyncAction =
      AsyncAction('_AuthenStoreBase.login', context: context);

  @override
  Future<dynamic> login(String phoneNumber, String password) {
    return _$loginAsyncAction.run(() => super.login(phoneNumber, password));
  }

  late final _$forgotAsyncAction =
      AsyncAction('_AuthenStoreBase.forgot', context: context);

  @override
  Future<dynamic> forgot(String email) {
    return _$forgotAsyncAction.run(() => super.forgot(email));
  }

  late final _$logoutAsyncAction =
      AsyncAction('_AuthenStoreBase.logout', context: context);

  @override
  Future<dynamic> logout() {
    return _$logoutAsyncAction.run(() => super.logout());
  }

  @override
  String toString() {
    return '''
authenticationFuture: ${authenticationFuture},
errAuthentication: ${errAuthentication},
authenticationCompleted: ${authenticationCompleted},
authenticated: ${authenticated},
isLoading: ${isLoading},
newPasswordSent: ${newPasswordSent},
requestNewPassCompleted: ${requestNewPassCompleted},
forgotResponseMessage: ${forgotResponseMessage},
errForgotPwd: ${errForgotPwd},
success: ${success},
authenicationComplete: ${authenicationComplete}
    ''';
  }
}
