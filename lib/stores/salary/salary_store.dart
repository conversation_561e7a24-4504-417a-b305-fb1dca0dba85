import 'package:flutter/material.dart';
import 'package:jiffy/jiffy.dart';
import 'package:mobx/mobx.dart';
import 'package:tutorO/data/repository/salary_repository.dart';
import 'package:tutorO/models/salary/salary_model.dart';
import 'package:tutorO/ui/salary/salary.dart';
part 'salary_store.g.dart';

class SalaryStore = _SalaryStore with _$SalaryStore;

abstract class _SalaryStore with Store {
  final SalaryRepository _salaryRepository;
  _SalaryStore({required SalaryRepository salaryRepository})
      : _salaryRepository = salaryRepository;

  @observable
  DataAttendanceInfo? dataAttendanceInfo;
  @observable
  bool isLoading = false;
  @observable
  bool isLoadingComplete = false;
  @observable
  bool isLoadingMoreComplete = false;
  @observable
  List<ListTeacherAttendanceInfo> listTeacherAttendanceInfo = [];
  @observable
  int pageNumber = 1;
  @observable
  bool isButtonVisible = false;
  @observable
  String? attendanceState;
  @observable
  String? checkInState;
  @observable
  DateTimeRange? dateRange;

  @action
  setButtonVisible(bool value) {
    isButtonVisible = value;
  }

  @action
  setPageNumber(int value) {
    if (pageNumber != value) {
      if (dataAttendanceInfo!.totalPage != null &&
          value <= dataAttendanceInfo!.totalPage! &&
          value > 0) {
        pageNumber = value;
      }
      print(pageNumber);
    }
  }

  @action
  Future<DataAttendanceInfo> getSalaryInfo() async {
    var startTime = dateRange!.start;
    var endTime = dateRange!.end;
    listTeacherAttendanceInfo = [];
    pageNumber = 1;
    isLoading = true;
    isLoadingComplete = false;
    var startSearchTime =
        '${startTime.year}-${startTime.month}-${startTime.day} 00:00:00';
    var endSearchTime =
        '${endTime.year}-${endTime.month}-${endTime.day} 23:59:59';

    return _salaryRepository
        .getSalaryInfo(startSearchTime, endSearchTime, pageNumber,
            attendanceState, checkInState)
        .then((value) {
      print(value);
      dataAttendanceInfo = value;
      listTeacherAttendanceInfo = value.listTeacherAttendanceInfo!;

      return value;
    }).whenComplete(() {
      isLoading = false;
      isLoadingComplete = true;
      isLoadingMoreComplete = true;
    });
  }

  @action
  Future<DataAttendanceInfo> getMoreSalaryInfo(
    DateTime startTime,
    DateTime endTime,
  ) async {
    isLoading = true;
    isLoadingMoreComplete = false;
    pageNumber += 1;
    var startSearchTime =
        '${startTime.year}-${startTime.month}-${startTime.day} 00:00:00';
    var endSearchTime =
        '${endTime.year}-${endTime.month}-${endTime.day} 23:59:59';
    return _salaryRepository
        .getSalaryInfo(startSearchTime, endSearchTime, pageNumber,
            attendanceState, checkInState)
        .then((value) {
      dataAttendanceInfo = value;
      if (value.listTeacherAttendanceInfo != null) {
        for (var e in value.listTeacherAttendanceInfo!) {
          if (!listTeacherAttendanceInfo.contains(e)) {
            listTeacherAttendanceInfo.add(e);
          }
        }
      }

      return value;
    }).whenComplete(() {
      isLoading = false;
      isLoadingMoreComplete = true;
    });
  }

  @action
  setAttendanceState(AttendanceStateFilter value) {
    attendanceState = attendanceStateConvertString(value);
  }

  @action
  setCheckInState(CheckInStateFilter value) {
    checkInState = checkInStateConvertString(value);
    print(checkInState);
  }

  @action
  setTimeRange(TimeSalary timesalary, DateTime startTime, DateTime endTime) {
    switch (timesalary) {
      case TimeSalary.thisMonth:
        dateRange = DateTimeRange(start: startTime, end: endTime);
        break;
      case TimeSalary.lastMonth:
        dateRange = DateTimeRange(
          start: Jiffy(startTime).subtract(months: 1).dateTime,
          end: Jiffy(endTime).subtract(months: 1).dateTime,
        );
        break;
      case TimeSalary.last2Months:
        var newStartTime = Jiffy(startTime).subtract(months: 2).dateTime;
        var newEndTime = Jiffy(endTime).subtract(months: 2).dateTime;
        dateRange = DateTimeRange(start: newStartTime, end: newEndTime);
        break;
      case TimeSalary.last3Months:
        var newStartTime = Jiffy(startTime).subtract(months: 3).dateTime;
        var newEndTime = Jiffy(endTime).subtract(months: 3).dateTime;
        dateRange = DateTimeRange(start: newStartTime, end: newEndTime);
        break;
    }
  }
}
