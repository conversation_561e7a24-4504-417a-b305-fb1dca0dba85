// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'salary_store.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$SalaryStore on _SalaryStore, Store {
  late final _$dataAttendanceInfoAtom =
      Atom(name: '_SalaryStore.dataAttendanceInfo', context: context);

  @override
  DataAttendanceInfo? get dataAttendanceInfo {
    _$dataAttendanceInfoAtom.reportRead();
    return super.dataAttendanceInfo;
  }

  @override
  set dataAttendanceInfo(DataAttendanceInfo? value) {
    _$dataAttendanceInfoAtom.reportWrite(value, super.dataAttendanceInfo, () {
      super.dataAttendanceInfo = value;
    });
  }

  late final _$isLoadingAtom =
      Atom(name: '_SalaryStore.isLoading', context: context);

  @override
  bool get isLoading {
    _$isLoadingAtom.reportRead();
    return super.isLoading;
  }

  @override
  set isLoading(bool value) {
    _$isLoadingAtom.reportWrite(value, super.isLoading, () {
      super.isLoading = value;
    });
  }

  late final _$isLoadingCompleteAtom =
      Atom(name: '_SalaryStore.isLoadingComplete', context: context);

  @override
  bool get isLoadingComplete {
    _$isLoadingCompleteAtom.reportRead();
    return super.isLoadingComplete;
  }

  @override
  set isLoadingComplete(bool value) {
    _$isLoadingCompleteAtom.reportWrite(value, super.isLoadingComplete, () {
      super.isLoadingComplete = value;
    });
  }

  late final _$isLoadingMoreCompleteAtom =
      Atom(name: '_SalaryStore.isLoadingMoreComplete', context: context);

  @override
  bool get isLoadingMoreComplete {
    _$isLoadingMoreCompleteAtom.reportRead();
    return super.isLoadingMoreComplete;
  }

  @override
  set isLoadingMoreComplete(bool value) {
    _$isLoadingMoreCompleteAtom.reportWrite(value, super.isLoadingMoreComplete,
        () {
      super.isLoadingMoreComplete = value;
    });
  }

  late final _$listTeacherAttendanceInfoAtom =
      Atom(name: '_SalaryStore.listTeacherAttendanceInfo', context: context);

  @override
  List<ListTeacherAttendanceInfo> get listTeacherAttendanceInfo {
    _$listTeacherAttendanceInfoAtom.reportRead();
    return super.listTeacherAttendanceInfo;
  }

  @override
  set listTeacherAttendanceInfo(List<ListTeacherAttendanceInfo> value) {
    _$listTeacherAttendanceInfoAtom
        .reportWrite(value, super.listTeacherAttendanceInfo, () {
      super.listTeacherAttendanceInfo = value;
    });
  }

  late final _$pageNumberAtom =
      Atom(name: '_SalaryStore.pageNumber', context: context);

  @override
  int get pageNumber {
    _$pageNumberAtom.reportRead();
    return super.pageNumber;
  }

  @override
  set pageNumber(int value) {
    _$pageNumberAtom.reportWrite(value, super.pageNumber, () {
      super.pageNumber = value;
    });
  }

  late final _$isButtonVisibleAtom =
      Atom(name: '_SalaryStore.isButtonVisible', context: context);

  @override
  bool get isButtonVisible {
    _$isButtonVisibleAtom.reportRead();
    return super.isButtonVisible;
  }

  @override
  set isButtonVisible(bool value) {
    _$isButtonVisibleAtom.reportWrite(value, super.isButtonVisible, () {
      super.isButtonVisible = value;
    });
  }

  late final _$attendanceStateAtom =
      Atom(name: '_SalaryStore.attendanceState', context: context);

  @override
  String? get attendanceState {
    _$attendanceStateAtom.reportRead();
    return super.attendanceState;
  }

  @override
  set attendanceState(String? value) {
    _$attendanceStateAtom.reportWrite(value, super.attendanceState, () {
      super.attendanceState = value;
    });
  }

  late final _$checkInStateAtom =
      Atom(name: '_SalaryStore.checkInState', context: context);

  @override
  String? get checkInState {
    _$checkInStateAtom.reportRead();
    return super.checkInState;
  }

  @override
  set checkInState(String? value) {
    _$checkInStateAtom.reportWrite(value, super.checkInState, () {
      super.checkInState = value;
    });
  }

  late final _$dateRangeAtom =
      Atom(name: '_SalaryStore.dateRange', context: context);

  @override
  DateTimeRange? get dateRange {
    _$dateRangeAtom.reportRead();
    return super.dateRange;
  }

  @override
  set dateRange(DateTimeRange? value) {
    _$dateRangeAtom.reportWrite(value, super.dateRange, () {
      super.dateRange = value;
    });
  }

  late final _$getSalaryInfoAsyncAction =
      AsyncAction('_SalaryStore.getSalaryInfo', context: context);

  @override
  Future<DataAttendanceInfo> getSalaryInfo() {
    return _$getSalaryInfoAsyncAction.run(() => super.getSalaryInfo());
  }

  late final _$getMoreSalaryInfoAsyncAction =
      AsyncAction('_SalaryStore.getMoreSalaryInfo', context: context);

  @override
  Future<DataAttendanceInfo> getMoreSalaryInfo(
      DateTime startTime, DateTime endTime) {
    return _$getMoreSalaryInfoAsyncAction
        .run(() => super.getMoreSalaryInfo(startTime, endTime));
  }

  late final _$_SalaryStoreActionController =
      ActionController(name: '_SalaryStore', context: context);

  @override
  dynamic setButtonVisible(bool value) {
    final _$actionInfo = _$_SalaryStoreActionController.startAction(
        name: '_SalaryStore.setButtonVisible');
    try {
      return super.setButtonVisible(value);
    } finally {
      _$_SalaryStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  dynamic setPageNumber(int value) {
    final _$actionInfo = _$_SalaryStoreActionController.startAction(
        name: '_SalaryStore.setPageNumber');
    try {
      return super.setPageNumber(value);
    } finally {
      _$_SalaryStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  dynamic setAttendanceState(AttendanceStateFilter value) {
    final _$actionInfo = _$_SalaryStoreActionController.startAction(
        name: '_SalaryStore.setAttendanceState');
    try {
      return super.setAttendanceState(value);
    } finally {
      _$_SalaryStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  dynamic setCheckInState(CheckInStateFilter value) {
    final _$actionInfo = _$_SalaryStoreActionController.startAction(
        name: '_SalaryStore.setCheckInState');
    try {
      return super.setCheckInState(value);
    } finally {
      _$_SalaryStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  dynamic setTimeRange(
      TimeSalary timesalary, DateTime startTime, DateTime endTime) {
    final _$actionInfo = _$_SalaryStoreActionController.startAction(
        name: '_SalaryStore.setTimeRange');
    try {
      return super.setTimeRange(timesalary, startTime, endTime);
    } finally {
      _$_SalaryStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
dataAttendanceInfo: ${dataAttendanceInfo},
isLoading: ${isLoading},
isLoadingComplete: ${isLoadingComplete},
isLoadingMoreComplete: ${isLoadingMoreComplete},
listTeacherAttendanceInfo: ${listTeacherAttendanceInfo},
pageNumber: ${pageNumber},
isButtonVisible: ${isButtonVisible},
attendanceState: ${attendanceState},
checkInState: ${checkInState},
dateRange: ${dateRange}
    ''';
  }
}
