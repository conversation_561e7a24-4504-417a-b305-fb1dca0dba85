// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'personal_info_store.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$PersonalInfoStore on _PersonalInfoStore, Store {
  Computed<bool>? _$loadingComputed;

  @override
  bool get loading => (_$loadingComputed ??= Computed<bool>(() => super.loading,
          name: '_PersonalInfoStore.loading'))
      .value;
  Computed<bool>? _$isChangingEmailComputed;

  @override
  bool get isChangingEmail =>
      (_$isChangingEmailComputed ??= Computed<bool>(() => super.isChangingEmail,
              name: '_PersonalInfoStore.isChangingEmail'))
          .value;
  Computed<bool>? _$isUpdatingAnInfoComputed;

  @override
  bool get isUpdatingAnInfo => (_$isUpdatingAnInfoComputed ??= Computed<bool>(
          () => super.isUpdatingAnInfo,
          name: '_PersonalInfoStore.isUpdatingAnInfo'))
      .value;

  late final _$fetchPersonalInfoFutureAtom = Atom(
      name: '_PersonalInfoStore.fetchPersonalInfoFuture', context: context);

  @override
  ObservableFuture<PersonalInfoModel?> get fetchPersonalInfoFuture {
    _$fetchPersonalInfoFutureAtom.reportRead();
    return super.fetchPersonalInfoFuture;
  }

  @override
  set fetchPersonalInfoFuture(ObservableFuture<PersonalInfoModel?> value) {
    _$fetchPersonalInfoFutureAtom
        .reportWrite(value, super.fetchPersonalInfoFuture, () {
      super.fetchPersonalInfoFuture = value;
    });
  }

  late final _$personalInfoModelAtom =
      Atom(name: '_PersonalInfoStore.personalInfoModel', context: context);

  @override
  PersonalInfoModel get personalInfoModel {
    _$personalInfoModelAtom.reportRead();
    return super.personalInfoModel;
  }

  @override
  set personalInfoModel(PersonalInfoModel value) {
    _$personalInfoModelAtom.reportWrite(value, super.personalInfoModel, () {
      super.personalInfoModel = value;
    });
  }

  late final _$personalInfoEditingModelAtom = Atom(
      name: '_PersonalInfoStore.personalInfoEditingModel', context: context);

  @override
  PersonalInfoModel get personalInfoEditingModel {
    _$personalInfoEditingModelAtom.reportRead();
    return super.personalInfoEditingModel;
  }

  @override
  set personalInfoEditingModel(PersonalInfoModel value) {
    _$personalInfoEditingModelAtom
        .reportWrite(value, super.personalInfoEditingModel, () {
      super.personalInfoEditingModel = value;
    });
  }

  late final _$avatarAtom =
      Atom(name: '_PersonalInfoStore.avatar', context: context);

  @override
  File get avatar {
    _$avatarAtom.reportRead();
    return super.avatar;
  }

  @override
  set avatar(File value) {
    _$avatarAtom.reportWrite(value, super.avatar, () {
      super.avatar = value;
    });
  }

  late final _$isSuccessAtom =
      Atom(name: '_PersonalInfoStore.isSuccess', context: context);

  @override
  bool get isSuccess {
    _$isSuccessAtom.reportRead();
    return super.isSuccess;
  }

  @override
  set isSuccess(bool value) {
    _$isSuccessAtom.reportWrite(value, super.isSuccess, () {
      super.isSuccess = value;
    });
  }

  late final _$isFirstCheckAtom =
      Atom(name: '_PersonalInfoStore.isFirstCheck', context: context);

  @override
  bool? get isFirstCheck {
    _$isFirstCheckAtom.reportRead();
    return super.isFirstCheck;
  }

  @override
  set isFirstCheck(bool? value) {
    _$isFirstCheckAtom.reportWrite(value, super.isFirstCheck, () {
      super.isFirstCheck = value;
    });
  }

  late final _$errorUpdateImageAtom =
      Atom(name: '_PersonalInfoStore.errorUpdateImage', context: context);

  @override
  PersonalInfoModelErrorModel? get errorUpdateImage {
    _$errorUpdateImageAtom.reportRead();
    return super.errorUpdateImage;
  }

  @override
  set errorUpdateImage(PersonalInfoModelErrorModel? value) {
    _$errorUpdateImageAtom.reportWrite(value, super.errorUpdateImage, () {
      super.errorUpdateImage = value;
    });
  }

  late final _$errorFetchPersonalInfoAtom =
      Atom(name: '_PersonalInfoStore.errorFetchPersonalInfo', context: context);

  @override
  PersonalInfoModelErrorModel? get errorFetchPersonalInfo {
    _$errorFetchPersonalInfoAtom.reportRead();
    return super.errorFetchPersonalInfo;
  }

  @override
  set errorFetchPersonalInfo(PersonalInfoModelErrorModel? value) {
    _$errorFetchPersonalInfoAtom
        .reportWrite(value, super.errorFetchPersonalInfo, () {
      super.errorFetchPersonalInfo = value;
    });
  }

  late final _$setEmailFutureAtom =
      Atom(name: '_PersonalInfoStore.setEmailFuture', context: context);

  @override
  ObservableFuture<bool> get setEmailFuture {
    _$setEmailFutureAtom.reportRead();
    return super.setEmailFuture;
  }

  @override
  set setEmailFuture(ObservableFuture<bool> value) {
    _$setEmailFutureAtom.reportWrite(value, super.setEmailFuture, () {
      super.setEmailFuture = value;
    });
  }

  late final _$setEmailSuccessAtom =
      Atom(name: '_PersonalInfoStore.setEmailSuccess', context: context);

  @override
  bool get setEmailSuccess {
    _$setEmailSuccessAtom.reportRead();
    return super.setEmailSuccess;
  }

  @override
  set setEmailSuccess(bool value) {
    _$setEmailSuccessAtom.reportWrite(value, super.setEmailSuccess, () {
      super.setEmailSuccess = value;
    });
  }

  late final _$requestSetEmailCompletedAtom = Atom(
      name: '_PersonalInfoStore.requestSetEmailCompleted', context: context);

  @override
  bool get requestSetEmailCompleted {
    _$requestSetEmailCompletedAtom.reportRead();
    return super.requestSetEmailCompleted;
  }

  @override
  set requestSetEmailCompleted(bool value) {
    _$requestSetEmailCompletedAtom
        .reportWrite(value, super.requestSetEmailCompleted, () {
      super.requestSetEmailCompleted = value;
    });
  }

  late final _$requestEditCompletedAtom =
      Atom(name: '_PersonalInfoStore.requestEditCompleted', context: context);

  @override
  bool get requestEditCompleted {
    _$requestEditCompletedAtom.reportRead();
    return super.requestEditCompleted;
  }

  @override
  set requestEditCompleted(bool value) {
    _$requestEditCompletedAtom.reportWrite(value, super.requestEditCompleted,
        () {
      super.requestEditCompleted = value;
    });
  }

  late final _$setEmailErrAtom =
      Atom(name: '_PersonalInfoStore.setEmailErr', context: context);

  @override
  String? get setEmailErr {
    _$setEmailErrAtom.reportRead();
    return super.setEmailErr;
  }

  @override
  set setEmailErr(String? value) {
    _$setEmailErrAtom.reportWrite(value, super.setEmailErr, () {
      super.setEmailErr = value;
    });
  }

  late final _$updateAllInfoStatusAtom =
      Atom(name: '_PersonalInfoStore.updateAllInfoStatus', context: context);

  @override
  ObservableFuture<PersonalInfoModel?> get updateAllInfoStatus {
    _$updateAllInfoStatusAtom.reportRead();
    return super.updateAllInfoStatus;
  }

  @override
  set updateAllInfoStatus(ObservableFuture<PersonalInfoModel?> value) {
    _$updateAllInfoStatusAtom.reportWrite(value, super.updateAllInfoStatus, () {
      super.updateAllInfoStatus = value;
    });
  }

  late final _$errorUpdateAnInfoAtom =
      Atom(name: '_PersonalInfoStore.errorUpdateAnInfo', context: context);

  @override
  PersonalInfoModelErrorModel? get errorUpdateAnInfo {
    _$errorUpdateAnInfoAtom.reportRead();
    return super.errorUpdateAnInfo;
  }

  @override
  set errorUpdateAnInfo(PersonalInfoModelErrorModel? value) {
    _$errorUpdateAnInfoAtom.reportWrite(value, super.errorUpdateAnInfo, () {
      super.errorUpdateAnInfo = value;
    });
  }

  late final _$getOtpUpdatePhoneCompletedAtom = Atom(
      name: '_PersonalInfoStore.getOtpUpdatePhoneCompleted', context: context);

  @override
  bool get getOtpUpdatePhoneCompleted {
    _$getOtpUpdatePhoneCompletedAtom.reportRead();
    return super.getOtpUpdatePhoneCompleted;
  }

  @override
  set getOtpUpdatePhoneCompleted(bool value) {
    _$getOtpUpdatePhoneCompletedAtom
        .reportWrite(value, super.getOtpUpdatePhoneCompleted, () {
      super.getOtpUpdatePhoneCompleted = value;
    });
  }

  late final _$getOtpUpdatePhoneErrorAtom =
      Atom(name: '_PersonalInfoStore.getOtpUpdatePhoneError', context: context);

  @override
  OTPErrorModel? get getOtpUpdatePhoneError {
    _$getOtpUpdatePhoneErrorAtom.reportRead();
    return super.getOtpUpdatePhoneError;
  }

  @override
  set getOtpUpdatePhoneError(OTPErrorModel? value) {
    _$getOtpUpdatePhoneErrorAtom
        .reportWrite(value, super.getOtpUpdatePhoneError, () {
      super.getOtpUpdatePhoneError = value;
    });
  }

  late final _$cancelUpdateProfileCompletedAtom = Atom(
      name: '_PersonalInfoStore.cancelUpdateProfileCompleted',
      context: context);

  @override
  bool get cancelUpdateProfileCompleted {
    _$cancelUpdateProfileCompletedAtom.reportRead();
    return super.cancelUpdateProfileCompleted;
  }

  @override
  set cancelUpdateProfileCompleted(bool value) {
    _$cancelUpdateProfileCompletedAtom
        .reportWrite(value, super.cancelUpdateProfileCompleted, () {
      super.cancelUpdateProfileCompleted = value;
    });
  }

  late final _$cancelUpdateProfileErrorAtom = Atom(
      name: '_PersonalInfoStore.cancelUpdateProfileError', context: context);

  @override
  dynamic get cancelUpdateProfileError {
    _$cancelUpdateProfileErrorAtom.reportRead();
    return super.cancelUpdateProfileError;
  }

  @override
  set cancelUpdateProfileError(dynamic value) {
    _$cancelUpdateProfileErrorAtom
        .reportWrite(value, super.cancelUpdateProfileError, () {
      super.cancelUpdateProfileError = value;
    });
  }

  late final _$submitProfileCompletedAtom =
      Atom(name: '_PersonalInfoStore.submitProfileCompleted', context: context);

  @override
  bool get submitProfileCompleted {
    _$submitProfileCompletedAtom.reportRead();
    return super.submitProfileCompleted;
  }

  @override
  set submitProfileCompleted(bool value) {
    _$submitProfileCompletedAtom
        .reportWrite(value, super.submitProfileCompleted, () {
      super.submitProfileCompleted = value;
    });
  }

  late final _$submitProfileErrorAtom =
      Atom(name: '_PersonalInfoStore.submitProfileError', context: context);

  @override
  dynamic get submitProfileError {
    _$submitProfileErrorAtom.reportRead();
    return super.submitProfileError;
  }

  @override
  set submitProfileError(dynamic value) {
    _$submitProfileErrorAtom.reportWrite(value, super.submitProfileError, () {
      super.submitProfileError = value;
    });
  }

  late final _$updateImageAsyncAction =
      AsyncAction('_PersonalInfoStore.updateImage', context: context);

  @override
  Future<dynamic> updateImage(File image, String key,
      {bool isUpdateVerifiedProfile = false}) {
    return _$updateImageAsyncAction.run(() => super.updateImage(image, key,
        isUpdateVerifiedProfile: isUpdateVerifiedProfile));
  }

  late final _$fetchPersonalInfoAsyncAction =
      AsyncAction('_PersonalInfoStore.fetchPersonalInfo', context: context);

  @override
  Future<dynamic> fetchPersonalInfo() {
    return _$fetchPersonalInfoAsyncAction.run(() => super.fetchPersonalInfo());
  }

  late final _$getPersonalInfoAsyncAction =
      AsyncAction('_PersonalInfoStore.getPersonalInfo', context: context);

  @override
  Future<PersonalInfoModel> getPersonalInfo() {
    return _$getPersonalInfoAsyncAction.run(() => super.getPersonalInfo());
  }

  late final _$setEmailAsyncAction =
      AsyncAction('_PersonalInfoStore.setEmail', context: context);

  @override
  Future<dynamic> setEmail(String email) {
    return _$setEmailAsyncAction.run(() => super.setEmail(email));
  }

  late final _$updateAnInfoAsyncAction =
      AsyncAction('_PersonalInfoStore.updateAnInfo', context: context);

  @override
  Future<dynamic> updateAnInfo(Map<String, dynamic> data) {
    return _$updateAnInfoAsyncAction.run(() => super.updateAnInfo(data));
  }

  late final _$getOtpUpdatePhoneAsyncAction =
      AsyncAction('_PersonalInfoStore.getOtpUpdatePhone', context: context);

  @override
  Future<dynamic> getOtpUpdatePhone(String phoneNumber, String token) {
    return _$getOtpUpdatePhoneAsyncAction
        .run(() => super.getOtpUpdatePhone(phoneNumber, token));
  }

  late final _$cancelUpdateProfileAsyncAction =
      AsyncAction('_PersonalInfoStore.cancelUpdateProfile', context: context);

  @override
  Future<dynamic> cancelUpdateProfile() {
    return _$cancelUpdateProfileAsyncAction
        .run(() => super.cancelUpdateProfile());
  }

  late final _$submitProfileAsyncAction =
      AsyncAction('_PersonalInfoStore.submitProfile', context: context);

  @override
  Future<dynamic> submitProfile() {
    return _$submitProfileAsyncAction.run(() => super.submitProfile());
  }

  late final _$_PersonalInfoStoreActionController =
      ActionController(name: '_PersonalInfoStore', context: context);

  @override
  void setAvatar(File value) {
    final _$actionInfo = _$_PersonalInfoStoreActionController.startAction(
        name: '_PersonalInfoStore.setAvatar');
    try {
      return super.setAvatar(value);
    } finally {
      _$_PersonalInfoStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  List<String> validateData() {
    final _$actionInfo = _$_PersonalInfoStoreActionController.startAction(
        name: '_PersonalInfoStore.validateData');
    try {
      return super.validateData();
    } finally {
      _$_PersonalInfoStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
fetchPersonalInfoFuture: ${fetchPersonalInfoFuture},
personalInfoModel: ${personalInfoModel},
personalInfoEditingModel: ${personalInfoEditingModel},
avatar: ${avatar},
isSuccess: ${isSuccess},
isFirstCheck: ${isFirstCheck},
errorUpdateImage: ${errorUpdateImage},
errorFetchPersonalInfo: ${errorFetchPersonalInfo},
setEmailFuture: ${setEmailFuture},
setEmailSuccess: ${setEmailSuccess},
requestSetEmailCompleted: ${requestSetEmailCompleted},
requestEditCompleted: ${requestEditCompleted},
setEmailErr: ${setEmailErr},
updateAllInfoStatus: ${updateAllInfoStatus},
errorUpdateAnInfo: ${errorUpdateAnInfo},
getOtpUpdatePhoneCompleted: ${getOtpUpdatePhoneCompleted},
getOtpUpdatePhoneError: ${getOtpUpdatePhoneError},
cancelUpdateProfileCompleted: ${cancelUpdateProfileCompleted},
cancelUpdateProfileError: ${cancelUpdateProfileError},
submitProfileCompleted: ${submitProfileCompleted},
submitProfileError: ${submitProfileError},
loading: ${loading},
isChangingEmail: ${isChangingEmail},
isUpdatingAnInfo: ${isUpdatingAnInfo}
    ''';
  }
}
