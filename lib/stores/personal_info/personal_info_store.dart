import 'dart:io';

import 'package:mobx/mobx.dart';
import 'package:tutorO/data/repository/academic_level_repository.dart';
import 'package:tutorO/data/repository/certificate_repository.dart';
import 'package:tutorO/data/repository/profile_repository.dart';
import 'package:tutorO/data/repository/work_experience_repository.dart';
import 'package:tutorO/models/otp/otp_model.dart';
import 'package:tutorO/models/profile/email.dart';
import 'package:tutorO/models/profile/personal_info_model.dart';
import 'package:tutorO/stores/academic_level/academic_level_store.dart';
import 'package:tutorO/stores/certificate_store/certificate_store.dart';
import 'package:tutorO/stores/error/error_store.dart';
import 'package:tutorO/stores/image_self/image_self_store.dart';
import 'package:tutorO/stores/work_exp/work_exp_store.dart';
import 'package:tutorO/utils/image/image.dart';
import 'package:tutorO/utils/validate/validate_input.dart';
part 'personal_info_store.g.dart';

class PersonalInfoStore = _PersonalInfoStore with _$PersonalInfoStore;

abstract class _PersonalInfoStore with Store {
  final ProfileRepository _profileRepository;

  // store for handling errors
  final ErrorStore errorStore = ErrorStore();

  // Store for handling academic level
  late AcademicLevelStore academicLevelStore;

  // Store for handling certification level
  late CertificationStore certificationStore;

  // Store for handling academic level
  late WorkExpStore workExpStore;

  late ImageSelfStore imageSelfStore;

  _PersonalInfoStore(
      {required ProfileRepository profileRepository,
      required AcademicLevelRepository academicLevelRepository,
      required WorkExpRepository workExpRepository,
      required CertificationRepository certificationRepository})
      : _profileRepository = profileRepository,
        workExpStore =
            WorkExpStore(professionExperienceRepository: workExpRepository),
        academicLevelStore = AcademicLevelStore(
            academicLevelRepository: academicLevelRepository),
        certificationStore = CertificationStore(
            certificationRepository: certificationRepository),
        imageSelfStore = ImageSelfStore(profileRepository);

  @observable
  ObservableFuture<PersonalInfoModel?> fetchPersonalInfoFuture =
      ObservableFuture.value(null);

  @observable
  PersonalInfoModel personalInfoModel = PersonalInfoModel.empty();

  @observable
  PersonalInfoModel personalInfoEditingModel = PersonalInfoModel.empty();

  @observable
  File avatar = File('');

  @computed
  bool get loading => fetchPersonalInfoFuture.status == FutureStatus.pending;

  @observable
  bool isSuccess = false;

  @observable
  bool? isFirstCheck = true;
  // ignore: use_setters_to_change_properties
  @action
  void setAvatar(File value) {
    avatar = value;
  }

  @action
  List<String> validateData() {
    var inValidData = <String>[];
    final personalInfoModelJson = personalInfoEditingModel.toJson();
    personalInfoModelJson.forEach((final key, final value) {
      var keyValidate = [
        "full_name",
        "facebook_url",
        "identity_card",
        "identity_place"
      ];
      if (keyValidate.contains(key)) {
        var valueCheck = value ?? "";
        if (!ValidateInput.validate(valueCheck, [TypeValidate.EMPTY])) {
          inValidData.add(key);
        }
      }
    });
    return inValidData;
  }

  @observable
  PersonalInfoModelErrorModel? errorUpdateImage;

  @action
  Future updateImage(File image, String key,
      {bool isUpdateVerifiedProfile = false}) async {
    isSuccess = false;
    errorUpdateImage = null;
    final future = _profileRepository.uploadImage(
        await ImageConvert.compressFile(image), key);
    fetchPersonalInfoFuture = ObservableFuture(future);
    future.then((profile) {
      personalInfoEditingModel = profile;
      if (isUpdateVerifiedProfile) {
        personalInfoModel = profile;
      }
    }).catchError((onError) {
      errorUpdateImage = onError as PersonalInfoModelErrorModel;
    }).whenComplete(() {
      isSuccess = true;
    });
  }

  @observable
  PersonalInfoModelErrorModel? errorFetchPersonalInfo;

  @action
  Future fetchPersonalInfo() async {
    isSuccess = false;

    return _profileRepository.fetchPersonalInfo().then((profile) {
      personalInfoModel = profile;
      personalInfoEditingModel = profile;
      return profile;
    }).whenComplete(() {
      isSuccess = true;
    });
  }

  @action
  Future<PersonalInfoModel> getPersonalInfo() async {
    isSuccess = false;
    return _profileRepository.fetchPersonalInfo().then((value) {
      personalInfoModel = value;
      personalInfoEditingModel = value;
      return value;
    }).whenComplete(() => isSuccess = true);
  }

  @observable
  ObservableFuture<bool> setEmailFuture = ObservableFuture.value(false);

  @observable
  bool setEmailSuccess = false;

  @observable
  bool requestSetEmailCompleted = false;

  @observable
  bool requestEditCompleted = false;

  @observable
  String? setEmailErr;

  @computed
  bool get isChangingEmail => setEmailFuture.status == FutureStatus.pending;

  @action
  Future setEmail(String email) async {
    setEmailErr = null;
    setEmailSuccess = false;
    requestSetEmailCompleted = false;
    final future = _profileRepository.setEmail(email);
    setEmailFuture = ObservableFuture(future);
    future.then((value) {
      setEmailSuccess = true;
    }).catchError((error) {
      if (error is EmailErrorModel) {
        setEmailErr = error.emailErr;
      }
      setEmailSuccess = false;
    }).whenComplete(() {
      requestSetEmailCompleted = true;
    });
  }

  @observable
  ObservableFuture<PersonalInfoModel?> updateAllInfoStatus =
      ObservableFuture.value(null);

  @computed
  bool get isUpdatingAnInfo =>
      updateAllInfoStatus.status == FutureStatus.pending;

  @observable
  PersonalInfoModelErrorModel? errorUpdateAnInfo;

  @action
  Future updateAnInfo(Map<String, dynamic> data) async {
    requestEditCompleted = false;
    errorUpdateAnInfo = null;
    final future = _profileRepository.updateAnInfo(data);
    updateAllInfoStatus = ObservableFuture(future);
    future.then((profile) {
      personalInfoEditingModel = profile;
      isSuccess = true;
    }).catchError((onError) {
      errorUpdateAnInfo = onError;
    }).whenComplete(() => requestEditCompleted = true);
  }

  @observable
  bool getOtpUpdatePhoneCompleted = false;

  @observable
  OTPErrorModel? getOtpUpdatePhoneError;

  @action
  Future getOtpUpdatePhone(String phoneNumber, String token) async {
    getOtpUpdatePhoneCompleted = false;
    getOtpUpdatePhoneError = null;
    final future = _profileRepository.getOtpUpdatePhone(phoneNumber, token);
    future.then((_) {}).catchError((onError) {
      getOtpUpdatePhoneError = onError;
    }).whenComplete(() => getOtpUpdatePhoneCompleted = true);
  }

  @observable
  bool cancelUpdateProfileCompleted = false;

  @observable
  dynamic cancelUpdateProfileError;

  @action
  Future cancelUpdateProfile() async {
    cancelUpdateProfileCompleted = false;
    cancelUpdateProfileError = null;
    final future = _profileRepository.cancelUpdateProfile();
    future.then((_) {}).catchError((onError) {
      cancelUpdateProfileError = onError;
    }).whenComplete(() => cancelUpdateProfileCompleted = true);
  }

  @observable
  bool submitProfileCompleted = false;

  @observable
  dynamic submitProfileError;

  @action
  Future submitProfile() async {
    submitProfileCompleted = false;
    submitProfileError = null;
    final future = _profileRepository.submitProfile();
    future.then((_) {}).catchError((onError) {
      submitProfileError = onError;
    }).whenComplete(() => submitProfileCompleted = true);
  }
}
