// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'class_detail_store.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ClassDetailStore on _ClassDetailStore, Store {
  Computed<bool>? _$isLoadingInitDataComputed;

  @override
  bool get isLoadingInitData => (_$isLoadingInitDataComputed ??= Computed<bool>(
          () => super.isLoadingInitData,
          name: '_ClassDetailStore.isLoadingInitData'))
      .value;

  late final _$listStudentInClassAtom =
      Atom(name: '_ClassDetailStore.listStudentInClass', context: context);

  @override
  List<Student> get listStudentInClass {
    _$listStudentInClassAtom.reportRead();
    return super.listStudentInClass;
  }

  @override
  set listStudentInClass(List<Student> value) {
    _$listStudentInClassAtom.reportWrite(value, super.listStudentInClass, () {
      super.listStudentInClass = value;
    });
  }

  late final _$fetchStudentsErrorAtom =
      Atom(name: '_ClassDetailStore.fetchStudentsError', context: context);

  @override
  StudentError? get fetchStudentsError {
    _$fetchStudentsErrorAtom.reportRead();
    return super.fetchStudentsError;
  }

  @override
  set fetchStudentsError(StudentError? value) {
    _$fetchStudentsErrorAtom.reportWrite(value, super.fetchStudentsError, () {
      super.fetchStudentsError = value;
    });
  }

  late final _$fetchStudentsCompletedAtom =
      Atom(name: '_ClassDetailStore.fetchStudentsCompleted', context: context);

  @override
  bool get fetchStudentsCompleted {
    _$fetchStudentsCompletedAtom.reportRead();
    return super.fetchStudentsCompleted;
  }

  @override
  set fetchStudentsCompleted(bool value) {
    _$fetchStudentsCompletedAtom
        .reportWrite(value, super.fetchStudentsCompleted, () {
      super.fetchStudentsCompleted = value;
    });
  }

  late final _$futureFetchStudentsAtom =
      Atom(name: '_ClassDetailStore.futureFetchStudents', context: context);

  @override
  ObservableFuture<List<Student>> get futureFetchStudents {
    _$futureFetchStudentsAtom.reportRead();
    return super.futureFetchStudents;
  }

  @override
  set futureFetchStudents(ObservableFuture<List<Student>> value) {
    _$futureFetchStudentsAtom.reportWrite(value, super.futureFetchStudents, () {
      super.futureFetchStudents = value;
    });
  }

  late final _$listSessionsInClassAtom =
      Atom(name: '_ClassDetailStore.listSessionsInClass', context: context);

  @override
  ClassSessions get listSessionsInClass {
    _$listSessionsInClassAtom.reportRead();
    return super.listSessionsInClass;
  }

  @override
  set listSessionsInClass(ClassSessions value) {
    _$listSessionsInClassAtom.reportWrite(value, super.listSessionsInClass, () {
      super.listSessionsInClass = value;
    });
  }

  late final _$fetchSessionsInClassCompletedAtom = Atom(
      name: '_ClassDetailStore.fetchSessionsInClassCompleted',
      context: context);

  @override
  bool get fetchSessionsInClassCompleted {
    _$fetchSessionsInClassCompletedAtom.reportRead();
    return super.fetchSessionsInClassCompleted;
  }

  @override
  set fetchSessionsInClassCompleted(bool value) {
    _$fetchSessionsInClassCompletedAtom
        .reportWrite(value, super.fetchSessionsInClassCompleted, () {
      super.fetchSessionsInClassCompleted = value;
    });
  }

  late final _$errFetchSessionsInClassAtom =
      Atom(name: '_ClassDetailStore.errFetchSessionsInClass', context: context);

  @override
  ClassSessionsError? get errFetchSessionsInClass {
    _$errFetchSessionsInClassAtom.reportRead();
    return super.errFetchSessionsInClass;
  }

  @override
  set errFetchSessionsInClass(ClassSessionsError? value) {
    _$errFetchSessionsInClassAtom
        .reportWrite(value, super.errFetchSessionsInClass, () {
      super.errFetchSessionsInClass = value;
    });
  }

  late final _$futureFetchSessionInClassAtom = Atom(
      name: '_ClassDetailStore.futureFetchSessionInClass', context: context);

  @override
  ObservableFuture<ClassSessions?> get futureFetchSessionInClass {
    _$futureFetchSessionInClassAtom.reportRead();
    return super.futureFetchSessionInClass;
  }

  @override
  set futureFetchSessionInClass(ObservableFuture<ClassSessions?> value) {
    _$futureFetchSessionInClassAtom
        .reportWrite(value, super.futureFetchSessionInClass, () {
      super.futureFetchSessionInClass = value;
    });
  }

  late final _$cancelTeachClassCompletedAtom = Atom(
      name: '_ClassDetailStore.cancelTeachClassCompleted', context: context);

  @override
  bool get cancelTeachClassCompleted {
    _$cancelTeachClassCompletedAtom.reportRead();
    return super.cancelTeachClassCompleted;
  }

  @override
  set cancelTeachClassCompleted(bool value) {
    _$cancelTeachClassCompletedAtom
        .reportWrite(value, super.cancelTeachClassCompleted, () {
      super.cancelTeachClassCompleted = value;
    });
  }

  late final _$errCancelTeachClassAtom =
      Atom(name: '_ClassDetailStore.errCancelTeachClass', context: context);

  @override
  ClassSessionsError? get errCancelTeachClass {
    _$errCancelTeachClassAtom.reportRead();
    return super.errCancelTeachClass;
  }

  @override
  set errCancelTeachClass(ClassSessionsError? value) {
    _$errCancelTeachClassAtom.reportWrite(value, super.errCancelTeachClass, () {
      super.errCancelTeachClass = value;
    });
  }

  late final _$currentClassScheduleAtom =
      Atom(name: '_ClassDetailStore.currentClassSchedule', context: context);

  @override
  ClassSchedule? get currentClassSchedule {
    _$currentClassScheduleAtom.reportRead();
    return super.currentClassSchedule;
  }

  @override
  set currentClassSchedule(ClassSchedule? value) {
    _$currentClassScheduleAtom.reportWrite(value, super.currentClassSchedule,
        () {
      super.currentClassSchedule = value;
    });
  }

  late final _$fetchCancelRequestCompletedAtom = Atom(
      name: '_ClassDetailStore.fetchCancelRequestCompleted', context: context);

  @override
  bool get fetchCancelRequestCompleted {
    _$fetchCancelRequestCompletedAtom.reportRead();
    return super.fetchCancelRequestCompleted;
  }

  @override
  set fetchCancelRequestCompleted(bool value) {
    _$fetchCancelRequestCompletedAtom
        .reportWrite(value, super.fetchCancelRequestCompleted, () {
      super.fetchCancelRequestCompleted = value;
    });
  }

  late final _$futureFetchCancelClassStatusAtom = Atom(
      name: '_ClassDetailStore.futureFetchCancelClassStatus', context: context);

  @override
  ObservableFuture<Map<String, dynamic>?> get futureFetchCancelClassStatus {
    _$futureFetchCancelClassStatusAtom.reportRead();
    return super.futureFetchCancelClassStatus;
  }

  @override
  set futureFetchCancelClassStatus(
      ObservableFuture<Map<String, dynamic>?> value) {
    _$futureFetchCancelClassStatusAtom
        .reportWrite(value, super.futureFetchCancelClassStatus, () {
      super.futureFetchCancelClassStatus = value;
    });
  }

  late final _$listLessonImagesAndVideoAtom = Atom(
      name: '_ClassDetailStore.listLessonImagesAndVideo', context: context);

  @override
  List<LessonImage> get listLessonImagesAndVideo {
    _$listLessonImagesAndVideoAtom.reportRead();
    return super.listLessonImagesAndVideo;
  }

  @override
  set listLessonImagesAndVideo(List<LessonImage> value) {
    _$listLessonImagesAndVideoAtom
        .reportWrite(value, super.listLessonImagesAndVideo, () {
      super.listLessonImagesAndVideo = value;
    });
  }

  late final _$fetchListLessonImagesAndVideoErrorAtom = Atom(
      name: '_ClassDetailStore.fetchListLessonImagesAndVideoError',
      context: context);

  @override
  dynamic get fetchListLessonImagesAndVideoError {
    _$fetchListLessonImagesAndVideoErrorAtom.reportRead();
    return super.fetchListLessonImagesAndVideoError;
  }

  @override
  set fetchListLessonImagesAndVideoError(dynamic value) {
    _$fetchListLessonImagesAndVideoErrorAtom
        .reportWrite(value, super.fetchListLessonImagesAndVideoError, () {
      super.fetchListLessonImagesAndVideoError = value;
    });
  }

  late final _$fetchListLessonImagesAndVideoCompletedAtom = Atom(
      name: '_ClassDetailStore.fetchListLessonImagesAndVideoCompleted',
      context: context);

  @override
  bool get fetchListLessonImagesAndVideoCompleted {
    _$fetchListLessonImagesAndVideoCompletedAtom.reportRead();
    return super.fetchListLessonImagesAndVideoCompleted;
  }

  @override
  set fetchListLessonImagesAndVideoCompleted(bool value) {
    _$fetchListLessonImagesAndVideoCompletedAtom
        .reportWrite(value, super.fetchListLessonImagesAndVideoCompleted, () {
      super.fetchListLessonImagesAndVideoCompleted = value;
    });
  }

  late final _$fetchStudentsAsyncAction =
      AsyncAction('_ClassDetailStore.fetchStudents', context: context);

  @override
  Future<dynamic> fetchStudents(int idClass) {
    return _$fetchStudentsAsyncAction.run(() => super.fetchStudents(idClass));
  }

  late final _$fetchSessionInClassAsyncAction =
      AsyncAction('_ClassDetailStore.fetchSessionInClass', context: context);

  @override
  Future<dynamic> fetchSessionInClass(Map<String, dynamic> query) {
    return _$fetchSessionInClassAsyncAction
        .run(() => super.fetchSessionInClass(query));
  }

  late final _$cancelTeachClassAsyncAction =
      AsyncAction('_ClassDetailStore.cancelTeachClass', context: context);

  @override
  Future<dynamic> cancelTeachClass(
      ClassSchedule classSchedule, CancelClassActionType type) {
    return _$cancelTeachClassAsyncAction
        .run(() => super.cancelTeachClass(classSchedule, type));
  }

  late final _$fetchCancelClassStatusAsyncAction =
      AsyncAction('_ClassDetailStore.fetchCancelClassStatus', context: context);

  @override
  Future<dynamic> fetchCancelClassStatus(ClassSchedule classSchedule) {
    return _$fetchCancelClassStatusAsyncAction
        .run(() => super.fetchCancelClassStatus(classSchedule));
  }

  late final _$fetchListLessonAndVideoAsyncAction = AsyncAction(
      '_ClassDetailStore.fetchListLessonAndVideo',
      context: context);

  @override
  Future<dynamic> fetchListLessonAndVideo(int sessionId) {
    return _$fetchListLessonAndVideoAsyncAction
        .run(() => super.fetchListLessonAndVideo(sessionId));
  }

  @override
  String toString() {
    return '''
listStudentInClass: ${listStudentInClass},
fetchStudentsError: ${fetchStudentsError},
fetchStudentsCompleted: ${fetchStudentsCompleted},
futureFetchStudents: ${futureFetchStudents},
listSessionsInClass: ${listSessionsInClass},
fetchSessionsInClassCompleted: ${fetchSessionsInClassCompleted},
errFetchSessionsInClass: ${errFetchSessionsInClass},
futureFetchSessionInClass: ${futureFetchSessionInClass},
cancelTeachClassCompleted: ${cancelTeachClassCompleted},
errCancelTeachClass: ${errCancelTeachClass},
currentClassSchedule: ${currentClassSchedule},
fetchCancelRequestCompleted: ${fetchCancelRequestCompleted},
futureFetchCancelClassStatus: ${futureFetchCancelClassStatus},
listLessonImagesAndVideo: ${listLessonImagesAndVideo},
fetchListLessonImagesAndVideoError: ${fetchListLessonImagesAndVideoError},
fetchListLessonImagesAndVideoCompleted: ${fetchListLessonImagesAndVideoCompleted},
isLoadingInitData: ${isLoadingInitData}
    ''';
  }
}
