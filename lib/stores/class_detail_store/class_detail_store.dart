import 'package:mobx/mobx.dart';
import 'package:tutorO/constants/values.dart';
import 'package:tutorO/data/repository/class_repository.dart';
import 'package:tutorO/models/class/class.dart';

import '../../models/class_session/class_session.dart';
import '../../models/lesson_image/lesson_image.dart';
import '../../models/student/student.dart';

// Include generated file
part 'class_detail_store.g.dart';

class ClassDetailStore = _ClassDetailStore with _$ClassDetailStore;

// The store-class
abstract class _ClassDetailStore with Store {
  final ClassRepository _classRepository;

  _ClassDetailStore({required ClassRepository classRepository})
      : _classRepository = classRepository;

  @observable
  List<Student> listStudentInClass = [];

  @observable
  StudentError? fetchStudentsError;

  @observable
  bool fetchStudentsCompleted = false;

  @computed
  bool get isLoadingInitData =>
      futureFetchStudents.status == FutureStatus.pending ||
      futureFetchSessionInClass.status == FutureStatus.pending ||
      futureFetchCancelClassStatus.status == FutureStatus.pending;

  @observable
  ObservableFuture<List<Student>> futureFetchStudents =
      ObservableFuture.value([]);

  @action
  Future fetchStudents(int idClass) async {
    final future = _classRepository.fetchStudents(idClass);
    futureFetchStudents = ObservableFuture(future);
    fetchStudentsCompleted = false;
    fetchStudentsError = null;
    future.then((listStudentInClass) {
      this.listStudentInClass = listStudentInClass;
    }).catchError((error) {
      print(error);
      fetchStudentsError = error;
    }).whenComplete(() => fetchStudentsCompleted = true);
  }

  @observable
  ClassSessions listSessionsInClass = ClassSessions.empty();

  @observable
  bool fetchSessionsInClassCompleted = false;

  @observable
  ClassSessionsError? errFetchSessionsInClass;

  @observable
  ObservableFuture<ClassSessions?> futureFetchSessionInClass =
      ObservableFuture.value(null);

  @action
  Future fetchSessionInClass(Map<String, dynamic> query) async {
    final future = _classRepository.fetchClassSession(query);
    futureFetchSessionInClass = ObservableFuture(future);
    errFetchSessionsInClass = null;
    fetchSessionsInClassCompleted = false;
    future.then((listSessionsInClass) {
      this.listSessionsInClass = listSessionsInClass;
    }).catchError((error) {
      errFetchSessionsInClass = error;
    }).whenComplete(() => fetchSessionsInClassCompleted = true);
  }

  @observable
  bool cancelTeachClassCompleted = false;

  @observable
  ClassSessionsError? errCancelTeachClass;

  @observable
  ClassSchedule? currentClassSchedule;

  @action
  Future cancelTeachClass(
      ClassSchedule classSchedule, CancelClassActionType type) async {
    errCancelTeachClass = null;
    cancelTeachClassCompleted = false;
    currentClassSchedule = classSchedule;
    final future =
        _classRepository.cancelTeachClass(classSchedule.classId ?? 0, type);
    future.then((value) {
      currentClassSchedule!.updateRequestClassStatus(value["request_state"]);
    }).catchError((error) {
      errCancelTeachClass = error as ClassSessionsError;
    }).whenComplete(() => cancelTeachClassCompleted = true);
  }

  @observable
  bool fetchCancelRequestCompleted = false;

  @observable
  ObservableFuture<Map<String, dynamic>?> futureFetchCancelClassStatus =
      ObservableFuture.value(null);

  @action
  Future fetchCancelClassStatus(ClassSchedule classSchedule) async {
    final future =
        _classRepository.fetchCancelClassStatus(classSchedule.classId ?? 0);
    futureFetchCancelClassStatus = ObservableFuture(future);
    fetchCancelRequestCompleted = false;
    future.then((result) {
      classSchedule.updateRequestClassStatus(result["request_state"]);
    }).whenComplete(() => fetchCancelRequestCompleted = true);
  }

  @observable
  List<LessonImage> listLessonImagesAndVideo = [];

  @observable
  dynamic fetchListLessonImagesAndVideoError;

  @observable
  bool fetchListLessonImagesAndVideoCompleted = false;

  @action
  Future fetchListLessonAndVideo(int sessionId) async {
    final future = _classRepository.fetchListLessonAndVideo(sessionId);
    fetchListLessonImagesAndVideoCompleted = false;
    fetchListLessonImagesAndVideoError = null;
    future.then((listLessonImagesAndVideo) {
      this.listLessonImagesAndVideo = listLessonImagesAndVideo;
    }).catchError((error) {
      print(error);
      fetchListLessonImagesAndVideoError = error;
    }).whenComplete(() => fetchListLessonImagesAndVideoCompleted = true);
  }
}
