// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'image_self_store.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ImageSelfStore on _ImageSelfStore, Store {
  late final _$imagesAtom =
      Atom(name: '_ImageSelfStore.images', context: context);

  @override
  List<ImageSelf> get images {
    _$imagesAtom.reportRead();
    return super.images;
  }

  @override
  set images(List<ImageSelf> value) {
    _$imagesAtom.reportWrite(value, super.images, () {
      super.images = value;
    });
  }

  late final _$errorAtom =
      Atom(name: '_ImageSelfStore.error', context: context);

  @override
  dynamic get error {
    _$errorAtom.reportRead();
    return super.error;
  }

  @override
  set error(dynamic value) {
    _$errorAtom.reportWrite(value, super.error, () {
      super.error = value;
    });
  }

  late final _$fetchImageSuccessAtom =
      Atom(name: '_ImageSelfStore.fetchImageSuccess', context: context);

  @override
  bool get fetchImageSuccess {
    _$fetchImageSuccessAtom.reportRead();
    return super.fetchImageSuccess;
  }

  @override
  set fetchImageSuccess(bool value) {
    _$fetchImageSuccessAtom.reportWrite(value, super.fetchImageSuccess, () {
      super.fetchImageSuccess = value;
    });
  }

  late final _$uploadImageSuccessAtom =
      Atom(name: '_ImageSelfStore.uploadImageSuccess', context: context);

  @override
  bool get uploadImageSuccess {
    _$uploadImageSuccessAtom.reportRead();
    return super.uploadImageSuccess;
  }

  @override
  set uploadImageSuccess(bool value) {
    _$uploadImageSuccessAtom.reportWrite(value, super.uploadImageSuccess, () {
      super.uploadImageSuccess = value;
    });
  }

  late final _$removeImageSuccessAtom =
      Atom(name: '_ImageSelfStore.removeImageSuccess', context: context);

  @override
  bool get removeImageSuccess {
    _$removeImageSuccessAtom.reportRead();
    return super.removeImageSuccess;
  }

  @override
  set removeImageSuccess(bool value) {
    _$removeImageSuccessAtom.reportWrite(value, super.removeImageSuccess, () {
      super.removeImageSuccess = value;
    });
  }

  late final _$fetchImagesAsyncAction =
      AsyncAction('_ImageSelfStore.fetchImages', context: context);

  @override
  Future<dynamic> fetchImages() {
    return _$fetchImagesAsyncAction.run(() => super.fetchImages());
  }

  late final _$uploadImagesAsyncAction =
      AsyncAction('_ImageSelfStore.uploadImages', context: context);

  @override
  Future<dynamic> uploadImages(List<ImageSelf> images) {
    return _$uploadImagesAsyncAction.run(() => super.uploadImages(images));
  }

  late final _$removeImageAsyncAction =
      AsyncAction('_ImageSelfStore.removeImage', context: context);

  @override
  Future<dynamic> removeImage(ImageSelf image) {
    return _$removeImageAsyncAction.run(() => super.removeImage(image));
  }

  @override
  String toString() {
    return '''
images: ${images},
error: ${error},
fetchImageSuccess: ${fetchImageSuccess},
uploadImageSuccess: ${uploadImageSuccess},
removeImageSuccess: ${removeImageSuccess}
    ''';
  }
}
