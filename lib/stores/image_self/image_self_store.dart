import 'package:mobx/mobx.dart';
import 'package:tutorO/data/repository/profile_repository.dart';
import 'package:tutorO/models/profile/image_self.dart';
import 'package:tutorO/stores/error/error_store.dart';
part 'image_self_store.g.dart';

class ImageSelfStore = _ImageSelfStore with _$ImageSelfStore;

abstract class _ImageSelfStore with Store {
  ProfileRepository repository;

  // store for handling errors
  final ErrorStore errorStore = ErrorStore();

  _ImageSelfStore(this.repository);

  @observable
  List<ImageSelf> images = [];

  @observable
  dynamic error;

  @observable
  bool fetchImageSuccess = false;

  @action
  Future fetchImages() async {
    fetchImageSuccess = false;
    error = null;
    final future = repository.fetchImages();
    future.then((images) {
      this.images = images;
    }).catchError((error) {
      this.error = error;
      print("fetchImages error: $error");
    }).whenComplete(() => fetchImageSuccess = true);
  }

  @observable
  bool uploadImageSuccess = false;

  @action
  Future uploadImages(List<ImageSelf> images) async {
    uploadImageSuccess = false;
    error = null;
    final future = repository.uploadImages(images);
    future.then((images) {
      this.images = images;
    }).catchError((error) {
      this.error = error;
      print("fetchImages error: $error");
    }).whenComplete(() => uploadImageSuccess = true);
  }

  @observable
  bool removeImageSuccess = false;

  @action
  Future removeImage(ImageSelf image) async {
    removeImageSuccess = false;
    error = null;
    final future = repository.removeImage(image);
    future.then((_) {
      var _images = images;
      _images.remove(image);
      images = _images;
    }).catchError((error) {
      this.error = error;
    }).whenComplete(() => removeImageSuccess = true);
  }
}
