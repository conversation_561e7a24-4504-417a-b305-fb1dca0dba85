// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'class_schedule_store.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ClassScheduleStore on _ClassScheduleStore, Store {
  Computed<bool>? _$isLoadingComputed;

  @override
  bool get isLoading =>
      (_$isLoadingComputed ??= Computed<bool>(() => super.isLoading,
              name: '_ClassScheduleStore.isLoading'))
          .value;

  late final _$availableClassesAtom =
      Atom(name: '_ClassScheduleStore.availableClasses', context: context);

  @override
  AvailableClasses get availableClasses {
    _$availableClassesAtom.reportRead();
    return super.availableClasses;
  }

  @override
  set availableClasses(AvailableClasses value) {
    _$availableClassesAtom.reportWrite(value, super.availableClasses, () {
      super.availableClasses = value;
    });
  }

  late final _$loadingFetchClassSchedulesAtom = Atom(
      name: '_ClassScheduleStore.loadingFetchClassSchedules', context: context);

  @override
  ObservableFuture<dynamic> get loadingFetchClassSchedules {
    _$loadingFetchClassSchedulesAtom.reportRead();
    return super.loadingFetchClassSchedules;
  }

  @override
  set loadingFetchClassSchedules(ObservableFuture<dynamic> value) {
    _$loadingFetchClassSchedulesAtom
        .reportWrite(value, super.loadingFetchClassSchedules, () {
      super.loadingFetchClassSchedules = value;
    });
  }

  late final _$loadingFetchClassCountTodayAtom = Atom(
      name: '_ClassScheduleStore.loadingFetchClassCountToday',
      context: context);

  @override
  ObservableFuture<dynamic> get loadingFetchClassCountToday {
    _$loadingFetchClassCountTodayAtom.reportRead();
    return super.loadingFetchClassCountToday;
  }

  @override
  set loadingFetchClassCountToday(ObservableFuture<dynamic> value) {
    _$loadingFetchClassCountTodayAtom
        .reportWrite(value, super.loadingFetchClassCountToday, () {
      super.loadingFetchClassCountToday = value;
    });
  }

  late final _$countClassTodayAtom =
      Atom(name: '_ClassScheduleStore.countClassToday', context: context);

  @override
  int get countClassToday {
    _$countClassTodayAtom.reportRead();
    return super.countClassToday;
  }

  @override
  set countClassToday(int value) {
    _$countClassTodayAtom.reportWrite(value, super.countClassToday, () {
      super.countClassToday = value;
    });
  }

  late final _$isGettingClassTodayCompleteAtom = Atom(
      name: '_ClassScheduleStore.isGettingClassTodayComplete',
      context: context);

  @override
  bool get isGettingClassTodayComplete {
    _$isGettingClassTodayCompleteAtom.reportRead();
    return super.isGettingClassTodayComplete;
  }

  @override
  set isGettingClassTodayComplete(bool value) {
    _$isGettingClassTodayCompleteAtom
        .reportWrite(value, super.isGettingClassTodayComplete, () {
      super.isGettingClassTodayComplete = value;
    });
  }

  late final _$requestRegisterClassCompletedAtom = Atom(
      name: '_ClassScheduleStore.requestRegisterClassCompleted',
      context: context);

  @override
  bool get requestRegisterClassCompleted {
    _$requestRegisterClassCompletedAtom.reportRead();
    return super.requestRegisterClassCompleted;
  }

  @override
  set requestRegisterClassCompleted(bool value) {
    _$requestRegisterClassCompletedAtom
        .reportWrite(value, super.requestRegisterClassCompleted, () {
      super.requestRegisterClassCompleted = value;
    });
  }

  late final _$errorRegisterClassAtom =
      Atom(name: '_ClassScheduleStore.errorRegisterClass', context: context);

  @override
  ClassScheduleError? get errorRegisterClass {
    _$errorRegisterClassAtom.reportRead();
    return super.errorRegisterClass;
  }

  @override
  set errorRegisterClass(ClassScheduleError? value) {
    _$errorRegisterClassAtom.reportWrite(value, super.errorRegisterClass, () {
      super.errorRegisterClass = value;
    });
  }

  late final _$requestCancelRegisterClassCompletedAtom = Atom(
      name: '_ClassScheduleStore.requestCancelRegisterClassCompleted',
      context: context);

  @override
  bool get requestCancelRegisterClassCompleted {
    _$requestCancelRegisterClassCompletedAtom.reportRead();
    return super.requestCancelRegisterClassCompleted;
  }

  @override
  set requestCancelRegisterClassCompleted(bool value) {
    _$requestCancelRegisterClassCompletedAtom
        .reportWrite(value, super.requestCancelRegisterClassCompleted, () {
      super.requestCancelRegisterClassCompleted = value;
    });
  }

  late final _$errorCancelRegisterClassAtom = Atom(
      name: '_ClassScheduleStore.errorCancelRegisterClass', context: context);

  @override
  ClassScheduleError? get errorCancelRegisterClass {
    _$errorCancelRegisterClassAtom.reportRead();
    return super.errorCancelRegisterClass;
  }

  @override
  set errorCancelRegisterClass(ClassScheduleError? value) {
    _$errorCancelRegisterClassAtom
        .reportWrite(value, super.errorCancelRegisterClass, () {
      super.errorCancelRegisterClass = value;
    });
  }

  late final _$fetchAvailableClassesAsyncAction = AsyncAction(
      '_ClassScheduleStore.fetchAvailableClasses',
      context: context);

  @override
  Future<dynamic> fetchAvailableClasses(ClassFilterModel? filter) {
    return _$fetchAvailableClassesAsyncAction
        .run(() => super.fetchAvailableClasses(filter));
  }

  late final _$fetchCountClassTodayAsyncAction =
      AsyncAction('_ClassScheduleStore.fetchCountClassToday', context: context);

  @override
  Future<dynamic> fetchCountClassToday() {
    return _$fetchCountClassTodayAsyncAction
        .run(() => super.fetchCountClassToday());
  }

  late final _$requestRegisterClassAsyncAction =
      AsyncAction('_ClassScheduleStore.requestRegisterClass', context: context);

  @override
  Future<dynamic> requestRegisterClass(int idClass) {
    return _$requestRegisterClassAsyncAction
        .run(() => super.requestRegisterClass(idClass));
  }

  late final _$requestCancelRegisterClassAsyncAction = AsyncAction(
      '_ClassScheduleStore.requestCancelRegisterClass',
      context: context);

  @override
  Future<dynamic> requestCancelRegisterClass(int idClass) {
    return _$requestCancelRegisterClassAsyncAction
        .run(() => super.requestCancelRegisterClass(idClass));
  }

  @override
  String toString() {
    return '''
availableClasses: ${availableClasses},
loadingFetchClassSchedules: ${loadingFetchClassSchedules},
loadingFetchClassCountToday: ${loadingFetchClassCountToday},
countClassToday: ${countClassToday},
isGettingClassTodayComplete: ${isGettingClassTodayComplete},
requestRegisterClassCompleted: ${requestRegisterClassCompleted},
errorRegisterClass: ${errorRegisterClass},
requestCancelRegisterClassCompleted: ${requestCancelRegisterClassCompleted},
errorCancelRegisterClass: ${errorCancelRegisterClass},
isLoading: ${isLoading}
    ''';
  }
}
