import 'package:mobx/mobx.dart';
import 'package:tutorO/data/repository/class_repository.dart';
import 'package:tutorO/models/class/available_classes/available_classes.dart';
import 'package:tutorO/models/class/class.dart';
import 'package:tutorO/models/class_filter/class_filter_model.dart';
part 'class_schedule_store.g.dart';

class ClassScheduleStore = _ClassScheduleStore with _$ClassScheduleStore;

abstract class _ClassScheduleStore with Store {
  final ClassRepository _classScheduleRepository;

  _ClassScheduleStore({required ClassRepository classScheduleRepository})
      : _classScheduleRepository = classScheduleRepository;

  @observable
  AvailableClasses availableClasses = AvailableClasses.empty();

  @observable
  ObservableFuture loadingFetchClassSchedules = ObservableFuture.value(null);

  @observable
  ObservableFuture loadingFetchClassCountToday = ObservableFuture.value(null);

  @computed
  bool get isLoading => [
        loadingFetchClassCountToday.status,
        loadingFetchClassSchedules.status
      ].contains(FutureStatus.pending);

  @action
  Future fetchAvailableClasses(ClassFilterModel? filter) async {
    final future =
        _classScheduleRepository.fetchAvailableClasses(filter?.toJson());
    loadingFetchClassCountToday = ObservableFuture(future);
    future.then((availableClasses) {
      if (filter == null || (filter.page ?? 1) == 1) {
        this.availableClasses = availableClasses;
      } else {
        var oldAvailableClasses = this.availableClasses;
        oldAvailableClasses.listClasses
            ?.addAll(availableClasses.listClasses ?? []);
        this.availableClasses = oldAvailableClasses;
      }
    }).catchError((error) {
      print("fetchClassSchedules error: $error");
    });
  }

  @observable
  int countClassToday = 0;
  @observable
  bool isGettingClassTodayComplete = false;
  @action
  Future fetchCountClassToday() async {
    final future = _classScheduleRepository.fetchCountClassToday();
    loadingFetchClassSchedules = ObservableFuture(future);
    future.then((countClassToday) {
      this.countClassToday = countClassToday;
    }).catchError((error) {
      print("fetchCountClassToday error: $error");
    }).whenComplete(() => isGettingClassTodayComplete = true);
  }

  @observable
  bool requestRegisterClassCompleted = false;

  @observable
  ClassScheduleError? errorRegisterClass;

  @action
  Future requestRegisterClass(int idClass) async {
    requestRegisterClassCompleted = false;
    errorRegisterClass = null;
    final future = _classScheduleRepository.requestRegisterClass(idClass);
    loadingFetchClassSchedules = ObservableFuture(future);
    future.then((result) {
      var registerAndConfirmed = result["registration_state"] == "APPROVE";
      if (availableClasses.listClasses != null && registerAndConfirmed) {
        availableClasses.listClasses!
            .removeWhere((item) => item.classId == idClass);
      } else {
        var classUpdate = availableClasses.listClasses!
            .firstWhere((item) => item.classId == idClass);
        classUpdate.updateRequestClassStatus(result["registration_state"]);
      }
    }).catchError((error) {
      errorRegisterClass = error as ClassScheduleError;
    }).whenComplete(() => requestRegisterClassCompleted = true);
  }

  @observable
  bool requestCancelRegisterClassCompleted = false;

  @observable
  ClassScheduleError? errorCancelRegisterClass;

  @action
  Future requestCancelRegisterClass(int idClass) async {
    requestCancelRegisterClassCompleted = false;
    errorRegisterClass = null;
    final future = _classScheduleRepository.requestCancelRegisterClass(idClass);
    loadingFetchClassSchedules = ObservableFuture(future);
    future.then((result) {
      var classUpdate = availableClasses.listClasses!
          .firstWhere((item) => item.classId == idClass);
      classUpdate.updateRequestClassStatus(result["registration_state"]);
    }).catchError((error) {
      errorCancelRegisterClass = error;
    }).whenComplete(() => requestCancelRegisterClassCompleted = true);
  }
}
