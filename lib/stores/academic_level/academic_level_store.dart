import 'dart:io';

import 'package:mobx/mobx.dart';
import 'package:tutorO/data/repository/academic_level_repository.dart';
import 'package:tutorO/models/academic_level/academic_level.dart';
import 'package:tutorO/utils/image/image.dart';
part 'academic_level_store.g.dart';

class AcademicLevelStore = _AcademicLevelStore with _$AcademicLevelStore;

abstract class _AcademicLevelStore with Store {
  final AcademicLevelRepository _academicLevelRepository;

  _AcademicLevelStore(
      {required AcademicLevelRepository academicLevelRepository})
      : _academicLevelRepository = academicLevelRepository;

  @observable
  ObservableFuture<List<AcademicLevel>> fetchAcademicLevelsFuture =
      ObservableFuture.value([]);

  @observable
  ObservableFuture<AcademicLevel?> addAcademicLevelFuture =
      ObservableFuture.value(null);

  @observable
  ObservableFuture<AcademicLevel?> editAcademicLevelFuture =
      ObservableFuture.value(null);

  @observable
  ObservableFuture<bool?> deleteAcademicLevelFuture =
      ObservableFuture.value(null);

  @observable
  ObservableList<AcademicLevel> arrAcademicLevels = ObservableList.of([]);

  @observable
  AcademicLevelError? errAddAcademicLevel;
  @observable
  bool isComplete = false;
  @computed
  bool get fetchingAcademicLevels =>
      fetchAcademicLevelsFuture.status == FutureStatus.pending;

  @computed
  bool get requesting =>
      addAcademicLevelFuture.status == FutureStatus.pending ||
      editAcademicLevelFuture.status == FutureStatus.pending ||
      deleteAcademicLevelFuture.status == FutureStatus.pending;

  @computed
  bool get addAcademicLevelSuccessful =>
      addAcademicLevelFuture.status == FutureStatus.fulfilled;

  @computed
  bool get editingAcademicLevelSuccessful =>
      editAcademicLevelFuture.status == FutureStatus.fulfilled;

  @computed
  bool get deleteAcademicLevelSuccessful =>
      deleteAcademicLevelFuture.status == FutureStatus.fulfilled;

  @action
  Future fetchAcademicLevels() async {
    final future = _academicLevelRepository.fetchAcademicLevels();
    fetchAcademicLevelsFuture = ObservableFuture(future);

    future.then((academicLevels) {
      arrAcademicLevels.clear();
      arrAcademicLevels.addAll(academicLevels);
    }).catchError((error) {
      print("fetchAcademicLevels error: $error");
    });
  }

  @action
  Future addAcademicLevel(
      AcademicLevel newAcademicLevel, File academicFile) async {
    errAddAcademicLevel = null;
    final future = _academicLevelRepository.addAcademicLevel(
        newAcademicLevel, await ImageConvert.compressFile(academicFile));
    addAcademicLevelFuture = ObservableFuture(future);
    future.then((academicLevel) {
      arrAcademicLevels.add(academicLevel);
    }).catchError((error) {
      print("addAcademicLevel error: $error");
      if (error is AcademicLevelError) {
        errAddAcademicLevel = error;
      }
    });
  }

  @action
  Future editAcademicLevel(
      AcademicLevel editAcademicLevel, File? academicFile) async {
    errAddAcademicLevel = null;
    final future = _academicLevelRepository.editAcademicLevel(
        editAcademicLevel,
        academicFile != null
            ? await ImageConvert.compressFile(academicFile)
            : null);
    editAcademicLevelFuture = ObservableFuture(future);

    future.then((academicLevel) {
      final replaceIndex = arrAcademicLevels
          .indexWhere((element) => element.id == editAcademicLevel.id);
      if (replaceIndex >= 0) {
        arrAcademicLevels[replaceIndex] = academicLevel;
      }
    }).catchError((error) {
      print("editAcademicLevel error: $error");
      if (error is AcademicLevelError) {
        errAddAcademicLevel = error;
      }
    });
  }

  @action
  Future deleteAcademicLevel(int id) async {
    errAddAcademicLevel = null;
    final future = _academicLevelRepository.deleteAcademicLevel(id);
    deleteAcademicLevelFuture = ObservableFuture(future);
    future.then((_) {
      arrAcademicLevels.removeWhere((element) => element.id == id);
    }).catchError((error) {
      print("editAcademicLevel error: $error");
      if (error is AcademicLevelError) {
        errAddAcademicLevel = error;
      }
    });
  }
}
