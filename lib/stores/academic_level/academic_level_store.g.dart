// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'academic_level_store.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$AcademicLevelStore on _AcademicLevelStore, Store {
  Computed<bool>? _$fetchingAcademicLevelsComputed;

  @override
  bool get fetchingAcademicLevels => (_$fetchingAcademicLevelsComputed ??=
          Computed<bool>(() => super.fetchingAcademicLevels,
              name: '_AcademicLevelStore.fetchingAcademicLevels'))
      .value;
  Computed<bool>? _$requestingComputed;

  @override
  bool get requesting =>
      (_$requestingComputed ??= Computed<bool>(() => super.requesting,
              name: '_AcademicLevelStore.requesting'))
          .value;
  Computed<bool>? _$addAcademicLevelSuccessfulComputed;

  @override
  bool get addAcademicLevelSuccessful =>
      (_$addAcademicLevelSuccessfulComputed ??= Computed<bool>(
              () => super.addAcademicLevelSuccessful,
              name: '_AcademicLevelStore.addAcademicLevelSuccessful'))
          .value;
  Computed<bool>? _$editingAcademicLevelSuccessfulComputed;

  @override
  bool get editingAcademicLevelSuccessful =>
      (_$editingAcademicLevelSuccessfulComputed ??= Computed<bool>(
              () => super.editingAcademicLevelSuccessful,
              name: '_AcademicLevelStore.editingAcademicLevelSuccessful'))
          .value;
  Computed<bool>? _$deleteAcademicLevelSuccessfulComputed;

  @override
  bool get deleteAcademicLevelSuccessful =>
      (_$deleteAcademicLevelSuccessfulComputed ??= Computed<bool>(
              () => super.deleteAcademicLevelSuccessful,
              name: '_AcademicLevelStore.deleteAcademicLevelSuccessful'))
          .value;

  late final _$fetchAcademicLevelsFutureAtom = Atom(
      name: '_AcademicLevelStore.fetchAcademicLevelsFuture', context: context);

  @override
  ObservableFuture<List<AcademicLevel>> get fetchAcademicLevelsFuture {
    _$fetchAcademicLevelsFutureAtom.reportRead();
    return super.fetchAcademicLevelsFuture;
  }

  @override
  set fetchAcademicLevelsFuture(ObservableFuture<List<AcademicLevel>> value) {
    _$fetchAcademicLevelsFutureAtom
        .reportWrite(value, super.fetchAcademicLevelsFuture, () {
      super.fetchAcademicLevelsFuture = value;
    });
  }

  late final _$addAcademicLevelFutureAtom = Atom(
      name: '_AcademicLevelStore.addAcademicLevelFuture', context: context);

  @override
  ObservableFuture<AcademicLevel?> get addAcademicLevelFuture {
    _$addAcademicLevelFutureAtom.reportRead();
    return super.addAcademicLevelFuture;
  }

  @override
  set addAcademicLevelFuture(ObservableFuture<AcademicLevel?> value) {
    _$addAcademicLevelFutureAtom
        .reportWrite(value, super.addAcademicLevelFuture, () {
      super.addAcademicLevelFuture = value;
    });
  }

  late final _$editAcademicLevelFutureAtom = Atom(
      name: '_AcademicLevelStore.editAcademicLevelFuture', context: context);

  @override
  ObservableFuture<AcademicLevel?> get editAcademicLevelFuture {
    _$editAcademicLevelFutureAtom.reportRead();
    return super.editAcademicLevelFuture;
  }

  @override
  set editAcademicLevelFuture(ObservableFuture<AcademicLevel?> value) {
    _$editAcademicLevelFutureAtom
        .reportWrite(value, super.editAcademicLevelFuture, () {
      super.editAcademicLevelFuture = value;
    });
  }

  late final _$deleteAcademicLevelFutureAtom = Atom(
      name: '_AcademicLevelStore.deleteAcademicLevelFuture', context: context);

  @override
  ObservableFuture<bool?> get deleteAcademicLevelFuture {
    _$deleteAcademicLevelFutureAtom.reportRead();
    return super.deleteAcademicLevelFuture;
  }

  @override
  set deleteAcademicLevelFuture(ObservableFuture<bool?> value) {
    _$deleteAcademicLevelFutureAtom
        .reportWrite(value, super.deleteAcademicLevelFuture, () {
      super.deleteAcademicLevelFuture = value;
    });
  }

  late final _$arrAcademicLevelsAtom =
      Atom(name: '_AcademicLevelStore.arrAcademicLevels', context: context);

  @override
  ObservableList<AcademicLevel> get arrAcademicLevels {
    _$arrAcademicLevelsAtom.reportRead();
    return super.arrAcademicLevels;
  }

  @override
  set arrAcademicLevels(ObservableList<AcademicLevel> value) {
    _$arrAcademicLevelsAtom.reportWrite(value, super.arrAcademicLevels, () {
      super.arrAcademicLevels = value;
    });
  }

  late final _$errAddAcademicLevelAtom =
      Atom(name: '_AcademicLevelStore.errAddAcademicLevel', context: context);

  @override
  AcademicLevelError? get errAddAcademicLevel {
    _$errAddAcademicLevelAtom.reportRead();
    return super.errAddAcademicLevel;
  }

  @override
  set errAddAcademicLevel(AcademicLevelError? value) {
    _$errAddAcademicLevelAtom.reportWrite(value, super.errAddAcademicLevel, () {
      super.errAddAcademicLevel = value;
    });
  }

  late final _$isCompleteAtom =
      Atom(name: '_AcademicLevelStore.isComplete', context: context);

  @override
  bool get isComplete {
    _$isCompleteAtom.reportRead();
    return super.isComplete;
  }

  @override
  set isComplete(bool value) {
    _$isCompleteAtom.reportWrite(value, super.isComplete, () {
      super.isComplete = value;
    });
  }

  late final _$fetchAcademicLevelsAsyncAction =
      AsyncAction('_AcademicLevelStore.fetchAcademicLevels', context: context);

  @override
  Future<dynamic> fetchAcademicLevels() {
    return _$fetchAcademicLevelsAsyncAction
        .run(() => super.fetchAcademicLevels());
  }

  late final _$addAcademicLevelAsyncAction =
      AsyncAction('_AcademicLevelStore.addAcademicLevel', context: context);

  @override
  Future<dynamic> addAcademicLevel(
      AcademicLevel newAcademicLevel, File academicFile) {
    return _$addAcademicLevelAsyncAction
        .run(() => super.addAcademicLevel(newAcademicLevel, academicFile));
  }

  late final _$editAcademicLevelAsyncAction =
      AsyncAction('_AcademicLevelStore.editAcademicLevel', context: context);

  @override
  Future<dynamic> editAcademicLevel(
      AcademicLevel editAcademicLevel, File? academicFile) {
    return _$editAcademicLevelAsyncAction
        .run(() => super.editAcademicLevel(editAcademicLevel, academicFile));
  }

  late final _$deleteAcademicLevelAsyncAction =
      AsyncAction('_AcademicLevelStore.deleteAcademicLevel', context: context);

  @override
  Future<dynamic> deleteAcademicLevel(int id) {
    return _$deleteAcademicLevelAsyncAction
        .run(() => super.deleteAcademicLevel(id));
  }

  @override
  String toString() {
    return '''
fetchAcademicLevelsFuture: ${fetchAcademicLevelsFuture},
addAcademicLevelFuture: ${addAcademicLevelFuture},
editAcademicLevelFuture: ${editAcademicLevelFuture},
deleteAcademicLevelFuture: ${deleteAcademicLevelFuture},
arrAcademicLevels: ${arrAcademicLevels},
errAddAcademicLevel: ${errAddAcademicLevel},
isComplete: ${isComplete},
fetchingAcademicLevels: ${fetchingAcademicLevels},
requesting: ${requesting},
addAcademicLevelSuccessful: ${addAcademicLevelSuccessful},
editingAcademicLevelSuccessful: ${editingAcademicLevelSuccessful},
deleteAcademicLevelSuccessful: ${deleteAcademicLevelSuccessful}
    ''';
  }
}
