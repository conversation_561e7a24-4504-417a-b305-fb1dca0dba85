// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'job_info_store.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$JobInfoStore on _JobInfoStore, Store {
  late final _$successAtom =
      Atom(name: '_JobInfoStore.success', context: context);

  @override
  bool get success {
    _$successAtom.reportRead();
    return super.success;
  }

  @override
  set success(bool value) {
    _$successAtom.reportWrite(value, super.success, () {
      super.success = value;
    });
  }

  late final _$fetchJobInfoAsyncAction =
      AsyncAction('_JobInfoStore.fetchJobInfo', context: context);

  @override
  Future<dynamic> fetchJobInfo() {
    return _$fetchJobInfoAsyncAction.run(() => super.fetchJobInfo());
  }

  @override
  String toString() {
    return '''
success: ${success}
    ''';
  }
}
