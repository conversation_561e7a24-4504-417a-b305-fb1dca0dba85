import 'package:mobx/mobx.dart';
import 'package:tutorO/data/repository/profile_repository.dart';
import 'package:tutorO/models/profile/job_info.dart';
import 'package:tutorO/stores/error/error_store.dart';
part 'job_info_store.g.dart';

class JobInfoStore = _JobInfoStore with _$JobInfoStore;

abstract class _JobInfoStore with Store {
  ProfileRepository repository;

  // store for handling errors
  final ErrorStore errorStore = ErrorStore();

  _JobInfoStore(this.repository);

  JobInfoModel jobInfo = JobInfoModel.empty();

  @observable
  bool success = false;

  // actions:-------------------------------------------------------------------
  @action
  Future fetchJobInfo() async {
    success = false;
    return repository.fetchJobInfo().then((value) {
      jobInfo = value;
      return jobInfo;
    }).whenComplete(() => success = true);
  }
}
