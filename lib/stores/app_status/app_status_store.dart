import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:mobx/mobx.dart';
import 'package:platform_device_id/platform_device_id.dart';
import 'package:tutorO/data/repository/repository.dart';
import 'package:tutorO/stores/error/error_store.dart';
import 'package:tutorO/utils/device/device_utils.dart';
import 'package:tutorO/utils/dio/dio_error_util.dart';

part 'app_status_store.g.dart';

class AppStatusStore = _AppStatusStore with _$AppStatusStore;

abstract class _AppStatusStore with Store {
  _AppStatusStore({required this.repository}) {
    connectivityStream =
        ObservableStream(repository.getConnectivity().onConnectivityChanged);

    initializeFirebaseDone = false;
  }
  final ErrorStore errorStore = ErrorStore();
  @observable
  ObservableStream<ConnectivityResult>? connectivityStream;
  @observable
  bool initializeFirebaseDone = false;
  Repository repository;

  @action
  Future requestNotificationPermission() async {
    var _messaging = FirebaseMessaging.instance;

    var settings = await _messaging.requestPermission(
        alert: true, badge: true, sound: false);
    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      print('User granted permission');

      if (Platform.isMacOS) {
        var _token = await _messaging.getToken();
        if (_token == null) {
          print("failed to get FCM token");
        } else {
          print('FCM Token: $_token');
          repository.saveFCMToken(_token);
        }
      }
    } else if (settings.authorizationStatus ==
        AuthorizationStatus.provisional) {
      print('User granted provisional permission');
    } else {
      print('User declined or has not accepted permission');
    }
  }

  @action
  Future getDeviceId() async {
    final future = PlatformDeviceId.getDeviceId;

    future.then((deviceId) {
      if (deviceId != null) {
        print("DeviceId:$deviceId");
        repository.saveDeviceId(deviceId);
      } else {
        print('Failed to get device id');
      }
    }).catchError((error) {
      print('Failed to get device id');
    });
  }

  @action
  Future initializeFlutterFire() async {
    final future = repository.initFirebase();

    future
        .then((value) => print('Initialize Firebase result: $value'))
        .catchError((error) =>
            errorStore.errorMessage = DioErrorUtil.handleError(error))
        .whenComplete(() => initializeFirebaseDone = true);
  }
}
