// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_status_store.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$AppStatusStore on _AppStatusStore, Store {
  late final _$connectivityStreamAtom =
      Atom(name: '_AppStatusStore.connectivityStream', context: context);

  @override
  ObservableStream<ConnectivityResult>? get connectivityStream {
    _$connectivityStreamAtom.reportRead();
    return super.connectivityStream;
  }

  @override
  set connectivityStream(ObservableStream<ConnectivityResult>? value) {
    _$connectivityStreamAtom.reportWrite(value, super.connectivityStream, () {
      super.connectivityStream = value;
    });
  }

  late final _$initializeFirebaseDoneAtom =
      Atom(name: '_AppStatusStore.initializeFirebaseDone', context: context);

  @override
  bool get initializeFirebaseDone {
    _$initializeFirebaseDoneAtom.reportRead();
    return super.initializeFirebaseDone;
  }

  @override
  set initializeFirebaseDone(bool value) {
    _$initializeFirebaseDoneAtom
        .reportWrite(value, super.initializeFirebaseDone, () {
      super.initializeFirebaseDone = value;
    });
  }

  late final _$requestNotificationPermissionAsyncAction = AsyncAction(
      '_AppStatusStore.requestNotificationPermission',
      context: context);

  @override
  Future<dynamic> requestNotificationPermission() {
    return _$requestNotificationPermissionAsyncAction
        .run(() => super.requestNotificationPermission());
  }

  late final _$getDeviceIdAsyncAction =
      AsyncAction('_AppStatusStore.getDeviceId', context: context);

  @override
  Future<dynamic> getDeviceId() {
    return _$getDeviceIdAsyncAction.run(() => super.getDeviceId());
  }

  late final _$initializeFlutterFireAsyncAction =
      AsyncAction('_AppStatusStore.initializeFlutterFire', context: context);

  @override
  Future<dynamic> initializeFlutterFire() {
    return _$initializeFlutterFireAsyncAction
        .run(() => super.initializeFlutterFire());
  }

  @override
  String toString() {
    return '''
connectivityStream: ${connectivityStream},
initializeFirebaseDone: ${initializeFirebaseDone}
    ''';
  }
}
