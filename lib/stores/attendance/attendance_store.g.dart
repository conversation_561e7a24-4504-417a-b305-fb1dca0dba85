// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'attendance_store.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$AttendanceStore on _AttendanceStore, Store {
  Computed<bool>? _$isLoadingComputed;

  @override
  bool get isLoading =>
      (_$isLoadingComputed ??= Computed<bool>(() => super.isLoading,
              name: '_AttendanceStore.isLoading'))
          .value;

  late final _$listAttendanceAtom =
      Atom(name: '_AttendanceStore.listAttendance', context: context);

  @override
  ListAttendance get listAttendance {
    _$listAttendanceAtom.reportRead();
    return super.listAttendance;
  }

  @override
  set listAttendance(ListAttendance value) {
    _$listAttendanceAtom.reportWrite(value, super.listAttendance, () {
      super.listAttendance = value;
    });
  }

  late final _$fetchAttendancesErrorAtom =
      Atom(name: '_AttendanceStore.fetchAttendancesError', context: context);

  @override
  AttendanceError? get fetchAttendancesError {
    _$fetchAttendancesErrorAtom.reportRead();
    return super.fetchAttendancesError;
  }

  @override
  set fetchAttendancesError(AttendanceError? value) {
    _$fetchAttendancesErrorAtom.reportWrite(value, super.fetchAttendancesError,
        () {
      super.fetchAttendancesError = value;
    });
  }

  late final _$fetchAttendancesCompletedAtom = Atom(
      name: '_AttendanceStore.fetchAttendancesCompleted', context: context);

  @override
  bool get fetchAttendancesCompleted {
    _$fetchAttendancesCompletedAtom.reportRead();
    return super.fetchAttendancesCompleted;
  }

  @override
  set fetchAttendancesCompleted(bool value) {
    _$fetchAttendancesCompletedAtom
        .reportWrite(value, super.fetchAttendancesCompleted, () {
      super.fetchAttendancesCompleted = value;
    });
  }

  late final _$listAllStudentAtom =
      Atom(name: '_AttendanceStore.listAllStudent', context: context);

  @override
  List<StudentAttendance> get listAllStudent {
    _$listAllStudentAtom.reportRead();
    return super.listAllStudent;
  }

  @override
  set listAllStudent(List<StudentAttendance> value) {
    _$listAllStudentAtom.reportWrite(value, super.listAllStudent, () {
      super.listAllStudent = value;
    });
  }

  late final _$centerInfoAtom =
      Atom(name: '_AttendanceStore.centerInfo', context: context);

  @override
  Attendance? get centerInfo {
    _$centerInfoAtom.reportRead();
    return super.centerInfo;
  }

  @override
  set centerInfo(Attendance? value) {
    _$centerInfoAtom.reportWrite(value, super.centerInfo, () {
      super.centerInfo = value;
    });
  }

  late final _$attendanceStudentErrorAtom =
      Atom(name: '_AttendanceStore.attendanceStudentError', context: context);

  @override
  AttendanceError? get attendanceStudentError {
    _$attendanceStudentErrorAtom.reportRead();
    return super.attendanceStudentError;
  }

  @override
  set attendanceStudentError(AttendanceError? value) {
    _$attendanceStudentErrorAtom
        .reportWrite(value, super.attendanceStudentError, () {
      super.attendanceStudentError = value;
    });
  }

  late final _$attendanceStudentCompletedAtom = Atom(
      name: '_AttendanceStore.attendanceStudentCompleted', context: context);

  @override
  bool get attendanceStudentCompleted {
    _$attendanceStudentCompletedAtom.reportRead();
    return super.attendanceStudentCompleted;
  }

  @override
  set attendanceStudentCompleted(bool value) {
    _$attendanceStudentCompletedAtom
        .reportWrite(value, super.attendanceStudentCompleted, () {
      super.attendanceStudentCompleted = value;
    });
  }

  late final _$attendanceStudentFutureAtom =
      Atom(name: '_AttendanceStore.attendanceStudentFuture', context: context);

  @override
  ObservableFuture<StudentAttendance?> get attendanceStudentFuture {
    _$attendanceStudentFutureAtom.reportRead();
    return super.attendanceStudentFuture;
  }

  @override
  set attendanceStudentFuture(ObservableFuture<StudentAttendance?> value) {
    _$attendanceStudentFutureAtom
        .reportWrite(value, super.attendanceStudentFuture, () {
      super.attendanceStudentFuture = value;
    });
  }

  late final _$fetchAttendancesAsyncAction =
      AsyncAction('_AttendanceStore.fetchAttendances', context: context);

  @override
  Future<dynamic> fetchAttendances(int sessionId, String languageCode) {
    return _$fetchAttendancesAsyncAction
        .run(() => super.fetchAttendances(sessionId, languageCode));
  }

  late final _$attendanceStudentAsyncAction =
      AsyncAction('_AttendanceStore.attendanceStudent', context: context);

  @override
  Future<dynamic> attendanceStudent(int sessionId, int studentId,
      {required bool status}) {
    return _$attendanceStudentAsyncAction.run(
        () => super.attendanceStudent(sessionId, studentId, status: status));
  }

  @override
  String toString() {
    return '''
listAttendance: ${listAttendance},
fetchAttendancesError: ${fetchAttendancesError},
fetchAttendancesCompleted: ${fetchAttendancesCompleted},
listAllStudent: ${listAllStudent},
centerInfo: ${centerInfo},
attendanceStudentError: ${attendanceStudentError},
attendanceStudentCompleted: ${attendanceStudentCompleted},
attendanceStudentFuture: ${attendanceStudentFuture},
isLoading: ${isLoading}
    ''';
  }
}
