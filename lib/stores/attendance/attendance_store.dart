import 'package:mobx/mobx.dart';
import 'package:tutorO/data/repository/class_repository.dart';

import '../../models/attendance/attendance.dart';

// Include generated file
part 'attendance_store.g.dart';

class AttendanceStore = _AttendanceStore with _$AttendanceStore;

// The store-class
abstract class _AttendanceStore with Store {
  final ClassRepository _classRepository;

  _AttendanceStore({required ClassRepository classRepository})
      : _classRepository = classRepository;

  @observable
  ListAttendance listAttendance = ListAttendance.empty();

  @observable
  AttendanceError? fetchAttendancesError;

  @observable
  bool fetchAttendancesCompleted = false;

  @observable
  List<StudentAttendance> listAllStudent = [];
  @observable
  Attendance? centerInfo;
  @action
  Future fetchAttendances(int sessionId, String languageCode) async {
    fetchAttendancesCompleted = false;

    print("languageCode $languageCode");
    listAttendance = await _classRepository.fetchAttendances(sessionId, languageCode);
     listAllStudent.clear();
      for (var centerInfo in listAttendance.listAttendance!) {
        for (var s in centerInfo.listStudents!) {
          final value = await _classRepository.fetchStudentPortfolioUuid(sessionId, s.studentId!);
          s.validPortfolio = await _classRepository.fetchPortfolioState(value);
          listAllStudent.add(s);
        }
      }
      fetchAttendancesCompleted = true;
  }

  @observable
  AttendanceError? attendanceStudentError;

  @observable
  bool attendanceStudentCompleted = false;

  @observable
  ObservableFuture<StudentAttendance?> attendanceStudentFuture =
      ObservableFuture.value(null);

  @computed
  bool get isLoading => attendanceStudentFuture.status == FutureStatus.pending;

  @action
  Future attendanceStudent(int sessionId, int studentId,
      {required bool status}) async {
    final future = _classRepository.attendanceStudent(sessionId, studentId,
        status: status);
    attendanceStudentFuture = ObservableFuture(future);
    attendanceStudentCompleted = false;
    attendanceStudentError = null;
    future.then((value) {
      var tempListAttendance = listAttendance;
      for (var item in tempListAttendance.listAttendance!) {
        var index = item.listStudents
            ?.indexWhere((element) => element.studentId == studentId);
        if (index! > -1 && item.listStudents != null) {
          item.listStudents![index].attendanceStatus = value.attendanceStatus;
        }
      }
      listAttendance = tempListAttendance;
    }).catchError((error) {
      print(error);
      attendanceStudentError = error;
    }).whenComplete(() => attendanceStudentCompleted = true);
  }
}
