import 'package:mobx/mobx.dart';
import 'package:tutorO/data/repository/class_repository.dart';
import 'package:tutorO/models/class/available_classes/available_classes.dart';
import '../../models/class_session/class_session.dart';
part 'teaching_manage_store.g.dart';

class TeachingManageStore = _TeachingManageStore with _$TeachingManageStore;

abstract class _TeachingManageStore with Store {
  final ClassRepository _classRepository;

  _TeachingManageStore({required ClassRepository classRepository})
      : _classRepository = classRepository;

  @computed
  bool get haveError =>
      errFetchClassSessionToday != null || errFetchListClassTeaching != null;

  @computed
  bool get isLoading => [
        loadingFetchClassSessionToday.status,
        loadingFetchListClassTeaching.status,
        loadingFetchClassSessionSpecificDay.status
      ].contains(FutureStatus.pending);
  @observable
  bool loadingComplete = false;
  @observable
  bool isLoadingClassTeachingComplete = false;
  @observable
  bool isLoadingClassTodayComplete = false;
  @computed
  bool get isGettingListSession => [
        loadingFetchClassSessionToday.status,
        loadingFetchClassSessionSpecificDay.status
      ].contains(FutureStatus.pending);
  @observable
  ClassSessions listClassSessionsToday = ClassSessions.empty();
  @observable
  ClassSessions listClassSessionsIncoming = ClassSessions.empty();

  @observable
  ObservableFuture loadingFetchClassSessionToday = ObservableFuture.value(null);

  @observable
  dynamic errFetchClassSessionToday;
  @observable
  bool isButtonVisible = false;
  @action
  setButtonVisible(bool value) {
    isButtonVisible = value;
  }

  @action
  Future fetchClassToday(Map<String, dynamic> query) async {
    isLoadingClassTodayComplete = false;
    final future = _classRepository.fetchClassSession(query);
    errFetchClassSessionToday = null;
    loadingFetchClassSessionToday = ObservableFuture(future);
    return future.then((listClassSessions) {
      listClassSessionsToday = listClassSessions;
      return listClassSessionsToday;
    }).catchError((error) {
      errFetchClassSessionToday = error as ClassSessionsError;
    }).whenComplete(() => isLoadingClassTodayComplete = true);
  }

  @action
  Future fetchClassIncoming(Map<String, dynamic> query) async {
    loadingComplete = false;
    final future = _classRepository.fetchClassSession(query);
    errFetchClassSessionToday = null;
    // loadingFetchClassSessionToday = ObservableFuture(future);
    return future.then((listClassSessions) {
      listClassSessionsIncoming = listClassSessions;
      return listClassSessionsIncoming;
    }).whenComplete(() => loadingComplete = true);
  }

  @observable
  ClassSessions listClassSessionsSpecificDay = ClassSessions.empty();

  @observable
  ObservableFuture loadingFetchClassSessionSpecificDay =
      ObservableFuture.value(null);

  @observable
  dynamic errFetchClassSessionSpecificDay;
  @observable
  bool isLoadingWeeklyClass = false;
  @observable
  bool isLoadingWeeklyClassComplete = false;
  @observable
  bool isLockedSwapPage = false;

  @action
  Future fetchClassSpecificDay(Map<String, dynamic> query) async {
    final future = _classRepository.fetchClassSession(query);
    isLockedSwapPage = true;
    isLoadingWeeklyClassComplete = false;
    isLoadingWeeklyClass = true;
    errFetchClassSessionSpecificDay = null;
    loadingFetchClassSessionSpecificDay = ObservableFuture(future);
    future.then((listClassSessionsSpecificDay) {
      this.listClassSessionsSpecificDay = listClassSessionsSpecificDay;
    }).whenComplete(() {
      isLoadingWeeklyClass = false;
      isLoadingWeeklyClassComplete = true;
      isLockedSwapPage = false;
    });
  }

  @observable
  AvailableClasses listClassTeaching = AvailableClasses.empty();

  @observable
  ObservableFuture loadingFetchListClassTeaching = ObservableFuture.value(null);

  @observable
  dynamic errFetchListClassTeaching;
  @action
  setupClearFunction() {
    isLoadingClassTeachingComplete = false;
    isLoadingClassTodayComplete = false;
    isLoadingWeeklyClassComplete = false;
  }

  @action
  Future fetchClassTeaching() async {
    isLoadingClassTeachingComplete = false;
    final future = _classRepository.fetchClassTeaching();
    errFetchListClassTeaching = null;
    loadingFetchListClassTeaching = ObservableFuture(future);
    future.then((listClassTeaching) {
      this.listClassTeaching = listClassTeaching;
    }).catchError((error) {
      errFetchListClassTeaching = error as AvailableClassesError;
    }).whenComplete(() => isLoadingClassTeachingComplete = true);
  }
}
