// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'teaching_manage_store.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$TeachingManageStore on _TeachingManageStore, Store {
  Computed<bool>? _$haveErrorComputed;

  @override
  bool get haveError =>
      (_$haveErrorComputed ??= Computed<bool>(() => super.haveError,
              name: '_TeachingManageStore.haveError'))
          .value;
  Computed<bool>? _$isLoadingComputed;

  @override
  bool get isLoading =>
      (_$isLoadingComputed ??= Computed<bool>(() => super.isLoading,
              name: '_TeachingManageStore.isLoading'))
          .value;
  Computed<bool>? _$isGettingListSessionComputed;

  @override
  bool get isGettingListSession => (_$isGettingListSessionComputed ??=
          Computed<bool>(() => super.isGettingListSession,
              name: '_TeachingManageStore.isGettingListSession'))
      .value;

  late final _$loadingCompleteAtom =
      Atom(name: '_TeachingManageStore.loadingComplete', context: context);

  @override
  bool get loadingComplete {
    _$loadingCompleteAtom.reportRead();
    return super.loadingComplete;
  }

  @override
  set loadingComplete(bool value) {
    _$loadingCompleteAtom.reportWrite(value, super.loadingComplete, () {
      super.loadingComplete = value;
    });
  }

  late final _$isLoadingClassTeachingCompleteAtom = Atom(
      name: '_TeachingManageStore.isLoadingClassTeachingComplete',
      context: context);

  @override
  bool get isLoadingClassTeachingComplete {
    _$isLoadingClassTeachingCompleteAtom.reportRead();
    return super.isLoadingClassTeachingComplete;
  }

  @override
  set isLoadingClassTeachingComplete(bool value) {
    _$isLoadingClassTeachingCompleteAtom
        .reportWrite(value, super.isLoadingClassTeachingComplete, () {
      super.isLoadingClassTeachingComplete = value;
    });
  }

  late final _$isLoadingClassTodayCompleteAtom = Atom(
      name: '_TeachingManageStore.isLoadingClassTodayComplete',
      context: context);

  @override
  bool get isLoadingClassTodayComplete {
    _$isLoadingClassTodayCompleteAtom.reportRead();
    return super.isLoadingClassTodayComplete;
  }

  @override
  set isLoadingClassTodayComplete(bool value) {
    _$isLoadingClassTodayCompleteAtom
        .reportWrite(value, super.isLoadingClassTodayComplete, () {
      super.isLoadingClassTodayComplete = value;
    });
  }

  late final _$listClassSessionsTodayAtom = Atom(
      name: '_TeachingManageStore.listClassSessionsToday', context: context);

  @override
  ClassSessions get listClassSessionsToday {
    _$listClassSessionsTodayAtom.reportRead();
    return super.listClassSessionsToday;
  }

  @override
  set listClassSessionsToday(ClassSessions value) {
    _$listClassSessionsTodayAtom
        .reportWrite(value, super.listClassSessionsToday, () {
      super.listClassSessionsToday = value;
    });
  }

  late final _$listClassSessionsIncomingAtom = Atom(
      name: '_TeachingManageStore.listClassSessionsIncoming', context: context);

  @override
  ClassSessions get listClassSessionsIncoming {
    _$listClassSessionsIncomingAtom.reportRead();
    return super.listClassSessionsIncoming;
  }

  @override
  set listClassSessionsIncoming(ClassSessions value) {
    _$listClassSessionsIncomingAtom
        .reportWrite(value, super.listClassSessionsIncoming, () {
      super.listClassSessionsIncoming = value;
    });
  }

  late final _$loadingFetchClassSessionTodayAtom = Atom(
      name: '_TeachingManageStore.loadingFetchClassSessionToday',
      context: context);

  @override
  ObservableFuture<dynamic> get loadingFetchClassSessionToday {
    _$loadingFetchClassSessionTodayAtom.reportRead();
    return super.loadingFetchClassSessionToday;
  }

  @override
  set loadingFetchClassSessionToday(ObservableFuture<dynamic> value) {
    _$loadingFetchClassSessionTodayAtom
        .reportWrite(value, super.loadingFetchClassSessionToday, () {
      super.loadingFetchClassSessionToday = value;
    });
  }

  late final _$errFetchClassSessionTodayAtom = Atom(
      name: '_TeachingManageStore.errFetchClassSessionToday', context: context);

  @override
  dynamic get errFetchClassSessionToday {
    _$errFetchClassSessionTodayAtom.reportRead();
    return super.errFetchClassSessionToday;
  }

  @override
  set errFetchClassSessionToday(dynamic value) {
    _$errFetchClassSessionTodayAtom
        .reportWrite(value, super.errFetchClassSessionToday, () {
      super.errFetchClassSessionToday = value;
    });
  }

  late final _$isButtonVisibleAtom =
      Atom(name: '_TeachingManageStore.isButtonVisible', context: context);

  @override
  bool get isButtonVisible {
    _$isButtonVisibleAtom.reportRead();
    return super.isButtonVisible;
  }

  @override
  set isButtonVisible(bool value) {
    _$isButtonVisibleAtom.reportWrite(value, super.isButtonVisible, () {
      super.isButtonVisible = value;
    });
  }

  late final _$listClassSessionsSpecificDayAtom = Atom(
      name: '_TeachingManageStore.listClassSessionsSpecificDay',
      context: context);

  @override
  ClassSessions get listClassSessionsSpecificDay {
    _$listClassSessionsSpecificDayAtom.reportRead();
    return super.listClassSessionsSpecificDay;
  }

  @override
  set listClassSessionsSpecificDay(ClassSessions value) {
    _$listClassSessionsSpecificDayAtom
        .reportWrite(value, super.listClassSessionsSpecificDay, () {
      super.listClassSessionsSpecificDay = value;
    });
  }

  late final _$loadingFetchClassSessionSpecificDayAtom = Atom(
      name: '_TeachingManageStore.loadingFetchClassSessionSpecificDay',
      context: context);

  @override
  ObservableFuture<dynamic> get loadingFetchClassSessionSpecificDay {
    _$loadingFetchClassSessionSpecificDayAtom.reportRead();
    return super.loadingFetchClassSessionSpecificDay;
  }

  @override
  set loadingFetchClassSessionSpecificDay(ObservableFuture<dynamic> value) {
    _$loadingFetchClassSessionSpecificDayAtom
        .reportWrite(value, super.loadingFetchClassSessionSpecificDay, () {
      super.loadingFetchClassSessionSpecificDay = value;
    });
  }

  late final _$errFetchClassSessionSpecificDayAtom = Atom(
      name: '_TeachingManageStore.errFetchClassSessionSpecificDay',
      context: context);

  @override
  dynamic get errFetchClassSessionSpecificDay {
    _$errFetchClassSessionSpecificDayAtom.reportRead();
    return super.errFetchClassSessionSpecificDay;
  }

  @override
  set errFetchClassSessionSpecificDay(dynamic value) {
    _$errFetchClassSessionSpecificDayAtom
        .reportWrite(value, super.errFetchClassSessionSpecificDay, () {
      super.errFetchClassSessionSpecificDay = value;
    });
  }

  late final _$isLoadingWeeklyClassAtom =
      Atom(name: '_TeachingManageStore.isLoadingWeeklyClass', context: context);

  @override
  bool get isLoadingWeeklyClass {
    _$isLoadingWeeklyClassAtom.reportRead();
    return super.isLoadingWeeklyClass;
  }

  @override
  set isLoadingWeeklyClass(bool value) {
    _$isLoadingWeeklyClassAtom.reportWrite(value, super.isLoadingWeeklyClass,
        () {
      super.isLoadingWeeklyClass = value;
    });
  }

  late final _$isLoadingWeeklyClassCompleteAtom = Atom(
      name: '_TeachingManageStore.isLoadingWeeklyClassComplete',
      context: context);

  @override
  bool get isLoadingWeeklyClassComplete {
    _$isLoadingWeeklyClassCompleteAtom.reportRead();
    return super.isLoadingWeeklyClassComplete;
  }

  @override
  set isLoadingWeeklyClassComplete(bool value) {
    _$isLoadingWeeklyClassCompleteAtom
        .reportWrite(value, super.isLoadingWeeklyClassComplete, () {
      super.isLoadingWeeklyClassComplete = value;
    });
  }

  late final _$isLockedSwapPageAtom =
      Atom(name: '_TeachingManageStore.isLockedSwapPage', context: context);

  @override
  bool get isLockedSwapPage {
    _$isLockedSwapPageAtom.reportRead();
    return super.isLockedSwapPage;
  }

  @override
  set isLockedSwapPage(bool value) {
    _$isLockedSwapPageAtom.reportWrite(value, super.isLockedSwapPage, () {
      super.isLockedSwapPage = value;
    });
  }

  late final _$listClassTeachingAtom =
      Atom(name: '_TeachingManageStore.listClassTeaching', context: context);

  @override
  AvailableClasses get listClassTeaching {
    _$listClassTeachingAtom.reportRead();
    return super.listClassTeaching;
  }

  @override
  set listClassTeaching(AvailableClasses value) {
    _$listClassTeachingAtom.reportWrite(value, super.listClassTeaching, () {
      super.listClassTeaching = value;
    });
  }

  late final _$loadingFetchListClassTeachingAtom = Atom(
      name: '_TeachingManageStore.loadingFetchListClassTeaching',
      context: context);

  @override
  ObservableFuture<dynamic> get loadingFetchListClassTeaching {
    _$loadingFetchListClassTeachingAtom.reportRead();
    return super.loadingFetchListClassTeaching;
  }

  @override
  set loadingFetchListClassTeaching(ObservableFuture<dynamic> value) {
    _$loadingFetchListClassTeachingAtom
        .reportWrite(value, super.loadingFetchListClassTeaching, () {
      super.loadingFetchListClassTeaching = value;
    });
  }

  late final _$errFetchListClassTeachingAtom = Atom(
      name: '_TeachingManageStore.errFetchListClassTeaching', context: context);

  @override
  dynamic get errFetchListClassTeaching {
    _$errFetchListClassTeachingAtom.reportRead();
    return super.errFetchListClassTeaching;
  }

  @override
  set errFetchListClassTeaching(dynamic value) {
    _$errFetchListClassTeachingAtom
        .reportWrite(value, super.errFetchListClassTeaching, () {
      super.errFetchListClassTeaching = value;
    });
  }

  late final _$fetchClassTodayAsyncAction =
      AsyncAction('_TeachingManageStore.fetchClassToday', context: context);

  @override
  Future<dynamic> fetchClassToday(Map<String, dynamic> query) {
    return _$fetchClassTodayAsyncAction.run(() => super.fetchClassToday(query));
  }

  late final _$fetchClassIncomingAsyncAction =
      AsyncAction('_TeachingManageStore.fetchClassIncoming', context: context);

  @override
  Future<dynamic> fetchClassIncoming(Map<String, dynamic> query) {
    return _$fetchClassIncomingAsyncAction
        .run(() => super.fetchClassIncoming(query));
  }

  late final _$fetchClassSpecificDayAsyncAction = AsyncAction(
      '_TeachingManageStore.fetchClassSpecificDay',
      context: context);

  @override
  Future<dynamic> fetchClassSpecificDay(Map<String, dynamic> query) {
    return _$fetchClassSpecificDayAsyncAction
        .run(() => super.fetchClassSpecificDay(query));
  }

  late final _$fetchClassTeachingAsyncAction =
      AsyncAction('_TeachingManageStore.fetchClassTeaching', context: context);

  @override
  Future<dynamic> fetchClassTeaching() {
    return _$fetchClassTeachingAsyncAction
        .run(() => super.fetchClassTeaching());
  }

  late final _$_TeachingManageStoreActionController =
      ActionController(name: '_TeachingManageStore', context: context);

  @override
  dynamic setButtonVisible(bool value) {
    final _$actionInfo = _$_TeachingManageStoreActionController.startAction(
        name: '_TeachingManageStore.setButtonVisible');
    try {
      return super.setButtonVisible(value);
    } finally {
      _$_TeachingManageStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  dynamic setupClearFunction() {
    final _$actionInfo = _$_TeachingManageStoreActionController.startAction(
        name: '_TeachingManageStore.setupClearFunction');
    try {
      return super.setupClearFunction();
    } finally {
      _$_TeachingManageStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
loadingComplete: ${loadingComplete},
isLoadingClassTeachingComplete: ${isLoadingClassTeachingComplete},
isLoadingClassTodayComplete: ${isLoadingClassTodayComplete},
listClassSessionsToday: ${listClassSessionsToday},
listClassSessionsIncoming: ${listClassSessionsIncoming},
loadingFetchClassSessionToday: ${loadingFetchClassSessionToday},
errFetchClassSessionToday: ${errFetchClassSessionToday},
isButtonVisible: ${isButtonVisible},
listClassSessionsSpecificDay: ${listClassSessionsSpecificDay},
loadingFetchClassSessionSpecificDay: ${loadingFetchClassSessionSpecificDay},
errFetchClassSessionSpecificDay: ${errFetchClassSessionSpecificDay},
isLoadingWeeklyClass: ${isLoadingWeeklyClass},
isLoadingWeeklyClassComplete: ${isLoadingWeeklyClassComplete},
isLockedSwapPage: ${isLockedSwapPage},
listClassTeaching: ${listClassTeaching},
loadingFetchListClassTeaching: ${loadingFetchListClassTeaching},
errFetchListClassTeaching: ${errFetchListClassTeaching},
haveError: ${haveError},
isLoading: ${isLoading},
isGettingListSession: ${isGettingListSession}
    ''';
  }
}
