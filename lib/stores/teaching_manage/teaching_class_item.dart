import 'package:flutter/material.dart';
import 'package:tutorO/models/class/class.dart';
import 'package:tutorO/ui/common/status_teaching_label.dart';
import 'package:tutorO/ui/settings/common/divider.dart';
import '../../constants/assets.dart';
import '../../constants/colors.dart';

class TeachingClassItem extends StatefulWidget {
  final String nameClass;
  final String codeClass;
  final String dayOfWeek;
  final String hours;
  final int numberStudent;
  final bool isTeaching;
  final bool showThreeDot;
  final bool showStatus;
  final Widget? customImageHeader;
  final Function? detailCallback;
  final double marginBottom;
  final List<DateRes>? schedule;

  TeachingClassItem(
      {this.nameClass = "HCM-1234",
      this.codeClass = "Be lam game",
      this.dayOfWeek = "Thu 3, thu 4",
      this.hours = "1:00",
      this.numberStudent = 2,
      this.isTeaching = false,
      this.showThreeDot = true,
      this.schedule,
      this.showStatus = true,
      this.customImageHeader,
      this.detailCallback,
      this.marginBottom = 10});

  @override
  _TeachingClassItemState createState() => _TeachingClassItemState();
}

class _TeachingClassItemState extends State<TeachingClassItem> {
  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          if (widget.detailCallback != null) {
            widget.detailCallback!();
          }
        },
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            nameAndTimeInClass(),
            Column(
              children: [
                SectionDivider(
                  beginIndent: 0,
                  endIndent: 0,
                ),
                Container(
                  height: 48,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                          margin: EdgeInsets.only(right: 5),
                          child: Image.asset(
                            Assets.threeUsersIcon,
                            width: 20,
                            height: 20,
                          )),
                      Expanded(
                          child: Text(
                        '${widget.numberStudent} học sinh',
                        style: TextStyle(
                            color: AppColors.thirdTextColor,
                            fontSize: 16,
                            height: 1.25),
                      )),
                      Container(child: buildStatusTeaching()),
                    ],
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget buildStatusTeaching() {
    if (widget.showStatus == false) return Container();
    return StatusLabel(
      isPositive: widget.isTeaching,
    );
  }

  Widget nameAndTimeInClass() {
    return Container(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  child: Container(
                    child: Text(
                      widget.nameClass,
                      maxLines: 1,
                      softWrap: false,
                      overflow: TextOverflow.fade,
                      style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w700,
                          height: 1.2,
                          color: AppColors.mainTextColor),
                    ),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: 5, bottom: 10),
                  child: Text(
                    widget.codeClass,
                    maxLines: 2,
                    softWrap: false,
                    overflow: TextOverflow.fade,
                    style: TextStyle(
                        fontSize: 14,
                        height: 1.2,
                        color: AppColors.thirdTextColor),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: 5, bottom: 10),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: EdgeInsets.only(right: 5, top: 2),
                        child: Image.asset(
                          Assets.timeClockIcon,
                          width: 16,
                          height: 16,
                        ),
                      ),
                      Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Column(
                              children: (widget.schedule ?? []).map((e) {
                                return Text(
                                  '${e.convertWeekDayIntToString()}, ${e.friendlyStartTime()} - ${e.friendlyEndTime()}',
                                  style: TextStyle(
                                      fontSize: 15,
                                      color: AppColors.thirdTextColor),
                                );
                              }).toList(),
                            ),
                          ]),
                    ],
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
