// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lesson_products_store.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$LessonProductsStore on _LessonProductsStore, Store {
  late final _$listProductsAtom =
      Atom(name: '_LessonProductsStore.listProducts', context: context);

  @override
  LessonProducts get listProducts {
    _$listProductsAtom.reportRead();
    return super.listProducts;
  }

  @override
  set listProducts(LessonProducts value) {
    _$listProductsAtom.reportWrite(value, super.listProducts, () {
      super.listProducts = value;
    });
  }

  late final _$fetchLessonProductsErrorAtom = Atom(
      name: '_LessonProductsStore.fetchLessonProductsError', context: context);

  @override
  dynamic get fetchLessonProductsError {
    _$fetchLessonProductsErrorAtom.reportRead();
    return super.fetchLessonProductsError;
  }

  @override
  set fetchLessonProductsError(dynamic value) {
    _$fetchLessonProductsErrorAtom
        .reportWrite(value, super.fetchLessonProductsError, () {
      super.fetchLessonProductsError = value;
    });
  }

  late final _$fetchLessonProductsCompletedAtom = Atom(
      name: '_LessonProductsStore.fetchLessonProductsCompleted',
      context: context);

  @override
  bool get fetchLessonProductsCompleted {
    _$fetchLessonProductsCompletedAtom.reportRead();
    return super.fetchLessonProductsCompleted;
  }

  @override
  set fetchLessonProductsCompleted(bool value) {
    _$fetchLessonProductsCompletedAtom
        .reportWrite(value, super.fetchLessonProductsCompleted, () {
      super.fetchLessonProductsCompleted = value;
    });
  }

  late final _$detailProductAtom =
      Atom(name: '_LessonProductsStore.detailProduct', context: context);

  @override
  ProductContent get detailProduct {
    _$detailProductAtom.reportRead();
    return super.detailProduct;
  }

  @override
  set detailProduct(ProductContent value) {
    _$detailProductAtom.reportWrite(value, super.detailProduct, () {
      super.detailProduct = value;
    });
  }

  late final _$fetchDetailProductErrorAtom = Atom(
      name: '_LessonProductsStore.fetchDetailProductError', context: context);

  @override
  dynamic get fetchDetailProductError {
    _$fetchDetailProductErrorAtom.reportRead();
    return super.fetchDetailProductError;
  }

  @override
  set fetchDetailProductError(dynamic value) {
    _$fetchDetailProductErrorAtom
        .reportWrite(value, super.fetchDetailProductError, () {
      super.fetchDetailProductError = value;
    });
  }

  late final _$fetchDetailProductCompletedAtom = Atom(
      name: '_LessonProductsStore.fetchDetailProductCompleted',
      context: context);

  @override
  bool get fetchDetailProductCompleted {
    _$fetchDetailProductCompletedAtom.reportRead();
    return super.fetchDetailProductCompleted;
  }

  @override
  set fetchDetailProductCompleted(bool value) {
    _$fetchDetailProductCompletedAtom
        .reportWrite(value, super.fetchDetailProductCompleted, () {
      super.fetchDetailProductCompleted = value;
    });
  }

  late final _$createOrUpdateDetailProductErrorAtom = Atom(
      name: '_LessonProductsStore.createOrUpdateDetailProductError',
      context: context);

  @override
  dynamic get createOrUpdateDetailProductError {
    _$createOrUpdateDetailProductErrorAtom.reportRead();
    return super.createOrUpdateDetailProductError;
  }

  @override
  set createOrUpdateDetailProductError(dynamic value) {
    _$createOrUpdateDetailProductErrorAtom
        .reportWrite(value, super.createOrUpdateDetailProductError, () {
      super.createOrUpdateDetailProductError = value;
    });
  }

  late final _$createDetailProductCompletedAtom = Atom(
      name: '_LessonProductsStore.createDetailProductCompleted',
      context: context);

  @override
  bool get createDetailProductCompleted {
    _$createDetailProductCompletedAtom.reportRead();
    return super.createDetailProductCompleted;
  }

  @override
  set createDetailProductCompleted(bool value) {
    _$createDetailProductCompletedAtom
        .reportWrite(value, super.createDetailProductCompleted, () {
      super.createDetailProductCompleted = value;
    });
  }

  late final _$deleteDetailProductErrorAtom = Atom(
      name: '_LessonProductsStore.deleteDetailProductError', context: context);

  @override
  dynamic get deleteDetailProductError {
    _$deleteDetailProductErrorAtom.reportRead();
    return super.deleteDetailProductError;
  }

  @override
  set deleteDetailProductError(dynamic value) {
    _$deleteDetailProductErrorAtom
        .reportWrite(value, super.deleteDetailProductError, () {
      super.deleteDetailProductError = value;
    });
  }

  late final _$deleteDetailProductCompletedAtom = Atom(
      name: '_LessonProductsStore.deleteDetailProductCompleted',
      context: context);

  @override
  bool get deleteDetailProductCompleted {
    _$deleteDetailProductCompletedAtom.reportRead();
    return super.deleteDetailProductCompleted;
  }

  @override
  set deleteDetailProductCompleted(bool value) {
    _$deleteDetailProductCompletedAtom
        .reportWrite(value, super.deleteDetailProductCompleted, () {
      super.deleteDetailProductCompleted = value;
    });
  }

  late final _$fetchLessonProductsAsyncAction =
      AsyncAction('_LessonProductsStore.fetchLessonProducts', context: context);

  @override
  Future<dynamic> fetchLessonProducts(int sessionId) {
    return _$fetchLessonProductsAsyncAction
        .run(() => super.fetchLessonProducts(sessionId));
  }

  late final _$fetchDetailProductAsyncAction =
      AsyncAction('_LessonProductsStore.fetchDetailProduct', context: context);

  @override
  Future<dynamic> fetchDetailProduct(int sessionId, int studentId) {
    return _$fetchDetailProductAsyncAction
        .run(() => super.fetchDetailProduct(sessionId, studentId));
  }

  late final _$createOrUpdateDetailProductAsyncAction = AsyncAction(
      '_LessonProductsStore.createOrUpdateDetailProduct',
      context: context);

  @override
  Future<dynamic> createOrUpdateDetailProduct(ProductContent product,
      {bool isUpdate = false, required int sessionId}) {
    return _$createOrUpdateDetailProductAsyncAction.run(() => super
        .createOrUpdateDetailProduct(product,
            isUpdate: isUpdate, sessionId: sessionId));
  }

  late final _$deleteDetailProductAsyncAction =
      AsyncAction('_LessonProductsStore.deleteDetailProduct', context: context);

  @override
  Future<dynamic> deleteDetailProduct(Product product,
      {required int sessionId}) {
    return _$deleteDetailProductAsyncAction
        .run(() => super.deleteDetailProduct(product, sessionId: sessionId));
  }

  late final _$_LessonProductsStoreActionController =
      ActionController(name: '_LessonProductsStore', context: context);

  @override
  void resetValueDetailProduct() {
    final _$actionInfo = _$_LessonProductsStoreActionController.startAction(
        name: '_LessonProductsStore.resetValueDetailProduct');
    try {
      return super.resetValueDetailProduct();
    } finally {
      _$_LessonProductsStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
listProducts: ${listProducts},
fetchLessonProductsError: ${fetchLessonProductsError},
fetchLessonProductsCompleted: ${fetchLessonProductsCompleted},
detailProduct: ${detailProduct},
fetchDetailProductError: ${fetchDetailProductError},
fetchDetailProductCompleted: ${fetchDetailProductCompleted},
createOrUpdateDetailProductError: ${createOrUpdateDetailProductError},
createDetailProductCompleted: ${createDetailProductCompleted},
deleteDetailProductError: ${deleteDetailProductError},
deleteDetailProductCompleted: ${deleteDetailProductCompleted}
    ''';
  }
}
