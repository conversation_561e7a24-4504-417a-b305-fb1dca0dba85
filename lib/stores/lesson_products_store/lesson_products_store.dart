import 'package:collection/collection.dart';
import 'package:mobx/mobx.dart';
import 'package:tutorO/data/repository/class_repository.dart';
import 'package:tutorO/models/lesson_product/lesson_product.dart';
import 'package:tutorO/models/lesson_product/lesson_product_content.dart';

// Include generated file
part 'lesson_products_store.g.dart';

class LessonProductsStore = _LessonProductsStore with _$LessonProductsStore;

// The store-class
abstract class _LessonProductsStore with Store {
  final ClassRepository _classRepository;

  _LessonProductsStore({required ClassRepository classRepository})
      : _classRepository = classRepository;

  @observable
  LessonProducts listProducts = LessonProducts.empty();

  @observable
  dynamic fetchLessonProductsError;

  @observable
  bool fetchLessonProductsCompleted = false;

  @action
  Future fetchLessonProducts(int sessionId) async {
    final future = _classRepository.fetchLessonProducts(sessionId);
    listProducts = LessonProducts.empty();
    fetchLessonProductsCompleted = false;
    fetchLessonProductsError = null;
    future.then((listProducts) {
      this.listProducts = listProducts;
    }).catchError((error) {
      print(error);
      fetchLessonProductsError = error;
    }).whenComplete(() => fetchLessonProductsCompleted = true);
  }

  @observable
  ProductContent detailProduct = ProductContent.empty();

  @observable
  dynamic fetchDetailProductError;

  @observable
  bool fetchDetailProductCompleted = false;

  @action
  void resetValueDetailProduct() => detailProduct = ProductContent.empty();

  @action
  Future fetchDetailProduct(int sessionId, int studentId) async {
    final future = _classRepository.fetchDetailProduct(sessionId, studentId);
    fetchDetailProductCompleted = false;
    fetchDetailProductError = null;
    detailProduct = ProductContent.empty();
    future.then((detailProduct) {
      this.detailProduct = detailProduct;
    }).catchError((error) {
      print(error);
      fetchLessonProductsError = error;
    }).whenComplete(() => fetchDetailProductCompleted = true);
  }

  @observable
  dynamic createOrUpdateDetailProductError;

  @observable
  bool createDetailProductCompleted = false;

  @action
  Future createOrUpdateDetailProduct(ProductContent product,
      {bool isUpdate = false, required int sessionId}) async {
    final future = _classRepository.createOrUpdateDetailProduct(product,
        isUpdate: isUpdate, sessionId: sessionId);
    createDetailProductCompleted = false;
    createOrUpdateDetailProductError = null;
    future.then((detailProduct) {
      var currentProduct = (listProducts.products ?? []).firstWhereOrNull(
          (element) => element.studentId == product.studentId);
      if (currentProduct != null) {
        currentProduct.hasProject = true;
      }
    }).catchError((error) {
      print(error);
      createOrUpdateDetailProductError = error;
    }).whenComplete(() => createDetailProductCompleted = true);
  }

  @observable
  dynamic deleteDetailProductError;

  @observable
  bool deleteDetailProductCompleted = false;

  @action
  Future deleteDetailProduct(Product product, {required int sessionId}) async {
    final future =
        _classRepository.deleteDetailProduct(product, sessionId: sessionId);
    deleteDetailProductCompleted = false;
    deleteDetailProductError = null;
    future.then((_) {
      var listOldProducts = listProducts;
      var currentProduct = (listProducts.products ?? []).firstWhereOrNull(
          (element) => element.studentId == product.studentId);
      if (currentProduct != null) {
        currentProduct.hasProject = false;
      }
      listProducts = listOldProducts;
    }).catchError((error) {
      print(error);
      deleteDetailProductError = error;
    }).whenComplete(() => deleteDetailProductCompleted = true);
  }
}
