import 'package:mobx/mobx.dart';
import 'package:tutorO/constants/values.dart';
import 'package:tutorO/data/repository/class_repository.dart';
import 'package:tutorO/models/check_in/check_in.dart';
import 'package:tutorO/models/class_session/request_off_session/request_off_session.dart';
import 'package:tutorO/models/common_response/common_response.dart';
import 'package:tutorO/models/reason_off/reason_off.dart';
import 'package:tutorO/models/report_student/report_student.dart';
import 'package:tutorO/stores/attendance/attendance_store.dart';

import '../../models/class_session/class_session.dart';
import '../../models/class_session/class_session_media_plan/class_session_media_plan.dart';
import '../../models/lesson_plan/lesson_plan.dart';
import '../../models/report/report.dart';
import 'dart:async';

// Include generated file
part 'lesson_store.g.dart';

class LessonStore = _LessonStore with _$LessonStore;

// The store-class
abstract class _LessonStore with Store {
  final ClassRepository _classRepository;

  final AttendanceStore _attendanceStore;

  _LessonStore({required ClassRepository classRepository})
      : _classRepository = classRepository,
        _attendanceStore = AttendanceStore(classRepository: classRepository);

  AttendanceStore get attendanceStore => _attendanceStore;

  @observable
  ObservableFuture<ClassSession> fetchDetailSessionFuture =
      ObservableFuture.value(ClassSession.empty());

  @observable
  ClassSession classSession = ClassSession.empty();

  @observable
  bool fetchDetailSessionCompleted = false;

  @observable
  ClassSessionError? errFetchDetailSession;

  @observable
  CommonResponse? commonResponse;
  @action
  Future fetchDetailSession(int? idSession) async {
    final future = _classRepository.fetchDetailSession(idSession!);
    errFetchDetailSession = null;
    fetchDetailSessionCompleted = false;
    fetchDetailSessionFuture = ObservableFuture(future);
    future.then((classSession) {
      this.classSession = classSession;
    }).catchError((error) {
      errFetchDetailSession = error;
    }).whenComplete(() => fetchDetailSessionCompleted = true);
  }

  @observable
  bool fetchCheckinStatusCompleted = false;

  @observable
  dynamic errFetchCheckinStatus;

  @observable
  ObservableFuture<CheckInModel> fetchCheckinStatusFuture =
      ObservableFuture.value(CheckInModel.empty());

  @action
  Future fetchCheckinStatus(int idSession) async {
    final future = _classRepository.fetchCheckinStatus(idSession);
    errFetchCheckinStatus = null;
    fetchCheckinStatusCompleted = false;
    fetchCheckinStatusFuture = ObservableFuture(future);
    future.then((checkInStatus) {
      var temp = classSession;
      temp.checkInStatus = checkInStatus;
      classSession = temp;
    }).catchError((error) {
      errFetchCheckinStatus = error;
    }).whenComplete(() => fetchCheckinStatusCompleted = true);
  }

  @observable
  bool fetchRequestOffStatusCompleted = false;

  @observable
  dynamic errFetchRequestOffStatus;

  @observable
  ObservableFuture<RequestOffSession> fetchRequestOffStatusFuture =
      ObservableFuture.value(RequestOffSession.empty());

  @action
  Future fetchRequestOffStatus(int idSession) async {
    final future = _classRepository.fetchRequestOffStatus(idSession);
    errFetchRequestOffStatus = null;
    fetchRequestOffStatusCompleted = false;
    fetchRequestOffStatusFuture = ObservableFuture(future);
    future.then((requestOffSession) {
      var temp = classSession;
      temp.requestOffSession = requestOffSession;
      classSession = temp;
    }).catchError((error) {
      print(error);
      errFetchRequestOffStatus = error;
    }).whenComplete(() => fetchRequestOffStatusCompleted = true);
  }

  @computed
  bool get fetchingData => [
        fetchRequestOffStatusFuture.status,
        fetchCheckinStatusFuture.status,
        fetchDetailSessionFuture.status
      ].contains(FutureStatus.pending);

  @observable
  bool checkInCompleted = false;

  @observable
  CheckInModelError? errCheckIn;

  @action
  Future checkInLesson(int sessionId) async {
    final future = _classRepository.checkInLesson(sessionId);
    errCheckIn = null;
    checkInCompleted = false;
    future.then((value) {
      var currentSession = classSession;
      currentSession.checkInStatus = value;
      classSession = currentSession;
    }).catchError((error) {
      errCheckIn = error;
    }).whenComplete(() => checkInCompleted = true);
  }

  @observable
  bool cancelTeachSessionCompleted = false;

  @observable
  ClassSessionsError? errCancelTeachSession;

  @action
  Future cancelTeachSession(int sessionID, ReasonOff reasonOff) async {
    errCancelTeachSession = null;
    cancelTeachSessionCompleted = false;
    final future = _classRepository.cancelTeachSession(sessionID, reasonOff);
    future.then((value) {
      var temp = classSession;
      temp.requestOffSession = value;
      classSession = temp;
    }).catchError((error) {
      errCancelTeachSession = error;
    }).whenComplete(() => cancelTeachSessionCompleted = true);
  }

  @observable
  bool fetchReportCompleted = false;

  @observable
  ReportError? errorFetchReport;

  @observable
  Report report = Report.empty();

  @action
  Future fetchReport(int id, ReportType type) async {
    final future = _classRepository.fetchReport(id, type);
    errorFetchReport = null;
    fetchReportCompleted = false;
    future.then((value) {
      report = value;
    }).catchError((error) {
      errorFetchReport = error as ReportError;
    }).whenComplete(() => fetchReportCompleted = true);
  }

  @observable
  bool cancelPermissionFormCompleted = false;

  @observable
  dynamic errCancelPermissionForm;

  @action
  Future cancelPermissionForm(int sessionID, ReasonOff reasonOff) async {
    final future = _classRepository.cancelPermissionForm(sessionID, reasonOff);
    errCancelPermissionForm = null;
    cancelPermissionFormCompleted = false;
    future.then((requestOffSession) {
      var temp = classSession;
      temp.requestOffSession = requestOffSession;
      classSession = temp;
    }).catchError((error) {
      errCancelPermissionForm = error;
    }).whenComplete(() => cancelPermissionFormCompleted = true);
  }

  @observable
  List<LessonPlan> listLessonPlan = [];

  @observable
  bool fetchListLessonPlanCompleted = false;

  @observable
  LessonPlanError? errorFetchListLessonPlan;

  @action
  Future fetchLessonPlan(int sessionId) async {
    final future = _classRepository.fetchLessonPlan(sessionId);
    errorFetchListLessonPlan = null;
    fetchListLessonPlanCompleted = false;
    future.then((listLessonPlan) {
      this.listLessonPlan = listLessonPlan;
    }).catchError((error) {
      errorFetchListLessonPlan = error as LessonPlanError;
    }).whenComplete(() => fetchListLessonPlanCompleted = true);
  }

  @computed
  bool get loadingLessonPlan => [
        futureSendLessonToEmail.status,
        futureReportLesson.status,
      ].contains(FutureStatus.pending);

  @observable
  ObservableFuture futureReportLesson = ObservableFuture.value(null);

  @observable
  LessonPlanError? reportLessonPlanError;

  @action
  Future reportLessonPlan(int lessonPlanId) async {
    final future = _classRepository.reportLessonPlan(lessonPlanId);
    reportLessonPlanError = null;
    futureReportLesson = ObservableFuture(future);
    future.catchError((error) {
      reportLessonPlanError = error as LessonPlanError;
    });
  }

  @observable
  ObservableFuture futureSendLessonToEmail = ObservableFuture.value(null);

  @observable
  LessonPlanError? sendLessonPlanToMailError;

  @action
  Future sendLessonPlanToMail(int lessonPlanId) async {
    final future = _classRepository.sendLessonPlanToMail(lessonPlanId);
    sendLessonPlanToMailError = null;
    futureSendLessonToEmail = ObservableFuture(future);
    future.catchError((error) {
      sendLessonPlanToMailError = error as LessonPlanError;
    });
  }

  @observable
  ReportStudent reportStudent = ReportStudent.empty();

  @observable
  bool fetchReportStudentCompleted = false;

  @observable
  ReportStudentError? errorFetchReportStudent;

  @action
  Future fetchReportStudent(int classId, int studentId) async {
    final future = _classRepository.fetchReportStudent(classId, studentId);
    errorFetchReportStudent = null;
    fetchReportStudentCompleted = false;
    future.then((reportStudent) {
      this.reportStudent = reportStudent;
    }).catchError((error) {
      errorFetchReportStudent = error;
    }).whenComplete(() => fetchReportStudentCompleted = true);
  }

  @observable
  bool getBorrowMaterialComplete = false;

  @observable
  bool sendListRequestComplete = false;
  @observable
  bool getListBorrowMaterialComplete = false;
  @observable
  String? materialIdError;

  @observable
  bool isHasData = false;

  @observable
  String filterCharacter = "";
  @observable
  String filterId = '';
  @observable
  bool isEmpty = true;

  // late List<ReactionDisposer> _disposers;

  // @action
  // void reactionAmount(MaterialItem item) {
  //   _disposers = [
  //     reaction((_) => item, increase),

  //     reaction((_) => item, remove),
  //   ];
  // }
  @observable
  String? lessionPlanIframeString = "";
  @observable
  String? lessonUrl = "";

  @observable
  bool? errorFetchListMediaPlan = false;
  @observable
  bool? fetchFetchListMediaPlanCompleted = false;

  @observable
  ListSessionMedialPlanResponse? listSessionMedialPlanResponse;
  @observable
  List<SessionMediaPlan> listSessionMediaPlan = List<SessionMediaPlan>.empty();

  @action
  Future fetchListMediaPlan(
    int sessionId,
  ) async {
    return _classRepository.fetchListMediaPlan(sessionId).then((value) {
      listSessionMedialPlanResponse = value;
      listSessionMediaPlan = value.listSessionMediaPlan!;

      return value;
    }).whenComplete(() => fetchFetchListMediaPlanCompleted = true);
  }

  @observable
  @action
  void formartIframeToUrl(lessionPlanIframeString) {
    List<String> tags = lessionPlanIframeString
        .replaceAll('<', ' ')
        .replaceAll('>', ' ')
        .split(' ');
    String srcTag = tags.where((s) => s.startsWith('src=')).first;
    String url = srcTag.substring(5, srcTag.length - 1);
    lessonUrl = url;
  }
}
