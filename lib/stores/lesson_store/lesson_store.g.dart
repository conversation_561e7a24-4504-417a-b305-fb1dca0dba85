// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lesson_store.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$LessonStore on _LessonStore, Store {
  Computed<bool>? _$fetchingDataComputed;

  @override
  bool get fetchingData =>
      (_$fetchingDataComputed ??= Computed<bool>(() => super.fetchingData,
              name: '_LessonStore.fetchingData'))
          .value;
  Computed<bool>? _$loadingLessonPlanComputed;

  @override
  bool get loadingLessonPlan => (_$loadingLessonPlanComputed ??= Computed<bool>(
          () => super.loadingLessonPlan,
          name: '_LessonStore.loadingLessonPlan'))
      .value;

  late final _$fetchDetailSessionFutureAtom =
      Atom(name: '_LessonStore.fetchDetailSessionFuture', context: context);

  @override
  ObservableFuture<ClassSession> get fetchDetailSessionFuture {
    _$fetchDetailSessionFutureAtom.reportRead();
    return super.fetchDetailSessionFuture;
  }

  @override
  set fetchDetailSessionFuture(ObservableFuture<ClassSession> value) {
    _$fetchDetailSessionFutureAtom
        .reportWrite(value, super.fetchDetailSessionFuture, () {
      super.fetchDetailSessionFuture = value;
    });
  }

  late final _$classSessionAtom =
      Atom(name: '_LessonStore.classSession', context: context);

  @override
  ClassSession get classSession {
    _$classSessionAtom.reportRead();
    return super.classSession;
  }

  @override
  set classSession(ClassSession value) {
    _$classSessionAtom.reportWrite(value, super.classSession, () {
      super.classSession = value;
    });
  }

  late final _$fetchDetailSessionCompletedAtom =
      Atom(name: '_LessonStore.fetchDetailSessionCompleted', context: context);

  @override
  bool get fetchDetailSessionCompleted {
    _$fetchDetailSessionCompletedAtom.reportRead();
    return super.fetchDetailSessionCompleted;
  }

  @override
  set fetchDetailSessionCompleted(bool value) {
    _$fetchDetailSessionCompletedAtom
        .reportWrite(value, super.fetchDetailSessionCompleted, () {
      super.fetchDetailSessionCompleted = value;
    });
  }

  late final _$errFetchDetailSessionAtom =
      Atom(name: '_LessonStore.errFetchDetailSession', context: context);

  @override
  ClassSessionError? get errFetchDetailSession {
    _$errFetchDetailSessionAtom.reportRead();
    return super.errFetchDetailSession;
  }

  @override
  set errFetchDetailSession(ClassSessionError? value) {
    _$errFetchDetailSessionAtom.reportWrite(value, super.errFetchDetailSession,
        () {
      super.errFetchDetailSession = value;
    });
  }

  late final _$commonResponseAtom =
      Atom(name: '_LessonStore.commonResponse', context: context);

  @override
  CommonResponse? get commonResponse {
    _$commonResponseAtom.reportRead();
    return super.commonResponse;
  }

  @override
  set commonResponse(CommonResponse? value) {
    _$commonResponseAtom.reportWrite(value, super.commonResponse, () {
      super.commonResponse = value;
    });
  }

  late final _$fetchCheckinStatusCompletedAtom =
      Atom(name: '_LessonStore.fetchCheckinStatusCompleted', context: context);

  @override
  bool get fetchCheckinStatusCompleted {
    _$fetchCheckinStatusCompletedAtom.reportRead();
    return super.fetchCheckinStatusCompleted;
  }

  @override
  set fetchCheckinStatusCompleted(bool value) {
    _$fetchCheckinStatusCompletedAtom
        .reportWrite(value, super.fetchCheckinStatusCompleted, () {
      super.fetchCheckinStatusCompleted = value;
    });
  }

  late final _$errFetchCheckinStatusAtom =
      Atom(name: '_LessonStore.errFetchCheckinStatus', context: context);

  @override
  dynamic get errFetchCheckinStatus {
    _$errFetchCheckinStatusAtom.reportRead();
    return super.errFetchCheckinStatus;
  }

  @override
  set errFetchCheckinStatus(dynamic value) {
    _$errFetchCheckinStatusAtom.reportWrite(value, super.errFetchCheckinStatus,
        () {
      super.errFetchCheckinStatus = value;
    });
  }

  late final _$fetchCheckinStatusFutureAtom =
      Atom(name: '_LessonStore.fetchCheckinStatusFuture', context: context);

  @override
  ObservableFuture<CheckInModel> get fetchCheckinStatusFuture {
    _$fetchCheckinStatusFutureAtom.reportRead();
    return super.fetchCheckinStatusFuture;
  }

  @override
  set fetchCheckinStatusFuture(ObservableFuture<CheckInModel> value) {
    _$fetchCheckinStatusFutureAtom
        .reportWrite(value, super.fetchCheckinStatusFuture, () {
      super.fetchCheckinStatusFuture = value;
    });
  }

  late final _$fetchRequestOffStatusCompletedAtom = Atom(
      name: '_LessonStore.fetchRequestOffStatusCompleted', context: context);

  @override
  bool get fetchRequestOffStatusCompleted {
    _$fetchRequestOffStatusCompletedAtom.reportRead();
    return super.fetchRequestOffStatusCompleted;
  }

  @override
  set fetchRequestOffStatusCompleted(bool value) {
    _$fetchRequestOffStatusCompletedAtom
        .reportWrite(value, super.fetchRequestOffStatusCompleted, () {
      super.fetchRequestOffStatusCompleted = value;
    });
  }

  late final _$errFetchRequestOffStatusAtom =
      Atom(name: '_LessonStore.errFetchRequestOffStatus', context: context);

  @override
  dynamic get errFetchRequestOffStatus {
    _$errFetchRequestOffStatusAtom.reportRead();
    return super.errFetchRequestOffStatus;
  }

  @override
  set errFetchRequestOffStatus(dynamic value) {
    _$errFetchRequestOffStatusAtom
        .reportWrite(value, super.errFetchRequestOffStatus, () {
      super.errFetchRequestOffStatus = value;
    });
  }

  late final _$fetchRequestOffStatusFutureAtom =
      Atom(name: '_LessonStore.fetchRequestOffStatusFuture', context: context);

  @override
  ObservableFuture<RequestOffSession> get fetchRequestOffStatusFuture {
    _$fetchRequestOffStatusFutureAtom.reportRead();
    return super.fetchRequestOffStatusFuture;
  }

  @override
  set fetchRequestOffStatusFuture(ObservableFuture<RequestOffSession> value) {
    _$fetchRequestOffStatusFutureAtom
        .reportWrite(value, super.fetchRequestOffStatusFuture, () {
      super.fetchRequestOffStatusFuture = value;
    });
  }

  late final _$checkInCompletedAtom =
      Atom(name: '_LessonStore.checkInCompleted', context: context);

  @override
  bool get checkInCompleted {
    _$checkInCompletedAtom.reportRead();
    return super.checkInCompleted;
  }

  @override
  set checkInCompleted(bool value) {
    _$checkInCompletedAtom.reportWrite(value, super.checkInCompleted, () {
      super.checkInCompleted = value;
    });
  }

  late final _$errCheckInAtom =
      Atom(name: '_LessonStore.errCheckIn', context: context);

  @override
  CheckInModelError? get errCheckIn {
    _$errCheckInAtom.reportRead();
    return super.errCheckIn;
  }

  @override
  set errCheckIn(CheckInModelError? value) {
    _$errCheckInAtom.reportWrite(value, super.errCheckIn, () {
      super.errCheckIn = value;
    });
  }

  late final _$cancelTeachSessionCompletedAtom =
      Atom(name: '_LessonStore.cancelTeachSessionCompleted', context: context);

  @override
  bool get cancelTeachSessionCompleted {
    _$cancelTeachSessionCompletedAtom.reportRead();
    return super.cancelTeachSessionCompleted;
  }

  @override
  set cancelTeachSessionCompleted(bool value) {
    _$cancelTeachSessionCompletedAtom
        .reportWrite(value, super.cancelTeachSessionCompleted, () {
      super.cancelTeachSessionCompleted = value;
    });
  }

  late final _$errCancelTeachSessionAtom =
      Atom(name: '_LessonStore.errCancelTeachSession', context: context);

  @override
  ClassSessionsError? get errCancelTeachSession {
    _$errCancelTeachSessionAtom.reportRead();
    return super.errCancelTeachSession;
  }

  @override
  set errCancelTeachSession(ClassSessionsError? value) {
    _$errCancelTeachSessionAtom.reportWrite(value, super.errCancelTeachSession,
        () {
      super.errCancelTeachSession = value;
    });
  }

  late final _$fetchReportCompletedAtom =
      Atom(name: '_LessonStore.fetchReportCompleted', context: context);

  @override
  bool get fetchReportCompleted {
    _$fetchReportCompletedAtom.reportRead();
    return super.fetchReportCompleted;
  }

  @override
  set fetchReportCompleted(bool value) {
    _$fetchReportCompletedAtom.reportWrite(value, super.fetchReportCompleted,
        () {
      super.fetchReportCompleted = value;
    });
  }

  late final _$errorFetchReportAtom =
      Atom(name: '_LessonStore.errorFetchReport', context: context);

  @override
  ReportError? get errorFetchReport {
    _$errorFetchReportAtom.reportRead();
    return super.errorFetchReport;
  }

  @override
  set errorFetchReport(ReportError? value) {
    _$errorFetchReportAtom.reportWrite(value, super.errorFetchReport, () {
      super.errorFetchReport = value;
    });
  }

  late final _$reportAtom = Atom(name: '_LessonStore.report', context: context);

  @override
  Report get report {
    _$reportAtom.reportRead();
    return super.report;
  }

  @override
  set report(Report value) {
    _$reportAtom.reportWrite(value, super.report, () {
      super.report = value;
    });
  }

  late final _$cancelPermissionFormCompletedAtom = Atom(
      name: '_LessonStore.cancelPermissionFormCompleted', context: context);

  @override
  bool get cancelPermissionFormCompleted {
    _$cancelPermissionFormCompletedAtom.reportRead();
    return super.cancelPermissionFormCompleted;
  }

  @override
  set cancelPermissionFormCompleted(bool value) {
    _$cancelPermissionFormCompletedAtom
        .reportWrite(value, super.cancelPermissionFormCompleted, () {
      super.cancelPermissionFormCompleted = value;
    });
  }

  late final _$errCancelPermissionFormAtom =
      Atom(name: '_LessonStore.errCancelPermissionForm', context: context);

  @override
  dynamic get errCancelPermissionForm {
    _$errCancelPermissionFormAtom.reportRead();
    return super.errCancelPermissionForm;
  }

  @override
  set errCancelPermissionForm(dynamic value) {
    _$errCancelPermissionFormAtom
        .reportWrite(value, super.errCancelPermissionForm, () {
      super.errCancelPermissionForm = value;
    });
  }

  late final _$listLessonPlanAtom =
      Atom(name: '_LessonStore.listLessonPlan', context: context);

  @override
  List<LessonPlan> get listLessonPlan {
    _$listLessonPlanAtom.reportRead();
    return super.listLessonPlan;
  }

  @override
  set listLessonPlan(List<LessonPlan> value) {
    _$listLessonPlanAtom.reportWrite(value, super.listLessonPlan, () {
      super.listLessonPlan = value;
    });
  }

  late final _$fetchListLessonPlanCompletedAtom =
      Atom(name: '_LessonStore.fetchListLessonPlanCompleted', context: context);

  @override
  bool get fetchListLessonPlanCompleted {
    _$fetchListLessonPlanCompletedAtom.reportRead();
    return super.fetchListLessonPlanCompleted;
  }

  @override
  set fetchListLessonPlanCompleted(bool value) {
    _$fetchListLessonPlanCompletedAtom
        .reportWrite(value, super.fetchListLessonPlanCompleted, () {
      super.fetchListLessonPlanCompleted = value;
    });
  }

  late final _$errorFetchListLessonPlanAtom =
      Atom(name: '_LessonStore.errorFetchListLessonPlan', context: context);

  @override
  LessonPlanError? get errorFetchListLessonPlan {
    _$errorFetchListLessonPlanAtom.reportRead();
    return super.errorFetchListLessonPlan;
  }

  @override
  set errorFetchListLessonPlan(LessonPlanError? value) {
    _$errorFetchListLessonPlanAtom
        .reportWrite(value, super.errorFetchListLessonPlan, () {
      super.errorFetchListLessonPlan = value;
    });
  }

  late final _$futureReportLessonAtom =
      Atom(name: '_LessonStore.futureReportLesson', context: context);

  @override
  ObservableFuture<dynamic> get futureReportLesson {
    _$futureReportLessonAtom.reportRead();
    return super.futureReportLesson;
  }

  @override
  set futureReportLesson(ObservableFuture<dynamic> value) {
    _$futureReportLessonAtom.reportWrite(value, super.futureReportLesson, () {
      super.futureReportLesson = value;
    });
  }

  late final _$reportLessonPlanErrorAtom =
      Atom(name: '_LessonStore.reportLessonPlanError', context: context);

  @override
  LessonPlanError? get reportLessonPlanError {
    _$reportLessonPlanErrorAtom.reportRead();
    return super.reportLessonPlanError;
  }

  @override
  set reportLessonPlanError(LessonPlanError? value) {
    _$reportLessonPlanErrorAtom.reportWrite(value, super.reportLessonPlanError,
        () {
      super.reportLessonPlanError = value;
    });
  }

  late final _$futureSendLessonToEmailAtom =
      Atom(name: '_LessonStore.futureSendLessonToEmail', context: context);

  @override
  ObservableFuture<dynamic> get futureSendLessonToEmail {
    _$futureSendLessonToEmailAtom.reportRead();
    return super.futureSendLessonToEmail;
  }

  @override
  set futureSendLessonToEmail(ObservableFuture<dynamic> value) {
    _$futureSendLessonToEmailAtom
        .reportWrite(value, super.futureSendLessonToEmail, () {
      super.futureSendLessonToEmail = value;
    });
  }

  late final _$sendLessonPlanToMailErrorAtom =
      Atom(name: '_LessonStore.sendLessonPlanToMailError', context: context);

  @override
  LessonPlanError? get sendLessonPlanToMailError {
    _$sendLessonPlanToMailErrorAtom.reportRead();
    return super.sendLessonPlanToMailError;
  }

  @override
  set sendLessonPlanToMailError(LessonPlanError? value) {
    _$sendLessonPlanToMailErrorAtom
        .reportWrite(value, super.sendLessonPlanToMailError, () {
      super.sendLessonPlanToMailError = value;
    });
  }

  late final _$reportStudentAtom =
      Atom(name: '_LessonStore.reportStudent', context: context);

  @override
  ReportStudent get reportStudent {
    _$reportStudentAtom.reportRead();
    return super.reportStudent;
  }

  @override
  set reportStudent(ReportStudent value) {
    _$reportStudentAtom.reportWrite(value, super.reportStudent, () {
      super.reportStudent = value;
    });
  }

  late final _$fetchReportStudentCompletedAtom =
      Atom(name: '_LessonStore.fetchReportStudentCompleted', context: context);

  @override
  bool get fetchReportStudentCompleted {
    _$fetchReportStudentCompletedAtom.reportRead();
    return super.fetchReportStudentCompleted;
  }

  @override
  set fetchReportStudentCompleted(bool value) {
    _$fetchReportStudentCompletedAtom
        .reportWrite(value, super.fetchReportStudentCompleted, () {
      super.fetchReportStudentCompleted = value;
    });
  }

  late final _$errorFetchReportStudentAtom =
      Atom(name: '_LessonStore.errorFetchReportStudent', context: context);

  @override
  ReportStudentError? get errorFetchReportStudent {
    _$errorFetchReportStudentAtom.reportRead();
    return super.errorFetchReportStudent;
  }

  @override
  set errorFetchReportStudent(ReportStudentError? value) {
    _$errorFetchReportStudentAtom
        .reportWrite(value, super.errorFetchReportStudent, () {
      super.errorFetchReportStudent = value;
    });
  }

  late final _$getBorrowMaterialCompleteAtom =
      Atom(name: '_LessonStore.getBorrowMaterialComplete', context: context);

  @override
  bool get getBorrowMaterialComplete {
    _$getBorrowMaterialCompleteAtom.reportRead();
    return super.getBorrowMaterialComplete;
  }

  @override
  set getBorrowMaterialComplete(bool value) {
    _$getBorrowMaterialCompleteAtom
        .reportWrite(value, super.getBorrowMaterialComplete, () {
      super.getBorrowMaterialComplete = value;
    });
  }

  late final _$sendListRequestCompleteAtom =
      Atom(name: '_LessonStore.sendListRequestComplete', context: context);

  @override
  bool get sendListRequestComplete {
    _$sendListRequestCompleteAtom.reportRead();
    return super.sendListRequestComplete;
  }

  @override
  set sendListRequestComplete(bool value) {
    _$sendListRequestCompleteAtom
        .reportWrite(value, super.sendListRequestComplete, () {
      super.sendListRequestComplete = value;
    });
  }

  late final _$getListBorrowMaterialCompleteAtom = Atom(
      name: '_LessonStore.getListBorrowMaterialComplete', context: context);

  @override
  bool get getListBorrowMaterialComplete {
    _$getListBorrowMaterialCompleteAtom.reportRead();
    return super.getListBorrowMaterialComplete;
  }

  @override
  set getListBorrowMaterialComplete(bool value) {
    _$getListBorrowMaterialCompleteAtom
        .reportWrite(value, super.getListBorrowMaterialComplete, () {
      super.getListBorrowMaterialComplete = value;
    });
  }

  late final _$materialIdErrorAtom =
      Atom(name: '_LessonStore.materialIdError', context: context);

  @override
  String? get materialIdError {
    _$materialIdErrorAtom.reportRead();
    return super.materialIdError;
  }

  @override
  set materialIdError(String? value) {
    _$materialIdErrorAtom.reportWrite(value, super.materialIdError, () {
      super.materialIdError = value;
    });
  }

  late final _$isHasDataAtom =
      Atom(name: '_LessonStore.isHasData', context: context);

  @override
  bool get isHasData {
    _$isHasDataAtom.reportRead();
    return super.isHasData;
  }

  @override
  set isHasData(bool value) {
    _$isHasDataAtom.reportWrite(value, super.isHasData, () {
      super.isHasData = value;
    });
  }

  late final _$filterCharacterAtom =
      Atom(name: '_LessonStore.filterCharacter', context: context);

  @override
  String get filterCharacter {
    _$filterCharacterAtom.reportRead();
    return super.filterCharacter;
  }

  @override
  set filterCharacter(String value) {
    _$filterCharacterAtom.reportWrite(value, super.filterCharacter, () {
      super.filterCharacter = value;
    });
  }

  late final _$filterIdAtom =
      Atom(name: '_LessonStore.filterId', context: context);

  @override
  String get filterId {
    _$filterIdAtom.reportRead();
    return super.filterId;
  }

  @override
  set filterId(String value) {
    _$filterIdAtom.reportWrite(value, super.filterId, () {
      super.filterId = value;
    });
  }

  late final _$isEmptyAtom =
      Atom(name: '_LessonStore.isEmpty', context: context);

  @override
  bool get isEmpty {
    _$isEmptyAtom.reportRead();
    return super.isEmpty;
  }

  @override
  set isEmpty(bool value) {
    _$isEmptyAtom.reportWrite(value, super.isEmpty, () {
      super.isEmpty = value;
    });
  }

  late final _$lessionPlanIframeStringAtom =
      Atom(name: '_LessonStore.lessionPlanIframeString', context: context);

  @override
  String? get lessionPlanIframeString {
    _$lessionPlanIframeStringAtom.reportRead();
    return super.lessionPlanIframeString;
  }

  @override
  set lessionPlanIframeString(String? value) {
    _$lessionPlanIframeStringAtom
        .reportWrite(value, super.lessionPlanIframeString, () {
      super.lessionPlanIframeString = value;
    });
  }

  late final _$lessonUrlAtom =
      Atom(name: '_LessonStore.lessonUrl', context: context);

  @override
  String? get lessonUrl {
    _$lessonUrlAtom.reportRead();
    return super.lessonUrl;
  }

  @override
  set lessonUrl(String? value) {
    _$lessonUrlAtom.reportWrite(value, super.lessonUrl, () {
      super.lessonUrl = value;
    });
  }

  late final _$errorFetchListMediaPlanAtom =
      Atom(name: '_LessonStore.errorFetchListMediaPlan', context: context);

  @override
  bool? get errorFetchListMediaPlan {
    _$errorFetchListMediaPlanAtom.reportRead();
    return super.errorFetchListMediaPlan;
  }

  @override
  set errorFetchListMediaPlan(bool? value) {
    _$errorFetchListMediaPlanAtom
        .reportWrite(value, super.errorFetchListMediaPlan, () {
      super.errorFetchListMediaPlan = value;
    });
  }

  late final _$fetchFetchListMediaPlanCompletedAtom = Atom(
      name: '_LessonStore.fetchFetchListMediaPlanCompleted', context: context);

  @override
  bool? get fetchFetchListMediaPlanCompleted {
    _$fetchFetchListMediaPlanCompletedAtom.reportRead();
    return super.fetchFetchListMediaPlanCompleted;
  }

  @override
  set fetchFetchListMediaPlanCompleted(bool? value) {
    _$fetchFetchListMediaPlanCompletedAtom
        .reportWrite(value, super.fetchFetchListMediaPlanCompleted, () {
      super.fetchFetchListMediaPlanCompleted = value;
    });
  }

  late final _$listSessionMedialPlanResponseAtom = Atom(
      name: '_LessonStore.listSessionMedialPlanResponse', context: context);

  @override
  ListSessionMedialPlanResponse? get listSessionMedialPlanResponse {
    _$listSessionMedialPlanResponseAtom.reportRead();
    return super.listSessionMedialPlanResponse;
  }

  @override
  set listSessionMedialPlanResponse(ListSessionMedialPlanResponse? value) {
    _$listSessionMedialPlanResponseAtom
        .reportWrite(value, super.listSessionMedialPlanResponse, () {
      super.listSessionMedialPlanResponse = value;
    });
  }

  late final _$listSessionMediaPlanAtom =
      Atom(name: '_LessonStore.listSessionMediaPlan', context: context);

  @override
  List<SessionMediaPlan> get listSessionMediaPlan {
    _$listSessionMediaPlanAtom.reportRead();
    return super.listSessionMediaPlan;
  }

  @override
  set listSessionMediaPlan(List<SessionMediaPlan> value) {
    _$listSessionMediaPlanAtom.reportWrite(value, super.listSessionMediaPlan,
        () {
      super.listSessionMediaPlan = value;
    });
  }

  late final _$fetchDetailSessionAsyncAction =
      AsyncAction('_LessonStore.fetchDetailSession', context: context);

  @override
  Future<dynamic> fetchDetailSession(int? idSession) {
    return _$fetchDetailSessionAsyncAction
        .run(() => super.fetchDetailSession(idSession));
  }

  late final _$fetchCheckinStatusAsyncAction =
      AsyncAction('_LessonStore.fetchCheckinStatus', context: context);

  @override
  Future<dynamic> fetchCheckinStatus(int idSession) {
    return _$fetchCheckinStatusAsyncAction
        .run(() => super.fetchCheckinStatus(idSession));
  }

  late final _$fetchRequestOffStatusAsyncAction =
      AsyncAction('_LessonStore.fetchRequestOffStatus', context: context);

  @override
  Future<dynamic> fetchRequestOffStatus(int idSession) {
    return _$fetchRequestOffStatusAsyncAction
        .run(() => super.fetchRequestOffStatus(idSession));
  }

  late final _$checkInLessonAsyncAction =
      AsyncAction('_LessonStore.checkInLesson', context: context);

  @override
  Future<dynamic> checkInLesson(int sessionId) {
    return _$checkInLessonAsyncAction.run(() => super.checkInLesson(sessionId));
  }

  late final _$cancelTeachSessionAsyncAction =
      AsyncAction('_LessonStore.cancelTeachSession', context: context);

  @override
  Future<dynamic> cancelTeachSession(int sessionID, ReasonOff reasonOff) {
    return _$cancelTeachSessionAsyncAction
        .run(() => super.cancelTeachSession(sessionID, reasonOff));
  }

  late final _$fetchReportAsyncAction =
      AsyncAction('_LessonStore.fetchReport', context: context);

  @override
  Future<dynamic> fetchReport(int id, ReportType type) {
    return _$fetchReportAsyncAction.run(() => super.fetchReport(id, type));
  }

  late final _$cancelPermissionFormAsyncAction =
      AsyncAction('_LessonStore.cancelPermissionForm', context: context);

  @override
  Future<dynamic> cancelPermissionForm(int sessionID, ReasonOff reasonOff) {
    return _$cancelPermissionFormAsyncAction
        .run(() => super.cancelPermissionForm(sessionID, reasonOff));
  }

  late final _$fetchLessonPlanAsyncAction =
      AsyncAction('_LessonStore.fetchLessonPlan', context: context);

  @override
  Future<dynamic> fetchLessonPlan(int sessionId) {
    return _$fetchLessonPlanAsyncAction
        .run(() => super.fetchLessonPlan(sessionId));
  }

  late final _$reportLessonPlanAsyncAction =
      AsyncAction('_LessonStore.reportLessonPlan', context: context);

  @override
  Future<dynamic> reportLessonPlan(int lessonPlanId) {
    return _$reportLessonPlanAsyncAction
        .run(() => super.reportLessonPlan(lessonPlanId));
  }

  late final _$sendLessonPlanToMailAsyncAction =
      AsyncAction('_LessonStore.sendLessonPlanToMail', context: context);

  @override
  Future<dynamic> sendLessonPlanToMail(int lessonPlanId) {
    return _$sendLessonPlanToMailAsyncAction
        .run(() => super.sendLessonPlanToMail(lessonPlanId));
  }

  late final _$fetchReportStudentAsyncAction =
      AsyncAction('_LessonStore.fetchReportStudent', context: context);

  @override
  Future<dynamic> fetchReportStudent(int classId, int studentId) {
    return _$fetchReportStudentAsyncAction
        .run(() => super.fetchReportStudent(classId, studentId));
  }

  late final _$fetchListMediaPlanAsyncAction =
      AsyncAction('_LessonStore.fetchListMediaPlan', context: context);

  @override
  Future<dynamic> fetchListMediaPlan(int sessionId) {
    return _$fetchListMediaPlanAsyncAction
        .run(() => super.fetchListMediaPlan(sessionId));
  }

  late final _$_LessonStoreActionController =
      ActionController(name: '_LessonStore', context: context);

  @override
  void formartIframeToUrl(dynamic lessionPlanIframeString) {
    final _$actionInfo = _$_LessonStoreActionController.startAction(
        name: '_LessonStore.formartIframeToUrl');
    try {
      return super.formartIframeToUrl(lessionPlanIframeString);
    } finally {
      _$_LessonStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
fetchDetailSessionFuture: ${fetchDetailSessionFuture},
classSession: ${classSession},
fetchDetailSessionCompleted: ${fetchDetailSessionCompleted},
errFetchDetailSession: ${errFetchDetailSession},
commonResponse: ${commonResponse},
fetchCheckinStatusCompleted: ${fetchCheckinStatusCompleted},
errFetchCheckinStatus: ${errFetchCheckinStatus},
fetchCheckinStatusFuture: ${fetchCheckinStatusFuture},
fetchRequestOffStatusCompleted: ${fetchRequestOffStatusCompleted},
errFetchRequestOffStatus: ${errFetchRequestOffStatus},
fetchRequestOffStatusFuture: ${fetchRequestOffStatusFuture},
checkInCompleted: ${checkInCompleted},
errCheckIn: ${errCheckIn},
cancelTeachSessionCompleted: ${cancelTeachSessionCompleted},
errCancelTeachSession: ${errCancelTeachSession},
fetchReportCompleted: ${fetchReportCompleted},
errorFetchReport: ${errorFetchReport},
report: ${report},
cancelPermissionFormCompleted: ${cancelPermissionFormCompleted},
errCancelPermissionForm: ${errCancelPermissionForm},
listLessonPlan: ${listLessonPlan},
fetchListLessonPlanCompleted: ${fetchListLessonPlanCompleted},
errorFetchListLessonPlan: ${errorFetchListLessonPlan},
futureReportLesson: ${futureReportLesson},
reportLessonPlanError: ${reportLessonPlanError},
futureSendLessonToEmail: ${futureSendLessonToEmail},
sendLessonPlanToMailError: ${sendLessonPlanToMailError},
reportStudent: ${reportStudent},
fetchReportStudentCompleted: ${fetchReportStudentCompleted},
errorFetchReportStudent: ${errorFetchReportStudent},
getBorrowMaterialComplete: ${getBorrowMaterialComplete},
sendListRequestComplete: ${sendListRequestComplete},
getListBorrowMaterialComplete: ${getListBorrowMaterialComplete},
materialIdError: ${materialIdError},
isHasData: ${isHasData},
filterCharacter: ${filterCharacter},
filterId: ${filterId},
isEmpty: ${isEmpty},
lessionPlanIframeString: ${lessionPlanIframeString},
lessonUrl: ${lessonUrl},
errorFetchListMediaPlan: ${errorFetchListMediaPlan},
fetchFetchListMediaPlanCompleted: ${fetchFetchListMediaPlanCompleted},
listSessionMedialPlanResponse: ${listSessionMedialPlanResponse},
listSessionMediaPlan: ${listSessionMediaPlan},
fetchingData: ${fetchingData},
loadingLessonPlan: ${loadingLessonPlan}
    ''';
  }
}
