// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'state_app_store.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$StateAppStore on _StateAppStore, Store {
  late final _$borderBottomTabAtom =
      Atom(name: '_StateAppStore.borderBottomTab', context: context);

  @override
  bool get borderBottomTab {
    _$borderBottomTabAtom.reportRead();
    return super.borderBottomTab;
  }

  @override
  set borderBottomTab(bool value) {
    _$borderBottomTabAtom.reportWrite(value, super.borderBottomTab, () {
      super.borderBottomTab = value;
    });
  }

  late final _$_StateAppStoreActionController =
      ActionController(name: '_StateAppStore', context: context);

  @override
  void setBorderBottomTab({required bool newState}) {
    final _$actionInfo = _$_StateAppStoreActionController.startAction(
        name: '_StateAppStore.setBorderBottomTab');
    try {
      return super.setBorderBottomTab(newState: newState);
    } finally {
      _$_StateAppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
borderBottomTab: ${borderBottomTab}
    ''';
  }
}
