import 'package:mobx/mobx.dart';

// Include generated file
part 'state_app_store.g.dart';

// This is the class used by rest of your codebase
class StateAppStore = _StateAppStore with _$StateAppStore;

// The store-class
abstract class _StateAppStore with Store {

  @observable
  bool borderBottomTab = true;

  @action
  void setBorderBottomTab({ required bool newState }){
    if(newState != borderBottomTab) borderBottomTab = newState;
  }

}
