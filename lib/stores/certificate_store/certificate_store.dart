import 'dart:io';

import 'package:mobx/mobx.dart';
import 'package:tutorO/data/repository/certificate_repository.dart';
import 'package:tutorO/models/cerificate/certificate_model.dart';
import 'package:tutorO/utils/image/image.dart';
part 'certificate_store.g.dart';

class CertificationStore = _CertificationStore with _$CertificationStore;

abstract class _CertificationStore with Store {
  final CertificationRepository _certificationRepository;

  _CertificationStore(
      {required CertificationRepository certificationRepository})
      : _certificationRepository = certificationRepository;

  @observable
  ObservableFuture<List<CertificationModel>> fetchCertificationsFuture =
      ObservableFuture.value([]);

  @observable
  ObservableList<CertificationModel> arrCertificationModels =
      ObservableList.of([]);

  @computed
  bool get fetchingCertifications =>
      fetchCertificationsFuture.status == FutureStatus.pending;

  @action
  Future fetchCertifications() async {
    final future = _certificationRepository.fetchCertifications();
    fetchCertificationsFuture = ObservableFuture(future);

    future.then((certifications) {
      arrCertificationModels.clear();
      arrCertificationModels.addAll(certifications);
    }).catchError((error) {
      print("fetchCertifications error: $error");
    });
  }

  @observable
  CertificationError? errAddOrEditCertificate;

  @observable
  bool requestAddOrEditCertificateCompleted = false;

  @action
  Future addCertificate(CertificationModel newCertificate, File image) async {
    errAddOrEditCertificate = null;
    requestAddOrEditCertificateCompleted = false;
    final future = _certificationRepository.addCertification(
        newCertificate, await ImageConvert.compressFile(image));
    future.then((academicLevel) {
      arrCertificationModels.add(academicLevel);
    }).catchError((error) {
      if (error is CertificationError) {
        errAddOrEditCertificate = error;
      }
    }).whenComplete(() => requestAddOrEditCertificateCompleted = true);
  }

  @action
  Future editCertificate(
      CertificationModel editCertificate, File? image) async {
    errAddOrEditCertificate = null;
    requestAddOrEditCertificateCompleted = false;
    final future = _certificationRepository.editCertification(editCertificate,
        image != null ? await ImageConvert.compressFile(image) : null);
    future.then((certificate) {
      final replaceIndex = arrCertificationModels
          .indexWhere((element) => element.id == editCertificate.id);
      if (replaceIndex >= 0) {
        arrCertificationModels[replaceIndex] = certificate;
      }
    }).catchError((error) {
      if (error is CertificationError) {
        errAddOrEditCertificate = error;
      }
    }).whenComplete(() => requestAddOrEditCertificateCompleted = true);
  }

  @observable
  CertificationError? errDeleteCertificate;

  @observable
  bool requestDeleteCertificateCompleted = false;

  @action
  Future deleteCertificate(int id) async {
    errDeleteCertificate = null;
    requestDeleteCertificateCompleted = false;
    final future = _certificationRepository.deleteCertificate(id);
    future.then((_) {
      arrCertificationModels.removeWhere((element) => element.id == id);
    }).catchError((error) {
      if (error is CertificationError) {
        errDeleteCertificate = error;
      }
    }).whenComplete(() => requestDeleteCertificateCompleted = true);
  }
}
