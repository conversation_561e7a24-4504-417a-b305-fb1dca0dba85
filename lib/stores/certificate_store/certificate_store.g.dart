// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'certificate_store.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$CertificationStore on _CertificationStore, Store {
  Computed<bool>? _$fetchingCertificationsComputed;

  @override
  bool get fetchingCertifications => (_$fetchingCertificationsComputed ??=
          Computed<bool>(() => super.fetchingCertifications,
              name: '_CertificationStore.fetchingCertifications'))
      .value;

  late final _$fetchCertificationsFutureAtom = Atom(
      name: '_CertificationStore.fetchCertificationsFuture', context: context);

  @override
  ObservableFuture<List<CertificationModel>> get fetchCertificationsFuture {
    _$fetchCertificationsFutureAtom.reportRead();
    return super.fetchCertificationsFuture;
  }

  @override
  set fetchCertificationsFuture(
      ObservableFuture<List<CertificationModel>> value) {
    _$fetchCertificationsFutureAtom
        .reportWrite(value, super.fetchCertificationsFuture, () {
      super.fetchCertificationsFuture = value;
    });
  }

  late final _$arrCertificationModelsAtom = Atom(
      name: '_CertificationStore.arrCertificationModels', context: context);

  @override
  ObservableList<CertificationModel> get arrCertificationModels {
    _$arrCertificationModelsAtom.reportRead();
    return super.arrCertificationModels;
  }

  @override
  set arrCertificationModels(ObservableList<CertificationModel> value) {
    _$arrCertificationModelsAtom
        .reportWrite(value, super.arrCertificationModels, () {
      super.arrCertificationModels = value;
    });
  }

  late final _$errAddOrEditCertificateAtom = Atom(
      name: '_CertificationStore.errAddOrEditCertificate', context: context);

  @override
  CertificationError? get errAddOrEditCertificate {
    _$errAddOrEditCertificateAtom.reportRead();
    return super.errAddOrEditCertificate;
  }

  @override
  set errAddOrEditCertificate(CertificationError? value) {
    _$errAddOrEditCertificateAtom
        .reportWrite(value, super.errAddOrEditCertificate, () {
      super.errAddOrEditCertificate = value;
    });
  }

  late final _$requestAddOrEditCertificateCompletedAtom = Atom(
      name: '_CertificationStore.requestAddOrEditCertificateCompleted',
      context: context);

  @override
  bool get requestAddOrEditCertificateCompleted {
    _$requestAddOrEditCertificateCompletedAtom.reportRead();
    return super.requestAddOrEditCertificateCompleted;
  }

  @override
  set requestAddOrEditCertificateCompleted(bool value) {
    _$requestAddOrEditCertificateCompletedAtom
        .reportWrite(value, super.requestAddOrEditCertificateCompleted, () {
      super.requestAddOrEditCertificateCompleted = value;
    });
  }

  late final _$errDeleteCertificateAtom =
      Atom(name: '_CertificationStore.errDeleteCertificate', context: context);

  @override
  CertificationError? get errDeleteCertificate {
    _$errDeleteCertificateAtom.reportRead();
    return super.errDeleteCertificate;
  }

  @override
  set errDeleteCertificate(CertificationError? value) {
    _$errDeleteCertificateAtom.reportWrite(value, super.errDeleteCertificate,
        () {
      super.errDeleteCertificate = value;
    });
  }

  late final _$requestDeleteCertificateCompletedAtom = Atom(
      name: '_CertificationStore.requestDeleteCertificateCompleted',
      context: context);

  @override
  bool get requestDeleteCertificateCompleted {
    _$requestDeleteCertificateCompletedAtom.reportRead();
    return super.requestDeleteCertificateCompleted;
  }

  @override
  set requestDeleteCertificateCompleted(bool value) {
    _$requestDeleteCertificateCompletedAtom
        .reportWrite(value, super.requestDeleteCertificateCompleted, () {
      super.requestDeleteCertificateCompleted = value;
    });
  }

  late final _$fetchCertificationsAsyncAction =
      AsyncAction('_CertificationStore.fetchCertifications', context: context);

  @override
  Future<dynamic> fetchCertifications() {
    return _$fetchCertificationsAsyncAction
        .run(() => super.fetchCertifications());
  }

  late final _$addCertificateAsyncAction =
      AsyncAction('_CertificationStore.addCertificate', context: context);

  @override
  Future<dynamic> addCertificate(
      CertificationModel newCertificate, File image) {
    return _$addCertificateAsyncAction
        .run(() => super.addCertificate(newCertificate, image));
  }

  late final _$editCertificateAsyncAction =
      AsyncAction('_CertificationStore.editCertificate', context: context);

  @override
  Future<dynamic> editCertificate(
      CertificationModel editCertificate, File? image) {
    return _$editCertificateAsyncAction
        .run(() => super.editCertificate(editCertificate, image));
  }

  late final _$deleteCertificateAsyncAction =
      AsyncAction('_CertificationStore.deleteCertificate', context: context);

  @override
  Future<dynamic> deleteCertificate(int id) {
    return _$deleteCertificateAsyncAction
        .run(() => super.deleteCertificate(id));
  }

  @override
  String toString() {
    return '''
fetchCertificationsFuture: ${fetchCertificationsFuture},
arrCertificationModels: ${arrCertificationModels},
errAddOrEditCertificate: ${errAddOrEditCertificate},
requestAddOrEditCertificateCompleted: ${requestAddOrEditCertificateCompleted},
errDeleteCertificate: ${errDeleteCertificate},
requestDeleteCertificateCompleted: ${requestDeleteCertificateCompleted},
fetchingCertifications: ${fetchingCertifications}
    ''';
  }
}
