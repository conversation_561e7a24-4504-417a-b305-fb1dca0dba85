// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'master_data_store.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$MasterDataStore on _MasterDataStore, Store {
  late final _$countriesAtom =
      Atom(name: '_MasterDataStore.countries', context: context);

  @override
  List<Country> get countries {
    _$countriesAtom.reportRead();
    return super.countries;
  }

  @override
  set countries(List<Country> value) {
    _$countriesAtom.reportWrite(value, super.countries, () {
      super.countries = value;
    });
  }

  late final _$districtsAtom =
      Atom(name: '_MasterDataStore.districts', context: context);

  @override
  List<District> get districts {
    _$districtsAtom.reportRead();
    return super.districts;
  }

  @override
  set districts(List<District> value) {
    _$districtsAtom.reportWrite(value, super.districts, () {
      super.districts = value;
    });
  }

  late final _$provincesAtom =
      Atom(name: '_MasterDataStore.provinces', context: context);

  @override
  List<Province> get provinces {
    _$provincesAtom.reportRead();
    return super.provinces;
  }

  @override
  set provinces(List<Province> value) {
    _$provincesAtom.reportWrite(value, super.provinces, () {
      super.provinces = value;
    });
  }

  late final _$wardsAtom =
      Atom(name: '_MasterDataStore.wards', context: context);

  @override
  List<Ward> get wards {
    _$wardsAtom.reportRead();
    return super.wards;
  }

  @override
  set wards(List<Ward> value) {
    _$wardsAtom.reportWrite(value, super.wards, () {
      super.wards = value;
    });
  }

  late final _$trainingUnitsAtom =
      Atom(name: '_MasterDataStore.trainingUnits', context: context);

  @override
  List<TrainingUnit> get trainingUnits {
    _$trainingUnitsAtom.reportRead();
    return super.trainingUnits;
  }

  @override
  set trainingUnits(List<TrainingUnit> value) {
    _$trainingUnitsAtom.reportWrite(value, super.trainingUnits, () {
      super.trainingUnits = value;
    });
  }

  late final _$academicTitlesAtom =
      Atom(name: '_MasterDataStore.academicTitles', context: context);

  @override
  List<AcademicTitle> get academicTitles {
    _$academicTitlesAtom.reportRead();
    return super.academicTitles;
  }

  @override
  set academicTitles(List<AcademicTitle> value) {
    _$academicTitlesAtom.reportWrite(value, super.academicTitles, () {
      super.academicTitles = value;
    });
  }

  late final _$successFetchCountriesAtom =
      Atom(name: '_MasterDataStore.successFetchCountries', context: context);

  @override
  bool get successFetchCountries {
    _$successFetchCountriesAtom.reportRead();
    return super.successFetchCountries;
  }

  @override
  set successFetchCountries(bool value) {
    _$successFetchCountriesAtom.reportWrite(value, super.successFetchCountries,
        () {
      super.successFetchCountries = value;
    });
  }

  late final _$successFetchProvincesAtom =
      Atom(name: '_MasterDataStore.successFetchProvinces', context: context);

  @override
  bool get successFetchProvinces {
    _$successFetchProvincesAtom.reportRead();
    return super.successFetchProvinces;
  }

  @override
  set successFetchProvinces(bool value) {
    _$successFetchProvincesAtom.reportWrite(value, super.successFetchProvinces,
        () {
      super.successFetchProvinces = value;
    });
  }

  late final _$successFetchDistrictsAtom =
      Atom(name: '_MasterDataStore.successFetchDistricts', context: context);

  @override
  bool get successFetchDistricts {
    _$successFetchDistrictsAtom.reportRead();
    return super.successFetchDistricts;
  }

  @override
  set successFetchDistricts(bool value) {
    _$successFetchDistrictsAtom.reportWrite(value, super.successFetchDistricts,
        () {
      super.successFetchDistricts = value;
    });
  }

  late final _$successFetchWardsAtom =
      Atom(name: '_MasterDataStore.successFetchWards', context: context);

  @override
  bool get successFetchWards {
    _$successFetchWardsAtom.reportRead();
    return super.successFetchWards;
  }

  @override
  set successFetchWards(bool value) {
    _$successFetchWardsAtom.reportWrite(value, super.successFetchWards, () {
      super.successFetchWards = value;
    });
  }

  late final _$fetchTrainingUnitsCompletedAtom = Atom(
      name: '_MasterDataStore.fetchTrainingUnitsCompleted', context: context);

  @override
  bool get fetchTrainingUnitsCompleted {
    _$fetchTrainingUnitsCompletedAtom.reportRead();
    return super.fetchTrainingUnitsCompleted;
  }

  @override
  set fetchTrainingUnitsCompleted(bool value) {
    _$fetchTrainingUnitsCompletedAtom
        .reportWrite(value, super.fetchTrainingUnitsCompleted, () {
      super.fetchTrainingUnitsCompleted = value;
    });
  }

  late final _$fetchAcademicTitlesCompletedAtom = Atom(
      name: '_MasterDataStore.fetchAcademicTitlesCompleted', context: context);

  @override
  bool get fetchAcademicTitlesCompleted {
    _$fetchAcademicTitlesCompletedAtom.reportRead();
    return super.fetchAcademicTitlesCompleted;
  }

  @override
  set fetchAcademicTitlesCompleted(bool value) {
    _$fetchAcademicTitlesCompletedAtom
        .reportWrite(value, super.fetchAcademicTitlesCompleted, () {
      super.fetchAcademicTitlesCompleted = value;
    });
  }

  late final _$termAndRulesAtom =
      Atom(name: '_MasterDataStore.termAndRules', context: context);

  @override
  String get termAndRules {
    _$termAndRulesAtom.reportRead();
    return super.termAndRules;
  }

  @override
  set termAndRules(String value) {
    _$termAndRulesAtom.reportWrite(value, super.termAndRules, () {
      super.termAndRules = value;
    });
  }

  late final _$fetchTermAndRulesCompletedAtom = Atom(
      name: '_MasterDataStore.fetchTermAndRulesCompleted', context: context);

  @override
  bool get fetchTermAndRulesCompleted {
    _$fetchTermAndRulesCompletedAtom.reportRead();
    return super.fetchTermAndRulesCompleted;
  }

  @override
  set fetchTermAndRulesCompleted(bool value) {
    _$fetchTermAndRulesCompletedAtom
        .reportWrite(value, super.fetchTermAndRulesCompleted, () {
      super.fetchTermAndRulesCompleted = value;
    });
  }

  late final _$subjectsAtom =
      Atom(name: '_MasterDataStore.subjects', context: context);

  @override
  List<Subject> get subjects {
    _$subjectsAtom.reportRead();
    return super.subjects;
  }

  @override
  set subjects(List<Subject> value) {
    _$subjectsAtom.reportWrite(value, super.subjects, () {
      super.subjects = value;
    });
  }

  late final _$fetchSubjectCompletedAtom =
      Atom(name: '_MasterDataStore.fetchSubjectCompleted', context: context);

  @override
  bool get fetchSubjectCompleted {
    _$fetchSubjectCompletedAtom.reportRead();
    return super.fetchSubjectCompleted;
  }

  @override
  set fetchSubjectCompleted(bool value) {
    _$fetchSubjectCompletedAtom.reportWrite(value, super.fetchSubjectCompleted,
        () {
      super.fetchSubjectCompleted = value;
    });
  }

  late final _$gradesAtom =
      Atom(name: '_MasterDataStore.grades', context: context);

  @override
  List<Grade> get grades {
    _$gradesAtom.reportRead();
    return super.grades;
  }

  @override
  set grades(List<Grade> value) {
    _$gradesAtom.reportWrite(value, super.grades, () {
      super.grades = value;
    });
  }

  late final _$fetchGradeCompletedAtom =
      Atom(name: '_MasterDataStore.fetchGradeCompleted', context: context);

  @override
  bool get fetchGradeCompleted {
    _$fetchGradeCompletedAtom.reportRead();
    return super.fetchGradeCompleted;
  }

  @override
  set fetchGradeCompleted(bool value) {
    _$fetchGradeCompletedAtom.reportWrite(value, super.fetchGradeCompleted, () {
      super.fetchGradeCompleted = value;
    });
  }

  late final _$listReasonOffAtom =
      Atom(name: '_MasterDataStore.listReasonOff', context: context);

  @override
  List<ReasonOff> get listReasonOff {
    _$listReasonOffAtom.reportRead();
    return super.listReasonOff;
  }

  @override
  set listReasonOff(List<ReasonOff> value) {
    _$listReasonOffAtom.reportWrite(value, super.listReasonOff, () {
      super.listReasonOff = value;
    });
  }

  late final _$fetchReasonOffCompletedAtom =
      Atom(name: '_MasterDataStore.fetchReasonOffCompleted', context: context);

  @override
  bool get fetchReasonOffCompleted {
    _$fetchReasonOffCompletedAtom.reportRead();
    return super.fetchReasonOffCompleted;
  }

  @override
  set fetchReasonOffCompleted(bool value) {
    _$fetchReasonOffCompletedAtom
        .reportWrite(value, super.fetchReasonOffCompleted, () {
      super.fetchReasonOffCompleted = value;
    });
  }

  late final _$errFetchReasonOffAtom =
      Atom(name: '_MasterDataStore.errFetchReasonOff', context: context);

  @override
  dynamic get errFetchReasonOff {
    _$errFetchReasonOffAtom.reportRead();
    return super.errFetchReasonOff;
  }

  @override
  set errFetchReasonOff(dynamic value) {
    _$errFetchReasonOffAtom.reportWrite(value, super.errFetchReasonOff, () {
      super.errFetchReasonOff = value;
    });
  }

  late final _$fetchCountriesAsyncAction =
      AsyncAction('_MasterDataStore.fetchCountries', context: context);

  @override
  Future<dynamic> fetchCountries() {
    return _$fetchCountriesAsyncAction.run(() => super.fetchCountries());
  }

  late final _$fetchProvincesAsyncAction =
      AsyncAction('_MasterDataStore.fetchProvinces', context: context);

  @override
  Future<dynamic> fetchProvinces({String countryCode = ''}) {
    return _$fetchProvincesAsyncAction
        .run(() => super.fetchProvinces(countryCode: countryCode));
  }

  late final _$fetchDistrictsAsyncAction =
      AsyncAction('_MasterDataStore.fetchDistricts', context: context);

  @override
  Future<dynamic> fetchDistricts(String provinceCode) {
    return _$fetchDistrictsAsyncAction
        .run(() => super.fetchDistricts(provinceCode));
  }

  late final _$fetchWardsAsyncAction =
      AsyncAction('_MasterDataStore.fetchWards', context: context);

  @override
  Future<dynamic> fetchWards(String districtCode) {
    return _$fetchWardsAsyncAction.run(() => super.fetchWards(districtCode));
  }

  late final _$fetchTrainingUnitsAsyncAction =
      AsyncAction('_MasterDataStore.fetchTrainingUnits', context: context);

  @override
  Future<dynamic> fetchTrainingUnits() {
    return _$fetchTrainingUnitsAsyncAction
        .run(() => super.fetchTrainingUnits());
  }

  late final _$fetchAcademicTitlesAsyncAction =
      AsyncAction('_MasterDataStore.fetchAcademicTitles', context: context);

  @override
  Future<dynamic> fetchAcademicTitles() {
    return _$fetchAcademicTitlesAsyncAction
        .run(() => super.fetchAcademicTitles());
  }

  late final _$fetchTermAndRulesAsyncAction =
      AsyncAction('_MasterDataStore.fetchTermAndRules', context: context);

  @override
  Future<dynamic> fetchTermAndRules(String languageCode) {
    return _$fetchTermAndRulesAsyncAction
        .run(() => super.fetchTermAndRules(languageCode));
  }

  late final _$fetchSubjectsAsyncAction =
      AsyncAction('_MasterDataStore.fetchSubjects', context: context);

  @override
  Future<dynamic> fetchSubjects(String languageCode) {
    return _$fetchSubjectsAsyncAction
        .run(() => super.fetchSubjects(languageCode));
  }

  late final _$fetchGradesAsyncAction =
      AsyncAction('_MasterDataStore.fetchGrades', context: context);

  @override
  Future<dynamic> fetchGrades(String languageCode) {
    return _$fetchGradesAsyncAction.run(() => super.fetchGrades(languageCode));
  }

  late final _$fetchListReasonOffAsyncAction =
      AsyncAction('_MasterDataStore.fetchListReasonOff', context: context);

  @override
  Future<dynamic> fetchListReasonOff() {
    return _$fetchListReasonOffAsyncAction
        .run(() => super.fetchListReasonOff());
  }

  late final _$_MasterDataStoreActionController =
      ActionController(name: '_MasterDataStore', context: context);

  @override
  void fetchAllData() {
    final _$actionInfo = _$_MasterDataStoreActionController.startAction(
        name: '_MasterDataStore.fetchAllData');
    try {
      return super.fetchAllData();
    } finally {
      _$_MasterDataStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
countries: ${countries},
districts: ${districts},
provinces: ${provinces},
wards: ${wards},
trainingUnits: ${trainingUnits},
academicTitles: ${academicTitles},
successFetchCountries: ${successFetchCountries},
successFetchProvinces: ${successFetchProvinces},
successFetchDistricts: ${successFetchDistricts},
successFetchWards: ${successFetchWards},
fetchTrainingUnitsCompleted: ${fetchTrainingUnitsCompleted},
fetchAcademicTitlesCompleted: ${fetchAcademicTitlesCompleted},
termAndRules: ${termAndRules},
fetchTermAndRulesCompleted: ${fetchTermAndRulesCompleted},
subjects: ${subjects},
fetchSubjectCompleted: ${fetchSubjectCompleted},
grades: ${grades},
fetchGradeCompleted: ${fetchGradeCompleted},
listReasonOff: ${listReasonOff},
fetchReasonOffCompleted: ${fetchReasonOffCompleted},
errFetchReasonOff: ${errFetchReasonOff}
    ''';
  }
}
