import 'package:mobx/mobx.dart';
import 'package:tutorO/data/repository/master_data_repository.dart';
import 'package:tutorO/models/academic_title/academic_title.dart';
import 'package:tutorO/models/grade/grade.dart';
import 'package:tutorO/models/master_data/country.dart';
import 'package:tutorO/models/master_data/district.dart';
import 'package:tutorO/models/master_data/province.dart';
import 'package:tutorO/models/master_data/ward.dart';
import 'package:tutorO/models/reason_off/reason_off.dart';
import 'package:tutorO/models/subject/subject.dart';
import 'package:tutorO/models/training_unit/training_unit.dart';
part 'master_data_store.g.dart';

class MasterDataStore = _MasterDataStore with _$MasterDataStore;

abstract class _MasterDataStore with Store {
  MasterDataRepository repository;

  _MasterDataStore(this.repository);

  @observable
  List<Country> countries = [];

  @observable
  List<District> districts = [];

  @observable
  List<Province> provinces = [];

  @observable
  List<Ward> wards = [];

  @observable
  List<TrainingUnit> trainingUnits = [];

  @observable
  List<AcademicTitle> academicTitles = [];

  @observable
  bool successFetchCountries = false;

  @action
  void fetchAllData() {
    fetchCountries();
    fetchProvinces(countryCode: '');
    fetchDistricts('');
    fetchWards('');
  }

  @action
  Future fetchCountries() async {
    successFetchCountries = false;
    countries = [];
    final future = repository.fetchCountries();
    future.then((result) {
      countries = result;
    }).whenComplete(() {
      successFetchCountries = true;
    });
  }

  @observable
  bool successFetchProvinces = false;

  @action
  Future fetchProvinces({String countryCode = ''}) async {
    successFetchProvinces = false;
    provinces = [];
    final future =
        repository.fetchProvinces(param: {'country_code': countryCode});
    future.then((result) {
      provinces = result;
    }).whenComplete(() => successFetchProvinces = true);
  }

  @observable
  bool successFetchDistricts = false;

  @action
  Future fetchDistricts(String provinceCode) async {
    successFetchDistricts = false;
    districts = [];
    final future =
        repository.fetchDistricts(param: {'province_code': provinceCode});
    future.then((result) {
      districts = result;
    }).whenComplete(() => successFetchDistricts = true);
  }

  @observable
  bool successFetchWards = false;

  @action
  Future fetchWards(String districtCode) async {
    successFetchWards = false;
    wards = [];
    final future =
        repository.fetchWards(param: {'district_code': districtCode});
    future.then((result) {
      wards = result;
    }).whenComplete(() => successFetchWards = true);
  }

  @observable
  bool fetchTrainingUnitsCompleted = false;

  @action
  Future fetchTrainingUnits() async {
    fetchTrainingUnitsCompleted = false;
    repository.fetchTrainingUnits().then((trainingUnits) {
      this.trainingUnits = trainingUnits;
    }).whenComplete(() => fetchTrainingUnitsCompleted = true);
  }

  @observable
  bool fetchAcademicTitlesCompleted = false;

  @action
  Future fetchAcademicTitles() async {
    fetchAcademicTitlesCompleted = false;
    repository.fetchAcademicTitles().then((academicTitles) {
      this.academicTitles = academicTitles;
    }).whenComplete(() => fetchAcademicTitlesCompleted = true);
  }

  @observable
  String termAndRules = "";

  @observable
  bool fetchTermAndRulesCompleted = false;

  @action
  Future fetchTermAndRules(String languageCode) async {
    fetchTermAndRulesCompleted = false;
    repository.fetchTermAndRules(languageCode).then((termAndRules) {
      this.termAndRules = termAndRules;
    }).whenComplete(() => fetchTermAndRulesCompleted = true);
  }

  // Subject
  @observable
  List<Subject> subjects = [];

  @observable
  bool fetchSubjectCompleted = false;

  @action
  Future fetchSubjects(String languageCode) async {
    fetchSubjectCompleted = false;
    repository.fetchSubjects(languageCode).then((subjects) {
      this.subjects = subjects;
    }).whenComplete(() => fetchSubjectCompleted = true);
  }

  // Grade
  @observable
  List<Grade> grades = [];

  @observable
  bool fetchGradeCompleted = false;

  @action
  Future fetchGrades(String languageCode) async {
    fetchGradeCompleted = false;
    repository.fetchGrades(languageCode).then((grades) {
      this.grades = grades;
    }).whenComplete(() => fetchGradeCompleted = true);
  }

  @observable
  List<ReasonOff> listReasonOff = [];

  @observable
  bool fetchReasonOffCompleted = false;

  @observable
  dynamic errFetchReasonOff;

  @action
  Future fetchListReasonOff() async {
    final future = repository.fetchReasonOffLesson();
    errFetchReasonOff = null;
    fetchReasonOffCompleted = false;
    future.then((listReasonOff) {
      this.listReasonOff = listReasonOff;
    }).catchError((error) {
      errFetchReasonOff = error;
    }).whenComplete(() => fetchReasonOffCompleted = true);
  }
}
