// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'work_exp_store.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$WorkExpStore on _WorkExpStore, Store {
  Computed<bool>? _$fetchingProfessionalExperienceComputed;

  @override
  bool get fetchingProfessionalExperience =>
      (_$fetchingProfessionalExperienceComputed ??= Computed<bool>(
              () => super.fetchingProfessionalExperience,
              name: '_WorkExpStore.fetchingProfessionalExperience'))
          .value;

  late final _$listProfessionalExperienceAtom =
      Atom(name: '_WorkExpStore.listProfessionalExperience', context: context);

  @override
  List<WorkExp> get listProfessionalExperience {
    _$listProfessionalExperienceAtom.reportRead();
    return super.listProfessionalExperience;
  }

  @override
  set listProfessionalExperience(List<WorkExp> value) {
    _$listProfessionalExperienceAtom
        .reportWrite(value, super.listProfessionalExperience, () {
      super.listProfessionalExperience = value;
    });
  }

  late final _$isSuccessExpAtom =
      Atom(name: '_WorkExpStore.isSuccessExp', context: context);

  @override
  bool get isSuccessExp {
    _$isSuccessExpAtom.reportRead();
    return super.isSuccessExp;
  }

  @override
  set isSuccessExp(bool value) {
    _$isSuccessExpAtom.reportWrite(value, super.isSuccessExp, () {
      super.isSuccessExp = value;
    });
  }

  late final _$fetchProfessionalExpFutureAtom =
      Atom(name: '_WorkExpStore.fetchProfessionalExpFuture', context: context);

  @override
  ObservableFuture<List<WorkExp>?> get fetchProfessionalExpFuture {
    _$fetchProfessionalExpFutureAtom.reportRead();
    return super.fetchProfessionalExpFuture;
  }

  @override
  set fetchProfessionalExpFuture(ObservableFuture<List<WorkExp>?> value) {
    _$fetchProfessionalExpFutureAtom
        .reportWrite(value, super.fetchProfessionalExpFuture, () {
      super.fetchProfessionalExpFuture = value;
    });
  }

  late final _$errorFetchProfessionExpAtom =
      Atom(name: '_WorkExpStore.errorFetchProfessionExp', context: context);

  @override
  WorkExpError? get errorFetchProfessionExp {
    _$errorFetchProfessionExpAtom.reportRead();
    return super.errorFetchProfessionExp;
  }

  @override
  set errorFetchProfessionExp(WorkExpError? value) {
    _$errorFetchProfessionExpAtom
        .reportWrite(value, super.errorFetchProfessionExp, () {
      super.errorFetchProfessionExp = value;
    });
  }

  late final _$isRequestProfessionExpAtom =
      Atom(name: '_WorkExpStore.isRequestProfessionExp', context: context);

  @override
  bool get isRequestProfessionExp {
    _$isRequestProfessionExpAtom.reportRead();
    return super.isRequestProfessionExp;
  }

  @override
  set isRequestProfessionExp(bool value) {
    _$isRequestProfessionExpAtom
        .reportWrite(value, super.isRequestProfessionExp, () {
      super.isRequestProfessionExp = value;
    });
  }

  late final _$errorAddOrUpdateProfessionalExperienceAtom = Atom(
      name: '_WorkExpStore.errorAddOrUpdateProfessionalExperience',
      context: context);

  @override
  WorkExpError? get errorAddOrUpdateProfessionalExperience {
    _$errorAddOrUpdateProfessionalExperienceAtom.reportRead();
    return super.errorAddOrUpdateProfessionalExperience;
  }

  @override
  set errorAddOrUpdateProfessionalExperience(WorkExpError? value) {
    _$errorAddOrUpdateProfessionalExperienceAtom
        .reportWrite(value, super.errorAddOrUpdateProfessionalExperience, () {
      super.errorAddOrUpdateProfessionalExperience = value;
    });
  }

  late final _$requestDeleteProfessionExpAtom =
      Atom(name: '_WorkExpStore.requestDeleteProfessionExp', context: context);

  @override
  bool get requestDeleteProfessionExp {
    _$requestDeleteProfessionExpAtom.reportRead();
    return super.requestDeleteProfessionExp;
  }

  @override
  set requestDeleteProfessionExp(bool value) {
    _$requestDeleteProfessionExpAtom
        .reportWrite(value, super.requestDeleteProfessionExp, () {
      super.requestDeleteProfessionExp = value;
    });
  }

  late final _$errorDeleteProfessionExpAtom =
      Atom(name: '_WorkExpStore.errorDeleteProfessionExp', context: context);

  @override
  WorkExpError? get errorDeleteProfessionExp {
    _$errorDeleteProfessionExpAtom.reportRead();
    return super.errorDeleteProfessionExp;
  }

  @override
  set errorDeleteProfessionExp(WorkExpError? value) {
    _$errorDeleteProfessionExpAtom
        .reportWrite(value, super.errorDeleteProfessionExp, () {
      super.errorDeleteProfessionExp = value;
    });
  }

  late final _$fetchProfessionalExpAsyncAction =
      AsyncAction('_WorkExpStore.fetchProfessionalExp', context: context);

  @override
  Future<dynamic> fetchProfessionalExp() {
    return _$fetchProfessionalExpAsyncAction
        .run(() => super.fetchProfessionalExp());
  }

  late final _$addWorkExpAsyncAction =
      AsyncAction('_WorkExpStore.addWorkExp', context: context);

  @override
  Future<dynamic> addWorkExp(WorkExp professionalExp, File image) {
    return _$addWorkExpAsyncAction
        .run(() => super.addWorkExp(professionalExp, image));
  }

  late final _$updateWorkExpAsyncAction =
      AsyncAction('_WorkExpStore.updateWorkExp', context: context);

  @override
  Future<dynamic> updateWorkExp(WorkExp professionalExp, File? image) {
    return _$updateWorkExpAsyncAction
        .run(() => super.updateWorkExp(professionalExp, image));
  }

  late final _$deleteWorkExpAsyncAction =
      AsyncAction('_WorkExpStore.deleteWorkExp', context: context);

  @override
  Future<dynamic> deleteWorkExp(int id) {
    return _$deleteWorkExpAsyncAction.run(() => super.deleteWorkExp(id));
  }

  @override
  String toString() {
    return '''
listProfessionalExperience: ${listProfessionalExperience},
isSuccessExp: ${isSuccessExp},
fetchProfessionalExpFuture: ${fetchProfessionalExpFuture},
errorFetchProfessionExp: ${errorFetchProfessionExp},
isRequestProfessionExp: ${isRequestProfessionExp},
errorAddOrUpdateProfessionalExperience: ${errorAddOrUpdateProfessionalExperience},
requestDeleteProfessionExp: ${requestDeleteProfessionExp},
errorDeleteProfessionExp: ${errorDeleteProfessionExp},
fetchingProfessionalExperience: ${fetchingProfessionalExperience}
    ''';
  }
}
