import 'dart:io';

import 'package:mobx/mobx.dart';
import 'package:tutorO/data/repository/work_experience_repository.dart';
import 'package:tutorO/models/profile/professional_experience.dart';
import 'package:tutorO/utils/image/image.dart';
part 'work_exp_store.g.dart';

class WorkExpStore = _WorkExpStore with _$WorkExpStore;

abstract class _WorkExpStore with Store {
  final WorkExpRepository _professionExperienceRepository;

  _WorkExpStore({required WorkExpRepository professionExperienceRepository})
      : _professionExperienceRepository = professionExperienceRepository;

  @observable
  List<WorkExp> listProfessionalExperience = [];

  @observable
  bool isSuccessExp = false;

  @observable
  ObservableFuture<List<WorkExp>?> fetchProfessionalExpFuture =
      ObservableFuture.value(null);

  @computed
  bool get fetchingProfessionalExperience =>
      fetchProfessionalExpFuture.status == FutureStatus.pending;

  @observable
  WorkExpError? errorFetchProfessionExp;

  @action
  Future fetchProfessionalExp() async {
    isSuccessExp = false;
    errorFetchProfessionExp = null;
    listProfessionalExperience = [];
    final future = _professionExperienceRepository.fetchProfessionalEx();
    fetchProfessionalExpFuture = ObservableFuture(future);
    future.then((exp) {
      listProfessionalExperience = exp;
    }).whenComplete(() {
      isSuccessExp = true;
    });
  }

  @observable
  bool isRequestProfessionExp = false;

  @observable
  WorkExpError? errorAddOrUpdateProfessionalExperience;

  @action
  Future addWorkExp(WorkExp professionalExp, File image) async {
    isRequestProfessionExp = false;
    errorAddOrUpdateProfessionalExperience = null;
    final future = _professionExperienceRepository.addProfessionalExperience(
        professionalExp, await ImageConvert.compressFile(image));
    future.then((exp) {
      listProfessionalExperience.add(professionalExp);
    }).catchError((onError) {
      errorAddOrUpdateProfessionalExperience = onError as WorkExpError;
    }).whenComplete(() => isRequestProfessionExp = true);
  }

  @action
  Future updateWorkExp(WorkExp professionalExp, File? image) async {
    isRequestProfessionExp = false;
    errorAddOrUpdateProfessionalExperience = null;
    final future = _professionExperienceRepository.updateProfessionalExperience(
        professionalExp,
        image != null ? await ImageConvert.compressFile(image) : null);
    future.then((exp) {
      listProfessionalExperience.add(professionalExp);
    }).catchError((onError) {
      errorAddOrUpdateProfessionalExperience = onError as WorkExpError;
    }).whenComplete(() => isRequestProfessionExp = true);
  }

  @observable
  bool requestDeleteProfessionExp = false;

  @observable
  WorkExpError? errorDeleteProfessionExp;

  @action
  Future deleteWorkExp(int id) async {
    errorDeleteProfessionExp = null;
    requestDeleteProfessionExp = false;
    final future = _professionExperienceRepository.deleteProfessionalExp(id);
    future.then((_) {
      listProfessionalExperience.removeWhere((element) => element.id == id);
    }).catchError((error) {
      if (error is WorkExpError) {
        errorDeleteProfessionExp = error;
      }
    }).whenComplete(() => requestDeleteProfessionExp = true);
  }
}
