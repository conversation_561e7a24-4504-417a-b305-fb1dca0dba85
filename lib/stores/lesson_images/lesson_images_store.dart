import 'package:mobx/mobx.dart';
import '../../data/repository/class_repository.dart';
import '../../models/lesson_image/lesson_image.dart';
import '../../widgets/media_picker_modal/media_picker_modal.dart';

// Include generated file
part 'lesson_images_store.g.dart';

class LessonImagesStore = _LessonImagesStore with _$LessonImagesStore;

// The store-class
abstract class _LessonImagesStore with Store {
  final ClassRepository _classRepository;

  _LessonImagesStore({required ClassRepository classRepository})
      : _classRepository = classRepository;

  @observable
  List<LessonImage> listLessonImagesAndVideo = [];

  @observable
  dynamic fetchListLessonImagesAndVideoError;

  @observable
  bool fetchListLessonImagesAndVideoCompleted = false;

  @action
  Future fetchListLessonAndVideo(int sessionId) async {
    final future = _classRepository.fetchListLessonAndVideo(sessionId);
    fetchListLessonImagesAndVideoCompleted = false;
    fetchListLessonImagesAndVideoError = null;
    future.then((listLessonImagesAndVideo) {
      this.listLessonImagesAndVideo = listLessonImagesAndVideo;
    }).catchError((error) {
      print(error);
      fetchListLessonImagesAndVideoError = error;
    }).whenComplete(() => fetchListLessonImagesAndVideoCompleted = true);
  }

  @observable
  dynamic removeImageOrVideoError;

  @observable
  bool removeImageOrVideoCompleted = false;

  @action
  Future removeImageOrVideo(int sessionId, Map<String, dynamic> data) async {
    final future = _classRepository.removeImageOrVideo(sessionId, data);
    removeImageOrVideoCompleted = false;
    removeImageOrVideoError = null;
    future.then((_) {
      listLessonImagesAndVideo
          .removeWhere((element) => element.id == sessionId);
    }).catchError((error) {
      print(error);
      removeImageOrVideoError = error as LessonImageError;
    }).whenComplete(() => removeImageOrVideoCompleted = true);
  }

  @observable
  bool uploadImageOrVideoSuccess = false;

  @observable
  dynamic uploadImageOrVideoError;

  @action
  Future uploadImageOrVideo(MediaPickedFile images, int sessionId) async {
    uploadImageOrVideoSuccess = false;
    uploadImageOrVideoError = null;
    final future = _classRepository.uploadImageOrVideo(images, sessionId);
    future.then((images) {
      listLessonImagesAndVideo = images;
    }).catchError((error) {
      uploadImageOrVideoError = error as LessonImageError;
    }).whenComplete(() => uploadImageOrVideoSuccess = true);
  }
}
