// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lesson_images_store.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$LessonImagesStore on _LessonImagesStore, Store {
  late final _$listLessonImagesAndVideoAtom = Atom(
      name: '_LessonImagesStore.listLessonImagesAndVideo', context: context);

  @override
  List<LessonImage> get listLessonImagesAndVideo {
    _$listLessonImagesAndVideoAtom.reportRead();
    return super.listLessonImagesAndVideo;
  }

  @override
  set listLessonImagesAndVideo(List<LessonImage> value) {
    _$listLessonImagesAndVideoAtom
        .reportWrite(value, super.listLessonImagesAndVideo, () {
      super.listLessonImagesAndVideo = value;
    });
  }

  late final _$fetchListLessonImagesAndVideoErrorAtom = Atom(
      name: '_LessonImagesStore.fetchListLessonImagesAndVideoError',
      context: context);

  @override
  dynamic get fetchListLessonImagesAndVideoError {
    _$fetchListLessonImagesAndVideoErrorAtom.reportRead();
    return super.fetchListLessonImagesAndVideoError;
  }

  @override
  set fetchListLessonImagesAndVideoError(dynamic value) {
    _$fetchListLessonImagesAndVideoErrorAtom
        .reportWrite(value, super.fetchListLessonImagesAndVideoError, () {
      super.fetchListLessonImagesAndVideoError = value;
    });
  }

  late final _$fetchListLessonImagesAndVideoCompletedAtom = Atom(
      name: '_LessonImagesStore.fetchListLessonImagesAndVideoCompleted',
      context: context);

  @override
  bool get fetchListLessonImagesAndVideoCompleted {
    _$fetchListLessonImagesAndVideoCompletedAtom.reportRead();
    return super.fetchListLessonImagesAndVideoCompleted;
  }

  @override
  set fetchListLessonImagesAndVideoCompleted(bool value) {
    _$fetchListLessonImagesAndVideoCompletedAtom
        .reportWrite(value, super.fetchListLessonImagesAndVideoCompleted, () {
      super.fetchListLessonImagesAndVideoCompleted = value;
    });
  }

  late final _$removeImageOrVideoErrorAtom = Atom(
      name: '_LessonImagesStore.removeImageOrVideoError', context: context);

  @override
  dynamic get removeImageOrVideoError {
    _$removeImageOrVideoErrorAtom.reportRead();
    return super.removeImageOrVideoError;
  }

  @override
  set removeImageOrVideoError(dynamic value) {
    _$removeImageOrVideoErrorAtom
        .reportWrite(value, super.removeImageOrVideoError, () {
      super.removeImageOrVideoError = value;
    });
  }

  late final _$removeImageOrVideoCompletedAtom = Atom(
      name: '_LessonImagesStore.removeImageOrVideoCompleted', context: context);

  @override
  bool get removeImageOrVideoCompleted {
    _$removeImageOrVideoCompletedAtom.reportRead();
    return super.removeImageOrVideoCompleted;
  }

  @override
  set removeImageOrVideoCompleted(bool value) {
    _$removeImageOrVideoCompletedAtom
        .reportWrite(value, super.removeImageOrVideoCompleted, () {
      super.removeImageOrVideoCompleted = value;
    });
  }

  late final _$uploadImageOrVideoSuccessAtom = Atom(
      name: '_LessonImagesStore.uploadImageOrVideoSuccess', context: context);

  @override
  bool get uploadImageOrVideoSuccess {
    _$uploadImageOrVideoSuccessAtom.reportRead();
    return super.uploadImageOrVideoSuccess;
  }

  @override
  set uploadImageOrVideoSuccess(bool value) {
    _$uploadImageOrVideoSuccessAtom
        .reportWrite(value, super.uploadImageOrVideoSuccess, () {
      super.uploadImageOrVideoSuccess = value;
    });
  }

  late final _$uploadImageOrVideoErrorAtom = Atom(
      name: '_LessonImagesStore.uploadImageOrVideoError', context: context);

  @override
  dynamic get uploadImageOrVideoError {
    _$uploadImageOrVideoErrorAtom.reportRead();
    return super.uploadImageOrVideoError;
  }

  @override
  set uploadImageOrVideoError(dynamic value) {
    _$uploadImageOrVideoErrorAtom
        .reportWrite(value, super.uploadImageOrVideoError, () {
      super.uploadImageOrVideoError = value;
    });
  }

  late final _$fetchListLessonAndVideoAsyncAction = AsyncAction(
      '_LessonImagesStore.fetchListLessonAndVideo',
      context: context);

  @override
  Future<dynamic> fetchListLessonAndVideo(int sessionId) {
    return _$fetchListLessonAndVideoAsyncAction
        .run(() => super.fetchListLessonAndVideo(sessionId));
  }

  late final _$removeImageOrVideoAsyncAction =
      AsyncAction('_LessonImagesStore.removeImageOrVideo', context: context);

  @override
  Future<dynamic> removeImageOrVideo(int sessionId, Map<String, dynamic> data) {
    return _$removeImageOrVideoAsyncAction
        .run(() => super.removeImageOrVideo(sessionId, data));
  }

  late final _$uploadImageOrVideoAsyncAction =
      AsyncAction('_LessonImagesStore.uploadImageOrVideo', context: context);

  @override
  Future<dynamic> uploadImageOrVideo(MediaPickedFile images, int sessionId) {
    return _$uploadImageOrVideoAsyncAction
        .run(() => super.uploadImageOrVideo(images, sessionId));
  }

  @override
  String toString() {
    return '''
listLessonImagesAndVideo: ${listLessonImagesAndVideo},
fetchListLessonImagesAndVideoError: ${fetchListLessonImagesAndVideoError},
fetchListLessonImagesAndVideoCompleted: ${fetchListLessonImagesAndVideoCompleted},
removeImageOrVideoError: ${removeImageOrVideoError},
removeImageOrVideoCompleted: ${removeImageOrVideoCompleted},
uploadImageOrVideoSuccess: ${uploadImageOrVideoSuccess},
uploadImageOrVideoError: ${uploadImageOrVideoError}
    ''';
  }
}
