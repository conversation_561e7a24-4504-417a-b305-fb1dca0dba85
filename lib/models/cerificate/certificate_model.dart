import 'dart:io';

import 'package:dio/dio.dart';
import '../../utils/mixin/error_response_extractor.dart';

///
class CertificationModel {
  int? _id;
  int? _odooCertificateId;
  String? _certificateName;
  String? _grade;
  String? _certificateAuthority;
  String? _certificateFile;
  int? _qualifiedYear;
  bool? _verifyStatus;
  String? _updatedAt;
  int? _user;
  String? _urlCertificateImage;

  ///
  int? get id => _id;

  int? get odooCertificateId => _odooCertificateId;

  String? get certificateName => _certificateName;

  String? get grade => _grade;

  String? get certificateAuthority => _certificateAuthority;

  String? get certificateFile => _certificateFile;

  int? get qualifiedYear => _qualifiedYear;

  bool? get verifyStatus => _verifyStatus;

  String? get updatedAt => _updatedAt;

  int? get user => _user;

  String? get urlCertificateImage => _urlCertificateImage;

  /// Constructor
  CertificationModel({
    int? id,
    int? odooCertificateId,
    String? certificateName,
    String? grade,
    String? certificateAuthority,
    String? certificateFile,
    int? qualifiedYear,
    bool? verifyStatus,
    String? updatedAt,
    int? user,
    String? certificateImage,
  }) {
    _id = id;
    _odooCertificateId = odooCertificateId;
    _certificateName = certificateName;
    _grade = grade;
    _certificateAuthority = certificateAuthority;
    _certificateFile = certificateFile;
    _qualifiedYear = qualifiedYear;
    _verifyStatus = verifyStatus;
    _updatedAt = updatedAt;
    _user = user;
    _urlCertificateImage = certificateImage;
  }

  CertificationModel.fromJson(dynamic json) {
    _id = json["id"];
    _odooCertificateId = json["odoo_certificate_id"];
    _certificateName = json["certificate_name"];
    _grade = json["grade"];
    _certificateAuthority = json["certificate_authority"];
    _certificateFile = json["certificate_file"];
    _qualifiedYear = json["qualified_year"];
    _verifyStatus = json["verify_status"];
    _updatedAt = json["updated_at"];
    _user = json["user"];
    _urlCertificateImage = json["certificate_file"];
  }

  Map<String, dynamic> toJson() {
    var map = <String, dynamic>{};
    map["id"] = _id;
    map["certificate_name"] = _certificateName;
    map["grade"] = _grade;
    map["certificate_authority"] = _certificateAuthority;
    map["qualified_year"] = _qualifiedYear;
    map["certificate_file"] = _certificateFile;
    map["odoo_certificate_id"] = _odooCertificateId;
    map["verify_status"] = _verifyStatus;
    map["updated_at"] = _updatedAt;
    map["user"] = _user;
    return map;
  }

  Future<FormData> toFormData(File? file) async {
    var data = {
      "certificate_name": _certificateName,
      "grade": _grade,
      "certificate_authority": _certificateAuthority,
      "qualified_year": _qualifiedYear,
    };
    if (file != null) {
      data["certificate_file"] = file.path.isNotEmpty
          ? await MultipartFile.fromFile(file.path,
              filename: 'image_certificate_file.png')
          : _urlCertificateImage;
    }
    var formData = FormData.fromMap(data);
    return formData;
  }
}

/// Error for certification API
class CertificationError with ErrorResponseExtractor {
  String? errCertificateName;
  String? errGrade;
  String? errCertificateAuthority;
  String? errQualifiedYear;
  String? errCertificateImage;

  /// Constructor
  CertificationError(
      {this.errCertificateName,
      this.errGrade,
      this.errCertificateAuthority,
      this.errQualifiedYear});

  /// Convert json map to object
  CertificationError.fromJsonMap(Map<String, dynamic> jsonData) {
    errCertificateName = extractErrorString(jsonData, "certificate_name");
    errGrade = extractErrorString(jsonData, "grade");
    errCertificateAuthority =
        extractErrorString(jsonData, "certificate_authority");
    errQualifiedYear = extractErrorString(jsonData, "qualified_year");
    errCertificateImage = extractErrorString(jsonData, "certificate_file");
  }
}
