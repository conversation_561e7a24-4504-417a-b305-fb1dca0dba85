/// language_code : "en"
/// name : "Primary school Mathematics"
/// slug : "Primary school Mathematics"
/// description : "Primary school Mathematics"

class Subject {
  late int _id;
  late int _odooSubjectId;
  String? _languageCode;
  String? _name;
  String? _slug;
  String? _description;

  int get id => _id;
  int get odooSubjectId => _odooSubjectId;
  String? get languageCode => _languageCode;
  String? get name => _name;
  String? get slug => _slug;
  String? get description => _description;

  Subject(
      {required int id, required int odooSubjectId, String? languageCode, String? name, String? slug, String? description}) {
    _id = id;
    _odooSubjectId = odooSubjectId;
    _languageCode = languageCode;
    _name = name;
    _slug = slug;
    _description = description;
  }

  Subject.fromJson(dynamic json) {
    _id = json["id"];
    _odooSubjectId = json["odoo_subject_id"];
    _languageCode = json["language_code"];
    _name = json["name"];
    _slug = json["slug"];
    _description = json["description"];
  }

  Map<String, dynamic> toJson() {
    var map = <String, dynamic>{};
    map["language_code"] = _languageCode;
    map["name"] = _name;
    map["slug"] = _slug;
    map["description"] = _description;
    return map;
  }
}
