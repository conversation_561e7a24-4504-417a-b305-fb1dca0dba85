import 'dart:io';

import 'package:dio/dio.dart';

import '../../utils/mixin/error_response_extractor.dart';

/// id : 1
/// odoo_academic_id : 1
/// grade : "Xuất sắc"
/// qualified_year : 2012
/// type_training : "Chính quy"
/// verify_status : false
/// updated_at : "2021-05-10T06:30:09.781256Z"
/// user : 9
/// academic_id : "Thạ<PERSON> sĩ"
/// training_unit : "Đ<PERSON>i học <PERSON> ngữ - ĐHQGHN"

class AcademicLevel {
  final int? _id;
  final int? _odooAcademicId;
  final String _grade;
  final String _major;
  final int? _qualifiedYear;
  final String? _typeTraining;
  final bool _verifyStatus;
  final String? _updatedAt;
  final int? _user;
  final int _academicId;
  final String _academicIdAsText;
  final int _trainingUnit;
  final String _trainingUnitAsText;
  final String _academicFile;

  int? get id => _id;
  int? get odooAcademicId => _odooAcademicId;
  String get grade => _grade;
  String get major => _major;
  int? get qualifiedYear => _qualifiedYear;
  String? get typeTraining => _typeTraining;
  bool get verifyStatus => _verifyStatus;
  String? get updatedAt => _updatedAt;
  int? get user => _user;
  int get academicId => _academicId;
  String get academicIdAsText => _academicIdAsText;
  int get trainingUnit => _trainingUnit;
  String get academicFile => _academicFile;
  String get trainingUnitAsText => _trainingUnitAsText;

  AcademicLevel({
    int? id,
    int? odooAcademicId,
    required String grade,
    int? qualifiedYear,
    String? typeTraining,
    String? major,
    required bool verifyStatus,
    String? updatedAt,
    int? user,
    required int academicId,
    String? academicIdAsText,
    required int trainingUnit,
    String? academicFile,
    String? trainingUnitAsText,
  })  : _id = id,
        _odooAcademicId = odooAcademicId,
        _grade = grade,
        _qualifiedYear = qualifiedYear,
        _typeTraining = typeTraining,
        _verifyStatus = verifyStatus,
        _updatedAt = updatedAt,
        _user = user,
        _academicId = academicId,
        _academicIdAsText = academicIdAsText ?? "",
        _trainingUnit = trainingUnit,
        _academicFile = academicFile ?? "",
        _major = major ?? "",
        _trainingUnitAsText = trainingUnitAsText ?? "";

  AcademicLevel.fromJson(dynamic json)
      : _id = json["id"],
        _odooAcademicId = json["odoo_academic_id"] ?? -1,
        _grade = json["grade"],
        _qualifiedYear = json["qualified_year"],
        _typeTraining = json["type_training"],
        _verifyStatus = json["verify_status"],
        _updatedAt = json["updated_at"],
        _user = json["user"],
        _academicId = json["academic_id"],
        _academicIdAsText = json["academic_title_as_text"] ?? "",
        _major = json["major"] ?? "",
        _trainingUnit = json["training_unit_id"],
        _academicFile = json["academic_file"] ?? "",
        _trainingUnitAsText = json["training_unit_as_text"] ?? "";

  Map<String, dynamic> toJson() {
    var map = <String, dynamic>{};
    map["grade"] = _grade;
    map["qualified_year"] = _qualifiedYear;
    // map["type_training"] = _typeTraining;
    map["major"] = _major;
    map["academic_id"] = _academicId;
    map["training_unit_id"] = _trainingUnit;
    return map;
  }

  Future<FormData> toFormData(File? file) async {
    var mapData = <String, dynamic>{
      "grade": _grade,
      "qualified_year": _qualifiedYear,
      "major": _major,
      "academic_id": _academicId,
      "training_unit_id": _trainingUnit,
    };
    if (file != null) {
      mapData["academic_file"] = file.path.isNotEmpty
          ? await MultipartFile.fromFile(file.path,
              filename: 'academic_file.png')
          : _academicFile;
    }
    var formData = FormData.fromMap(mapData);
    return formData;
  }
}

class AcademicLevelError with ErrorResponseExtractor {
  String? errTitle;
  String? errTrainingUnit;
  String? errGrade;
  String? errQualifiedYear;
  String? errMajor;
  String? errAcademicFile;

  AcademicLevelError(
      {this.errTitle,
      this.errTrainingUnit,
      this.errGrade,
      this.errQualifiedYear,
      this.errAcademicFile,
      this.errMajor});

  AcademicLevelError.fromJsonMap(Map<String, dynamic> jsonData) {
    errTitle = extractErrorString(jsonData, "academic_id");
    errTrainingUnit = extractErrorString(jsonData, "training_unit");
    errGrade = extractErrorString(jsonData, "grade");
    errQualifiedYear = extractErrorString(jsonData, "qualified_year");
    errMajor = extractErrorString(jsonData, "major");
    errAcademicFile = extractErrorString(jsonData, "academic_file");
  }
}
