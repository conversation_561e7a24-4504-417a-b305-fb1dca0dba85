import 'package:tutorO/utils/mixin/error_response_extractor.dart';

/// id : 1
/// name : "Đáng giá cuối buổi teky"
/// list_criterias : [{"id":1,"name":" <PERSON><PERSON><PERSON> năng tiếp thu kiến thức bài học mới","type":"SINGLE_CHOICE","sequence":"1","list_answers":["Câu trả lời 1","Câu trả lời 2"],"tag":{"id":0,"name":""}},{"id":2,"name":"Câu hỏi 2","type":"SINGLE_CHOICE","sequence":"2","list_answers":["Câu trả lời 1","Câu trả lời 2"],"tag":{"id":0,"name":""}},{"id":3,"name":"Câu hỏi 3","type":"SINGLE_CHOICE","sequence":"1","list_answers":["Câu trả lời 1","Câu trả lời 2"],"tag":{"id":0,"name":""}}]

class EvaluationTemplateModel {
  int? _id;
  String? _name;
  List<EvaluationCriteriaModel>? _listCriterias;

  int? get id => _id;
  String? get name => _name;
  List<EvaluationCriteriaModel>? get listCriterias => _listCriterias;

  EvaluationTemplateModel(
      {int? id, String? name, List<EvaluationCriteriaModel>? listCriterias}) {
    _id = id;
    _name = name;
    _listCriterias = listCriterias;
  }

  EvaluationTemplateModel.fromJson(dynamic json) {
    _id = json["id"];
    _name = json["name"];
    if (json["list_criterias"] != null) {
      _listCriterias = [];
      json["list_criterias"].forEach((v) {
        _listCriterias?.add(EvaluationCriteriaModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    var map = <String, dynamic>{};
    map["id"] = _id;
    map["name"] = _name;
    if (_listCriterias != null) {
      map["evaluated_criterias"] =
          _listCriterias?.map((v) => v.toJson()).toList();
    }
    return map;
  }
}

/// id : 1
/// name : " Khả năng tiếp thu kiến thức bài học mới"
/// type : "SINGLE_CHOICE"
/// sequence : "1"
/// list_answers : ["Câu trả lời 1","Câu trả lời 2"]
/// tag : {"id":0,"name":""}
///

enum EvaluationCriteriaType {
  singleChoice,
  multipleChoices,
  singleLineText,
  multipleLineText,
  number
}

extension ToString on EvaluationCriteriaType {
  String get value {
    switch (this) {
      case EvaluationCriteriaType.singleChoice:
        return "SINGLE_CHOICE";
      case EvaluationCriteriaType.multipleChoices:
        return "MULTIPLE_CHOICE";
      case EvaluationCriteriaType.singleLineText:
        return "SINGLE_LINE";
      case EvaluationCriteriaType.multipleLineText:
        return "MULTIPLE_LINE";
      case EvaluationCriteriaType.number:
        return "NUMBER";
    }
  }
}

class EvaluationCriteriaModel {
  int? _id;
  int? _point;
  String? _name;
  EvaluationCriteriaType? _type;
  String? _sequence;
  List<String>? listAnswers;
  EvaluationCriteriaTagModel? _tag;
  List<ListPointAnswer>? _listPointAnswers;
  List<String>? answers;

  int? get id => _id;
  int? get point => _point;
  String? get name => _name;
  EvaluationCriteriaType? get type => _type;
  String? get sequence => _sequence;
  EvaluationCriteriaTagModel? get tag => _tag;

  EvaluationCriteriaModel(
      {int? id,
      int? point,
      String? name,
      EvaluationCriteriaType? type,
      String? sequence,
      List<ListPointAnswer>? listPointAnswers,
      List<String>? listAnswers,
      EvaluationCriteriaTagModel? tag}) {
    _id = id;
    _name = name;
    _type = type;
    _sequence = sequence;
    listAnswers = listAnswers;
    _listPointAnswers = listPointAnswers;

    _tag = tag;
  }

  EvaluationCriteriaModel.fromJson(dynamic json) {
    _id = json["id"];
    _name = json["name"];
    _type = convertToEvaluationCriteriaType(json["type"]);
    _sequence = json["sequence"];
    _listPointAnswers = (json["list_point_answers"] as List)
        .map((e) => ListPointAnswer.fromJson(e))
        .toList();
    listAnswers =
        json["list_answers"] != null ? json["list_answers"].cast<String>() : [];
    _tag = json["tag"] != null
        ? EvaluationCriteriaTagModel.fromJson(json["tag"])
        : null;
    // _point = json["point"];
  }

  Map<String, dynamic> toJson() {
    var map = <String, dynamic>{};
    int? value = 0;
    map["id"] = _id;
    map["name"] = _name;
    map["type"] = _type?.value;
    map["sequence"] = _sequence;
    if (answers != null) {
      map["answer"] = answers!.length == 1 ? answers!.first : answers;
      if (_listPointAnswers != null && _listPointAnswers!.length >= 1) {
        value = _listPointAnswers!
            .where((e) => e._name == answers!.first)
            .first
            ._point;
        if (_listPointAnswers?.first._point != null) {
          map["point"] = value;
        }
        // value = _listPointAnswers?.first._point;
        // if (_listPointAnswers?.first._point != null) {
        //   map["point"] = value;
        // }
      }
    }
    // if (value != null) {
    // }
    return map;
  }

  EvaluationCriteriaType convertToEvaluationCriteriaType(String value) {
    switch (value) {
      case 'SINGLE_CHOICE':
        return EvaluationCriteriaType.singleChoice;
      case 'MULTIPLE_CHOICE':
        return EvaluationCriteriaType.singleChoice;
      case 'SINGLE_LINE':
        return EvaluationCriteriaType.singleLineText;
      case 'NUMBER':
        return EvaluationCriteriaType.number;
      default:
        return EvaluationCriteriaType.multipleLineText;
    }
  }
}

class ListPointAnswer {
  String? _name;
  int? _point;

  String? get name => _name;
  int? get point => _point;
  ListPointAnswer({int? name, String? point});

  ListPointAnswer.fromJson(Map<String, dynamic> json) {
    _name = json['name'];
    _point = json['point'];
  }
}

/// id : 0
/// name : ""
class EvaluationCriteriaTagModel {
  int? _id;
  String? _name;

  int? get id => _id;
  String? get name => _name;

  EvaluationCriteriaTagModel({int? id, String? name}) {
    _id = id;
    _name = name;
  }

  EvaluationCriteriaTagModel.fromJson(dynamic json) {
    _id = json["id"];
    _name = json["name"];
  }

  Map<String, dynamic> toJson() {
    var map = <String, dynamic>{};
    map["id"] = _id;
    map["name"] = _name;
    return map;
  }
}

class EvaluationTemplateErrModel {
  List<EvaluationCriteriaErrModel>? errEvaluationCriteria;

  EvaluationTemplateErrModel({
    this.errEvaluationCriteria,
  });

  EvaluationTemplateErrModel.fromJsonMap(Map<String, dynamic> jsonData) {
    errEvaluationCriteria = (jsonData["evaluated_criterias"] as List)
        .map((json) => EvaluationCriteriaErrModel.fromJsonMap(json))
        .toList();
  }
}

class EvaluationCriteriaErrModel with ErrorResponseExtractor {
  String? errAnswer;

  EvaluationCriteriaErrModel({
    this.errAnswer,
  });

  EvaluationCriteriaErrModel.fromJsonMap(Map<String, dynamic> jsonData) {
    errAnswer = extractErrorString(jsonData, "answer");
  }
}
