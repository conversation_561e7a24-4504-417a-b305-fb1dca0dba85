import 'package:tutorO/models/class/class.dart';
import '../../utils/mixin/error_response_extractor.dart';

enum StudentStatus {
  active,
  stopped,
  save,
  unEnroll,
}

class Student {
  int? studentId;
  String? avatar;
  String? gender;
  DateRes? birthday;
  String? fullName;
  StudentStatus? status;

  Student(
      {this.studentId,
      this.avatar,
      this.gender,
      this.birthday,
      this.fullName,
      this.status});

  Student.fromJsonMap(Map<String, dynamic> map) {
    studentId = map["student_id"];
    avatar = map["avatar"];
    gender = map["gender"];
    birthday = DateRes.fromJson(map["birthday"]);
    fullName = map["full_name"];
    status = convertStudentStatus(map["status"]);
  }

  StudentStatus convertStudentStatus(String value) {
    switch (value.toUpperCase()) {
      case "UNENROLL":
        {
          return StudentStatus.unEnroll;
        }
      case "ACTIVE":
        {
          return StudentStatus.active;
        }
      case "STOPPED":
        {
          return StudentStatus.stopped;
        }
      case "SAVE":
        {
          return StudentStatus.save;
        }
      default:
        {
          return StudentStatus.active;
        }
    }
  }

  String getGender() {
    if (gender != null) {
      return gender!.toLowerCase() == "male" ? "Nam" : "Nữ";
    }
    return "";
  }

  String getDob() {
    if (birthday != null) {
      return birthday.toString();
    }
    return "";
  }
}

class StudentError with ErrorResponseExtractor {
  String? studentIdErr;
  String? studentCodeErr;
  String? avatarErr;
  String? genderErr;
  String? birthdayErr;
  String? fullNameErr;

  StudentError(
      {this.studentIdErr,
      this.studentCodeErr,
      this.avatarErr,
      this.genderErr,
      this.birthdayErr,
      this.fullNameErr});

  StudentError.fromJsonMap(Map<String, dynamic> map) {
    studentIdErr = extractErrorString(map, "student_id");
    studentCodeErr = extractErrorString(map, "student_code");
    avatarErr = extractErrorString(map, "avatar");
    genderErr = extractErrorString(map, "gender");
    birthdayErr = extractErrorString(map, "birthday");
    fullNameErr = extractErrorString(map, "full_name");
  }
}
