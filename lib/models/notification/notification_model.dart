/// id : 3
/// manage_notifications_id : null
/// title : "Chúc mừng bạn đã là thành viên của TUTORO"
/// content : "<p>Chúc mừng bạn đã là thành viên của TUTORO.VN.</p> <p>H<PERSON>y hoàn thiện thông tin để tham gia giảng dạy. </p> <p>Chúc bạn một ngày tốt lành.</p>"
/// icon_url : "https://tutoro.vn/images/tutoro-landing/logo.png"
/// image_url : " ttps://sgp1.digitaloceanspaces.com/teky-prod/images/teachers/2021_07_07_teacher_3485.png"
/// short_description : "Chúc mừng bạn đã là thành viên của TUTORO.VN"
/// params : "{\"tutoro_app\": {\"scr_code\": \"profile-info\", \"paramater\": {}}, \"tutoro_web\": {\"scr_code\": \"\", \"paramater\": {}}}"
/// is_read : 0
/// user_id_created : 10
/// sender_fullname : "Nguyễn Hoàng Hoa Hân"
/// created_at : "2021-05-24"
/// user : 7749

class NotificationModel {
  late int _id;
  int? _manageNotificationsId;
  late String _title;
  late String _content;
  late String _iconUrl;
  String? _imageUrl;
  late String _shortDescription;
  NotificationParamModel? _params;
  int? _isRead;
  int? _userIdCreated;
  String? _senderFullName;
  late String _createdAt;
  int? _user;

  int get id => _id;
  int? get manageNotificationsId => _manageNotificationsId;
  String get title => _title;
  String get content => _content;
  String get iconUrl => _iconUrl;
  String? get imageUrl => _imageUrl;
  String get shortDescription => _shortDescription;
  NotificationParamModel? get params => _params;
  bool get isRead => _isRead == 1;
  int? get userIdCreated => _userIdCreated;
  String? get senderFullName => _senderFullName;
  String get createdAt => _createdAt;
  int? get user => _user;

  NotificationModel(
      {required int id,
      int? manageNotificationsId,
      required String title,
      required String content,
      required String iconUrl,
      String? imageUrl,
      required String shortDescription,
      NotificationParamModel? params,
      int? isRead,
      int? userIdCreated,
      String? senderFullName,
      required String createdAt,
      int? user}) {
    _id = id;
    _manageNotificationsId = manageNotificationsId;
    _title = title;
    _content = content;
    _iconUrl = iconUrl;
    _imageUrl = imageUrl;
    _shortDescription = shortDescription;
    _params = params;
    _isRead = isRead;
    _userIdCreated = userIdCreated;
    _senderFullName = senderFullName;
    _createdAt = createdAt;
    _user = user;
  }

  void markAsRead({required bool isRead}) {
    _isRead = isRead ? 1 : 0;
  }

  NotificationModel.fromJson(dynamic json) {
    print("tutoro_app params: ${json["params"]["tutoro_app"]}");
    _id = json["id"];
    _manageNotificationsId = json["manage_notifications_id"];
    _title = json["title"];
    _content = json["content"];
    _iconUrl = json["icon_url"];
    _imageUrl = json["image_url"];
    _shortDescription = json["short_description"];
    if (json["params"]["tutoro_app"] != null) {
      _params = NotificationParamModel.fromJSON(json["params"]["tutoro_app"]);
    }
    _isRead = json["is_read"];
    _userIdCreated = json["user_id_created"];
    _senderFullName = json["sender_fullname"];
    _createdAt = json["created_at"];
    _user = json["user"];
  }

  Map<String, dynamic> toJson() {
    var map = <String, dynamic>{};
    map["id"] = _id;
    map["manage_notifications_id"] = _manageNotificationsId;
    map["title"] = _title;
    map["content"] = _content;
    map["icon_url"] = _iconUrl;
    map["image_url"] = _imageUrl;
    map["short_description"] = _shortDescription;
    map["params"] = _params;
    map["is_read"] = _isRead;
    map["user_id_created"] = _userIdCreated;
    map["sender_fullname"] = _senderFullName;
    map["created_at"] = _createdAt;
    map["user"] = _user;
    return map;
  }
}

class NotificationParamModel {
  late String _screenCode;
  late Map<String, dynamic> _params;

  String get screenCode => _screenCode;
  Map<String, dynamic> get params => _params;

  NotificationParamModel(
      {required String screenCode, required Map<String, dynamic> params})
      : _screenCode = screenCode,
        _params = params;

  NotificationParamModel.fromJSON(dynamic json) {
    _screenCode = json["scr_code"];
    _params = json["parameter"];
  }

  NotificationParamModel.fromJSONWithScrKey(dynamic json,
      {String screenKey = "src_code"}) {
    _screenCode = json[screenKey];
    _params = json["parameter"];
  }
}
