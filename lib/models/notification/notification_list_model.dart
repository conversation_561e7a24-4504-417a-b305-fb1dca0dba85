import 'package:tutorO/models/notification/notification_model.dart';

/// next : null
/// previous : null
/// results : [{"id":3,"manage_notifications_id":null,"title":"<PERSON>úc mừng bạn đã là thành viên của TUTORO","content":"<p>Chú<PERSON> mừng bạn đã là thành viên của TUTORO.VN.</p> <p><PERSON><PERSON><PERSON> hoàn thiện thông tin để tham gia giảng dạy. </p> <p><PERSON><PERSON><PERSON> bạn một ngày tốt lành.</p>","icon_url":"https://tutoro.vn/images/tutoro-landing/logo.png","image_url":" ttps://sgp1.digitaloceanspaces.com/teky-prod/images/teachers/2021_07_07_teacher_3485.png","short_description":"<PERSON><PERSON><PERSON> mừng bạn đã là thành viên của TUTORO.VN","params":"{\"tutoro_app\": {\"scr_code\": \"profile-info\", \"paramater\": {}}, \"tutoro_web\": {\"scr_code\": \"\", \"paramater\": {}}}","is_read":0,"user_id_created":10,"sender_fullname":"Nguyễn Hoàng Hoa Hân","created_at":"2021-05-24","user":7749},{"id":4,"manage_notifications_id":null,"title":"Cuộc hồi hương 2.000 km","content":"","icon_url":"https://tutoro.vn/images/tutoro-landing/logo.png","image_url":" ttps://sgp1.digitaloceanspaces.com/teky-prod/images/teachers/2021_07_07_teacher_3485.png","short_description":"Không biết đường, cả nhà Giàng Đỗ Chai cứ chạy xe máy theo biển chỉ dẫn, thẳng đường cái to to từ Lâm Đồng về Lào Cai tránh dịch.","params":"{\"tutoro_app\": {\"scr_code\": \"noty\", \"paramater\": {\"id\": 40}}, \"web\": {\"url\": \"https://vnexpress.net/cuoc-hoi-huong-2-000-km-4335266.html\",\"tutoro_web\": {\"scr_code\": \"\", \"paramater\": {}}}","is_read":0,"user_id_created":10,"sender_fullname":"Nguyễn Hoàng Hoa","created_at":"2021-05-24","user":7749}]

class NotificationListModel {
  Map<String, dynamic>? _next;
  Map<String, dynamic>? _previous;
  late List<NotificationModel> _results;

  Map<String, dynamic>? get next => _next;
  Map<String, dynamic>? get previous => _previous;
  List<NotificationModel> get results => _results;

  NotificationListModel(
      {Map<String, dynamic>? next,
      Map<String, dynamic>? previous,
      required List<NotificationModel> results}) {
    _next = next;
    _previous = previous;
    _results = results;
  }

  NotificationListModel.fromJson(dynamic json) {
    _next = json["next"];
    _previous = json["previous"];
    if (json["results"] != null) {
      _results = [];
      json["results"].forEach((v) {
        _results.add(NotificationModel.fromJson(v));
      });
    } else {
      _results = [];
    }
  }

  Map<String, dynamic> toJson() {
    var map = <String, dynamic>{};
    map["next"] = _next;
    map["previous"] = _previous;
    map["results"] = _results.map((v) => v.toJson()).toList();
    return map;
  }
}
