import 'package:tutorO/utils/mixin/error_response_extractor.dart';

class ErrorCommon with ErrorResponseExtractor {
  String _commonError = 'Lỗi';

  String get commonError => _commonError;

  set commonError(String? value) {
    if (value != null && value.isNotEmpty) {
      _commonError = value;
    }
  }
}

class UnspecifiedError {
  late Map<String, dynamic> errInfo;

  UnspecifiedError({required this.errInfo});

  UnspecifiedError.fromJsonMap({required this.errInfo});
}
