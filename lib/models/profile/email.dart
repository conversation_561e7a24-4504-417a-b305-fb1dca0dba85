class Email {
  String email;
  int? verificationStatus;

  Email({required this.email, this.verificationStatus = 0});

  Email.fromJsonMap(Map<String, dynamic> map)
      : email = map["email"],
        verificationStatus = map["verification_status"];

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['email'] = email;
    data['verification_status'] = verificationStatus;
    return data;
  }

  static Email empty() {
    return Email(email: '', verificationStatus: 0);
  }
}

class EmailErrorModel {
  String? emailErr;

  EmailErrorModel({
    this.emailErr,
  });

  EmailErrorModel.fromJsonMap(Map<String, dynamic> jsonData) {
    final arrEmailErrs = List<String>.from(jsonData["email"]);
    emailErr = arrEmailErrs.isNotEmpty ? arrEmailErrs.first : null;
  }
}
