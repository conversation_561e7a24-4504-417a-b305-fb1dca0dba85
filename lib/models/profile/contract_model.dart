/// id : 1
/// odoo_contract_id : 1
/// contract_name : "Hop dong lao dong"
/// signing_date : "2021-01-06"
/// contract_duration : "1 nam"
/// verify_status : true
/// updated_at : "2021-05-08T15:11:38.236929Z"
/// user : 9

class ContractModel {
  final int? _id;
  final int? _odooContractId;
  final String _contractName;
  final String _contractType;
  final String _signingDate;
  final String _contractDuration;
  final bool _verifyStatus;
  final String? _updatedAt;
  final int? _user;

  int? get id => _id;
  int? get odooContractId => _odooContractId;
  String get contractName => _contractName;
  String get contractType => _contractType;
  String get signingDate => _signingDate;
  String get contractDuration => _contractDuration;
  bool get verifyStatus => _verifyStatus;
  String? get updatedAt => _updatedAt;
  int? get user => _user;

  ContractModel(
      {int? id,
      int? odooContractId,
      required String contractName,
      required String contractType,
      required String signingDate,
      required String contractDuration,
      required bool verifyStatus,
      String? updatedAt,
      int? user})
      : _id = id,
        _odooContractId = odooContractId,
        _contractName = contractName,
        _contractType = contractType,
        _signingDate = signingDate,
        _contractDuration = contractDuration,
        _verifyStatus = verifyStatus,
        _updatedAt = updatedAt,
        _user = user;

  ContractModel.fromJson(dynamic json)
      : _id = json["id"],
        _odooContractId = json["odoo_contract_id"],
        _contractName = json["contract_name"],
        _contractType = json["contract_type"] ?? "Chính thức",
        _signingDate = json["signing_date"],
        _contractDuration = json["contract_duration"],
        _verifyStatus = json["verify_status"],
        _updatedAt = json["updated_at"],
        _user = json["user"];

  Map<String, dynamic> toJson() {
    var map = <String, dynamic>{};
    map["id"] = _id;
    map["odoo_contract_id"] = _odooContractId;
    map["contract_name"] = _contractName;
    map["contract_type"] = _contractType;
    map["signing_date"] = _signingDate;
    map["contract_duration"] = _contractDuration;
    map["verify_status"] = _verifyStatus;
    map["updated_at"] = _updatedAt;
    map["user"] = _user;
    return map;
  }
}
