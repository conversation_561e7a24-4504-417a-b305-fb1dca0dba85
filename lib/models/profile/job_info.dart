class JobInfoModel {
  final int? id;
  final String? typeOfWork;
  final String? subject;
  final String? location;
  final String? department;
  final String? workEmail;
  final bool? workEmailVerifyStatus;
  final String? officialReceptionDate;
  final String? workStatus;
  final String? directManager;
  final String? contractStatus;
  final String? bankInfo;
  final String? taxCode;
  final int? user;

  JobInfoModel(
      {required this.id,
      required this.typeOfWork,
      required this.subject,
      required this.location,
      required this.department,
      required this.workEmail,
      required this.workEmailVerifyStatus,
      required this.officialReceptionDate,
      required this.workStatus,
      required this.directManager,
      required this.contractStatus,
      required this.bankInfo,
      required this.taxCode,
      required this.user});

  JobInfoModel.fromJsonMap(Map<String, dynamic> map)
      : id = map["id"],
        typeOfWork = map["type_of_work"],
        subject = map["subjects"],
        location = map["location"],
        department = map["department"],
        workEmail = map["work_email"],
        workEmailVerifyStatus = map["work_email_verify_status"],
        officialReceptionDate = map["official_reception_date"],
        workStatus = map["work_status"],
        directManager = map["direct_manager"],
        contractStatus = map["contract_status"],
        bankInfo = map["bank_info"],
        taxCode = map["tax_code"],
        user = map["user"];

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data["id"] = id;
    data["type_of_work"] = typeOfWork;
    data["subjects"] = subject;
    data["location"] = location;
    data["department"] = department;
    data["work_email"] = workEmail;
    data["work_email_verify_status"] = workEmailVerifyStatus;
    data["official_reception_date"] = officialReceptionDate;
    data["work_status"] = workStatus;
    data["direct_manager"] = directManager;
    data["contract_status"] = contractStatus;
    data["bank_info"] = bankInfo;
    data["tax_code"] = taxCode;
    data["user"] = user;
    return data;
  }

  static JobInfoModel empty() {
    return JobInfoModel(
        subject: '',
        directManager: '',
        officialReceptionDate: '',
        bankInfo: '',
        contractStatus: "",
        department: '',
        location: '',
        workStatus: '',
        typeOfWork: '',
        user: -1,
        workEmailVerifyStatus: false,
        id: -1,
        taxCode: '',
        workEmail: '');
  }
}

class JobInfoErrorModel {
  String? error;

  JobInfoErrorModel({
    this.error,
  });

  JobInfoErrorModel.fromJsonMap(Map<String, dynamic> jsonData) {
    final arrJobInfoErrs = List<String>.from(jsonData["error"]);
    error = arrJobInfoErrs.isNotEmpty ? arrJobInfoErrs.first : null;
  }
}
