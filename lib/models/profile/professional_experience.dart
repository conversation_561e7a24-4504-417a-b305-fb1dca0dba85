import 'dart:io';

import 'package:dio/dio.dart';

import '../../utils/mixin/error_response_extractor.dart';

class WorkExp {
  int? id;
  int? user;
  String? workspaceName;
  String? jobTitle;
  String? startDate;
  String? endDate;
  String? experienceCertificate;
  bool? verifyStatus;
  String? expCertificateUrl;
  File?  expCertificateFile;

  WorkExp({
    required this.id,
    required this.user,
    required this.workspaceName,
    required this.jobTitle,
    required this.startDate,
    required this.endDate,
    required this.experienceCertificate,
    required this.verifyStatus,
    this.expCertificateUrl,
    this.expCertificateFile,
  });

  WorkExp.fromJsonMap(Map<String, dynamic> map)
      : id = map["id"],
        workspaceName = map["workplace_name"],
        jobTitle = map["job_title"],
        startDate = map["start_date"],
        endDate = map["end_date"],
        experienceCertificate = map["experience_certificate"],
        verifyStatus = map["verify_status"],
        expCertificateUrl = map["experience_certificate"],
        user = map["user"];

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['workplace_name'] = user;
    data['job_title'] = workspaceName;
    data['start_date'] = jobTitle;
    data['end_date'] = startDate;
    data['experience_certificate'] = endDate;
    data['verify_status'] = experienceCertificate;
    data['user'] = verifyStatus;
    return data;
  }

  Map<String, dynamic> toJsonForAddNew() {
    final data = <String, dynamic>{};
    data['workplace_name'] = workspaceName;
    data['job_title'] = jobTitle;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    return data;
  }

  Future<FormData> toFormData(File? file) async {
    var data = <String, dynamic>{
      "workplace_name": workspaceName,
      "job_title": jobTitle,
      "start_date": startDate,
      "end_date": endDate,
    };

    if (file != null ) {
      data["experience_certificate"] = file.path.isNotEmpty
          ? await MultipartFile.fromFile(file.path,
          filename: 'image_certificate_file.png')
          : expCertificateUrl;
    }
    var formData = FormData.fromMap(data);
    return formData;
  }

  static WorkExp empty() {
    return WorkExp(
        verifyStatus: false,
        user: -1,
        id: -1,
        workspaceName: '',
        endDate: '',
        jobTitle: '',
        startDate: '',
        expCertificateUrl: '',
        experienceCertificate: '');
  }
}

class WorkExpError with ErrorResponseExtractor {
  String? idError;
  String? userError;
  String? workspaceNameError;
  String? jobTitleError;
  String? startDateError;
  String? endDateError;
  String? experienceCertificateError;
  String? verifyStatusError;

  WorkExpError({
    this.idError,
    this.userError,
    this.workspaceNameError,
    this.jobTitleError,
    this.startDateError,
    this.endDateError,
    this.experienceCertificateError,
    this.verifyStatusError,
  });

  WorkExpError.fromJsonMap(Map<String, dynamic> jsonData) {
    idError = extractErrorString(jsonData, "id");
    userError = extractErrorString(jsonData, "user");
    workspaceNameError = extractErrorString(jsonData, "workplace_name");
    jobTitleError = extractErrorString(jsonData, "job_title");
    startDateError = extractErrorString(jsonData, "start_date");
    endDateError = extractErrorString(jsonData, "end_date");
    experienceCertificateError =
        extractErrorString(jsonData, "experience_certificate");
    verifyStatusError = extractErrorString(jsonData, "verify_status");
  }

  @override
  String toString() {
    return "idError $idError, "
        "userError: $userError, "
        "workspaceNameError $workspaceNameError, "
        "jobTitleError: $jobTitleError, "
        "startDateError: $startDateError, "
        "endDateError: $endDateError, "
        "verifyStatusError: $verifyStatusError";
  }
}
