import '../../utils/date/date_convert.dart';
import 'address.dart';
import 'email.dart';

enum VerifyTeacherStatus {
  none,
  updated,
  verifying,
  verified,
  failed,
}

class PersonalInfoModel {
  int? userId;
  String? avatarUrl;
  String? fullName;
  String? birthday;
  int? gender;
  String? genderAsText;
  String? phone;
  Email? email;
  Address? address;
  String? numberIdCard;
  String? dateRangeIdCard;
  String? issuedIdCardBy;
  String? hrEmployeeId;
  String? phonePresenter;
  String? homeTown;
  String? facebookAddress;
  String? frontsideIdentityCard;
  String? backsideIdentityCard;
  String? imageSelf;
  VerifyTeacherStatus? verifyStatus;
  int? profileCompletionPercent;
  int? personalInformationCompletionPercent;
  int? workInformationCompletionPercent;
  int? professionalInformationCompletionPercent;
  int? contractInformationCompletionPercent;
  bool? isPasswordSet;
  bool? isTeacher;
  String? recommendationCode;
  PersonalInfoModel? updatedProfile;
  String? profileUpdateVerifyStatus;
  bool? isVerifiedTeacher;

  PersonalInfoModel._(
      {this.avatarUrl,
      this.fullName,
      this.birthday,
      this.gender,
      required this.phone,
      this.email,
      this.address,
      this.numberIdCard,
      this.dateRangeIdCard,
      this.issuedIdCardBy,
      this.hrEmployeeId,
      this.phonePresenter,
      this.homeTown,
      this.facebookAddress,
      this.frontsideIdentityCard,
      this.backsideIdentityCard,
      this.imageSelf,
      this.verifyStatus,
      this.profileCompletionPercent,
      this.personalInformationCompletionPercent,
      this.workInformationCompletionPercent,
      this.professionalInformationCompletionPercent,
      this.contractInformationCompletionPercent,
      this.genderAsText,
      this.isPasswordSet,
      this.isTeacher,
      this.updatedProfile,
      this.recommendationCode,
      this.profileUpdateVerifyStatus,
      this.isVerifiedTeacher});

  PersonalInfoModel.fromJsonMap(Map<String, dynamic> map) {
    userId = map["user"];
    avatarUrl = map["avatar"];
    fullName = map["full_name"];
    birthday = map["birthday"];
    gender = map["gender"];
    genderAsText = map["gender_in_text"];
    phone = map["phone_number"];
    email = map["email"] != null ? Email(email: map["email"]) : Email.empty();
    address = Address.fromJsonMap(map);
    numberIdCard = map["identity_card"];
    dateRangeIdCard = map["identity_date"];
    issuedIdCardBy = map["identity_place"];
    hrEmployeeId = map["hr_employee_id"];
    phonePresenter = map["phonePresenter"];
    //todo confirm db k có trường quê quá;
    homeTown = map["facebook_url"];
    facebookAddress = map["facebook_url"];
    frontsideIdentityCard = map["frontside_identity_card"];
    backsideIdentityCard = map["backside_identity_card"];
    verifyStatus = convertTypeVerifyTeacherStatus(map["verify_status"] ?? 0);
    profileCompletionPercent = map["percent_profile"];
    personalInformationCompletionPercent = map["percent_profile"];
    workInformationCompletionPercent = map["percent_employment"];
    professionalInformationCompletionPercent = map["percent_professional"];
    isPasswordSet = map["is_password_set"];
    isTeacher = map["is_verified_teacher"];
    isVerifiedTeacher = map["is_verified"];
    updatedProfile =
        map["updated_profile"] != null && map["updated_profile"] != ""
            ? PersonalInfoModel.fromJsonMap(map["updated_profile"])
            : null;
    recommendationCode = map["recommendation_code"];
    profileUpdateVerifyStatus = map["profile_update_verify_status"];
    contractInformationCompletionPercent = map["percent_contract"];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['avatar'] = avatarUrl;
    data['full_name'] = fullName;
    data['birthday'] = birthday != null
        ? DateConvert.convertDate(birthday!, "dd-MM-yyyy", "yyyy-MM-dd")
        : "";
    data['gender'] = gender;
    data['phone_number'] = phone;
    data['email'] = email == null ? null : email!.email;
    data['identity_card'] = numberIdCard;
    data['identity_date'] = dateRangeIdCard;
    data['identity_place'] = issuedIdCardBy;
    data['hr_employee_id'] = hrEmployeeId;
    data['phonePresenter'] = phonePresenter;
    data['homeTown'] = homeTown;
    data['facebook_url'] = facebookAddress;
    data['country_code'] = address!.countryCode;
    data['province_code'] = address!.cityCode;
    data['district_code'] = address!.districtCode;
    data['current_address'] = address!.currentAddress;
    data["ward_code"] = address!.wardCode;
    return data;
  }

  Map<String, dynamic> toJsonForEditText() {
    final data = <String, dynamic>{};
    data['full_name'] = fullName;
    data['birthday'] = birthday;
    data['gender'] = gender;
    data['phone_number'] = phone;
    data['email'] = email == null ? null : email!.email;
    data['identity_card'] = numberIdCard;
    data['identity_date'] = dateRangeIdCard;
    data['identity_place'] = issuedIdCardBy;
    data['hr_employee_id'] = hrEmployeeId;
    data['phonePresenter'] = phonePresenter;
    data['homeTown'] = homeTown;
    data['facebook_url'] = facebookAddress;
    data['country_code'] = address!.countryCode;
    data['province_code'] = address!.cityCode;
    data['district_code'] = address!.districtCode;
    data['current_address'] = address!.currentAddress;
    data["ward_code"] = address!.wardCode;
    return data;
  }

  Map<String, dynamic> toJsonForEditMultiText() {
    final data = <String, dynamic>{};
    data['full_name'] = fullName;
    data['identity_card'] = numberIdCard;
    data['identity_place'] = issuedIdCardBy;
    data['facebook_url'] = facebookAddress;
    return data;
  }

  static PersonalInfoModel empty() {
    return PersonalInfoModel._(
        avatarUrl: '',
        fullName: '',
        birthday: '',
        gender: -1,
        phone: "",
        email: Email.empty(),
        address: Address.empty(),
        numberIdCard: "",
        dateRangeIdCard: "",
        issuedIdCardBy: "",
        hrEmployeeId: "",
        phonePresenter: "",
        homeTown: "",
        facebookAddress: "",
        verifyStatus: VerifyTeacherStatus.updated,
        profileCompletionPercent: 0,
        personalInformationCompletionPercent: 0,
        workInformationCompletionPercent: 0,
        professionalInformationCompletionPercent: 0,
        isPasswordSet: false,
        contractInformationCompletionPercent: 0);
  }

  VerifyTeacherStatus convertTypeVerifyTeacherStatus(int value) {
    switch (value) {
      case 0:
        return VerifyTeacherStatus.updated;
      case 1:
        return VerifyTeacherStatus.verifying;
      case 2:
        return VerifyTeacherStatus.verified;
      case 3:
        return VerifyTeacherStatus.failed;
      default:
        return VerifyTeacherStatus.none;
    }
  }

  String getAvatarEdit() {
    return updatedProfile != null &&
            (updatedProfile!.avatarUrl ?? "").isNotEmpty
        ? updatedProfile!.avatarUrl!
        : avatarUrl ?? "";
  }

  String getFullNameEdit() {
    return updatedProfile != null && (updatedProfile!.fullName ?? "").isNotEmpty
        ? updatedProfile!.fullName!
        : fullName ?? "";
  }

  bool isUpdatingFullName() {
    if (updatedProfile == null) return false;
    return fullName != updatedProfile!.fullName &&
        updatedProfile!.fullName != null &&
        updatedProfile!.fullName!.isNotEmpty;
  }

  String getBirthdayEdit() {
    return updatedProfile != null && (updatedProfile!.birthday ?? "").isNotEmpty
        ? updatedProfile!.birthday!
        : birthday ?? "";
  }

  bool isUpdatingBirthday() {
    if (updatedProfile == null) return false;
    return birthday != updatedProfile!.birthday &&
        updatedProfile!.birthday != null &&
        updatedProfile!.birthday!.isNotEmpty;
  }

  int getGenderEdit() {
    return updatedProfile != null && (updatedProfile!.gender ?? -2) != -2
        ? updatedProfile!.gender!
        : gender ?? -1;
  }

  String? convertGender() {
    return gender == -1
        ? "Khác"
        : gender == 0
            ? "Nữ"
            : "Nam";
  }

  bool isUpdatingGender() {
    if (updatedProfile == null) return false;
    return gender != updatedProfile!.gender;
  }

  String getPhoneEdit() {
    return updatedProfile != null && (updatedProfile!.phone ?? "").isNotEmpty
        ? updatedProfile!.phone!
        : phone ?? "";
  }

  Email getEmailEdit() {
    return updatedProfile != null && (updatedProfile!.email!.email).isNotEmpty
        ? updatedProfile!.email!
        : email!;
  }

  Address getAddressEdit() {
    return updatedProfile != null && !updatedProfile!.address!.isEmpty()
        ? updatedProfile!.address!
        : address!;
  }

  bool isUpdatingAddress() {
    if (updatedProfile == null || updatedProfile!.address == null) return false;
    return address!.isDifferentAddress(updatedProfile!.address!);
  }

  bool isUpdatingFb() {
    if (updatedProfile == null) return false;
    return updatedProfile!.facebookAddress != facebookAddress &&
        updatedProfile!.facebookAddress != null &&
        updatedProfile!.facebookAddress!.isNotEmpty;
  }

  String getFbEdit() {
    return updatedProfile != null &&
            (updatedProfile!.facebookAddress ?? "").isNotEmpty
        ? updatedProfile!.facebookAddress!
        : facebookAddress ?? "";
  }

  String getNumberIdCardEdit() {
    return updatedProfile != null &&
            (updatedProfile!.numberIdCard ?? "").isNotEmpty
        ? updatedProfile!.numberIdCard!
        : numberIdCard ?? "";
  }

  bool isUpdatingNumberIdCard() {
    if (updatedProfile == null) return false;
    return updatedProfile!.numberIdCard != numberIdCard &&
        updatedProfile!.numberIdCard != null &&
        updatedProfile!.numberIdCard!.isNotEmpty;
  }

  String getDateRangeIdCardEdit() {
    return updatedProfile != null &&
            (updatedProfile!.dateRangeIdCard ?? "").isNotEmpty
        ? updatedProfile!.dateRangeIdCard!
        : dateRangeIdCard ?? "";
  }

  bool isUpdatingDateRangeIdCard() {
    if (updatedProfile == null) return false;
    return updatedProfile!.dateRangeIdCard != dateRangeIdCard &&
        updatedProfile!.dateRangeIdCard != null &&
        updatedProfile!.dateRangeIdCard!.isNotEmpty;
  }

  String getIssuedIdCardByEdit() {
    return updatedProfile != null &&
            (updatedProfile!.issuedIdCardBy ?? "").isNotEmpty
        ? updatedProfile!.issuedIdCardBy!
        : issuedIdCardBy ?? "";
  }

  bool isUpdatingIssuedIdCardBy() {
    if (updatedProfile == null) return false;
    return updatedProfile!.issuedIdCardBy != issuedIdCardBy &&
        updatedProfile!.issuedIdCardBy != null &&
        updatedProfile!.issuedIdCardBy!.isNotEmpty;
  }

  String getFrontsideIdentityCardEdit() {
    return updatedProfile != null &&
            (updatedProfile!.frontsideIdentityCard ?? "").isNotEmpty
        ? updatedProfile!.frontsideIdentityCard!
        : frontsideIdentityCard ?? "";
  }

  bool isUpdatingFrontsideIdentityCard() {
    if (updatedProfile == null) return false;
    return updatedProfile!.frontsideIdentityCard != frontsideIdentityCard &&
        updatedProfile!.frontsideIdentityCard != null &&
        updatedProfile!.frontsideIdentityCard!.isNotEmpty;
  }

  String getBacksideIdentityCardEdit() {
    return updatedProfile != null &&
            (updatedProfile!.backsideIdentityCard ?? "").isNotEmpty
        ? updatedProfile!.backsideIdentityCard!
        : backsideIdentityCard ?? "";
  }

  bool isUpdatingtBacksideIdentityCard() {
    if (updatedProfile == null) return false;
    return updatedProfile!.backsideIdentityCard != backsideIdentityCard &&
        updatedProfile!.backsideIdentityCard != null &&
        updatedProfile!.backsideIdentityCard!.isNotEmpty;
  }
}

class PersonalInfoModelErrorModel {
  String? avatarUrlError;
  String? fullNameError;
  String? birthdayError;
  String? genderError;
  String? phoneError;
  Email? emailError;
  Address? addressError;
  String? numberIdCardError;
  String? dateRangeIdCardError;
  String? issuedIdCardByError;
  String? namePresenterError;
  String? phonePresenterError;
  String? homeTownError;
  String? facebookAddressError;
  String? frontsideIdentityCard;
  String? backsideIdentityCard;
  String? imageSelf;
  int? profileStatus;
  int? profileCompletionPercent;
  int? personalInformationCompletionPercent;
  int? workInformationCompletionPercent;
  int? professionalInformationCompletionPercent;
  int? contractInformationCompletionPercent;

  PersonalInfoModelErrorModel(
      {this.avatarUrlError,
      this.fullNameError,
      this.birthdayError,
      this.genderError,
      this.phoneError,
      this.emailError,
      this.addressError,
      this.numberIdCardError,
      this.dateRangeIdCardError,
      this.issuedIdCardByError,
      this.namePresenterError,
      this.phonePresenterError,
      this.homeTownError,
      this.facebookAddressError,
      this.frontsideIdentityCard,
      this.backsideIdentityCard,
      this.imageSelf,
      this.profileStatus,
      this.profileCompletionPercent,
      this.personalInformationCompletionPercent,
      this.workInformationCompletionPercent,
      this.professionalInformationCompletionPercent,
      this.contractInformationCompletionPercent});

  PersonalInfoModelErrorModel.fromJsonMap(Map<String, dynamic> map)
      : avatarUrlError = map["avatar_url"],
        fullNameError = map["full_name"],
        birthdayError = map["birthday"],
        genderError = map["gender"],
        phoneError = map["phone"],
        emailError = map["email"],
        //addressError = Address.fromJsonMap(map["address"]),
        numberIdCardError = map["identity_card"],
        dateRangeIdCardError = map["identity_date"],
        issuedIdCardByError = map["identity_place"],
        namePresenterError = map["hr_employee_id"],
        phonePresenterError = map["phonePresenter"],
        //todo confirm db k có trường quê quán
        homeTownError = map["facebook_url"],
        facebookAddressError = map["facebook_url"],
        frontsideIdentityCard = map["frontside_identity_card"],
        backsideIdentityCard = map["backside_identity_card"],
        imageSelf = map["frontside_identity_card"],
        profileStatus = map["verify_status"],
        profileCompletionPercent = map["percent_profile"],
        personalInformationCompletionPercent = map["percent_profile"],
        workInformationCompletionPercent = map["percent_employment"],
        professionalInformationCompletionPercent = map["percent_professional"],
        contractInformationCompletionPercent = map["percent_contract"];
}
