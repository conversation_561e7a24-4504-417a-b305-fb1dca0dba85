import 'dart:io';

class ImageSelf {
  int? id;
  String? imageUrl;
  String? imageType;
  bool? verifyStatus;
  String? updatedAt;
  String? verifyDateAt;
  int? user;
  File? fileImage;

  ImageSelf(
      {this.id,
      this.imageUrl,
      this.imageType,
      this.verifyStatus,
      this.updatedAt,
      this.verifyDateAt,
      this.user});

  ImageSelf.fromJsonMap(Map<String, dynamic> map)
      : id = map["id"] ?? "",
        imageUrl = map["image_url"] ?? "",
        imageType = map["image_type"] ?? "",
        verifyStatus = map["verify_status"] ?? "",
        updatedAt = map["updated_at"] ?? "",
        verifyDateAt = map["verify_date_at"] ?? "",
        user = map["verify_date_at"] ?? 0;

  // ignore: prefer_initializing_formals
  ImageSelf.fromFile(File file) : fileImage = file;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['image_url'] = imageUrl;
    data['image_type'] = imageType;
    data['verify_status'] = verifyStatus;
    data["updated_at"] = updatedAt;
    data["verify_date_at"] = verifyDateAt;
    data["verify_date_at"] = user;
    return data;
  }
}
