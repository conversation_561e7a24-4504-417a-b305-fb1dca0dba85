class Address {
  String countryCode;
  String cityCode;
  String districtCode;
  String wardCode;
  String currentAddress;

  Address(
      {required this.countryCode,
      required this.cityCode,
      required this.districtCode,
      required this.currentAddress,
      required this.wardCode});

  Address.fromJsonMap(Map<String, dynamic> map)
      : countryCode = map["country_code"] ?? "",
        cityCode = map["province_code"] ?? "",
        districtCode = map["district_code"] ?? "",
        wardCode = map["ward_code"] ?? "",
        currentAddress = map["current_address"] ?? "";

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if(countryCode.isNotEmpty){
      data['country_code'] = countryCode;
    }
    if(cityCode.isNotEmpty){
      data['province_code'] = cityCode;
    }
    if(districtCode.isNotEmpty){
      data['district_code'] = districtCode;
    }
    data['current_address'] = currentAddress.isNotEmpty? currentAddress : null;
    if(wardCode.isNotEmpty){
      data["ward_code"] = wardCode;
    }
    return data;
  }

  bool isDifferentAddress(Address address){
    if(address.countryCode != countryCode && countryCode.isNotEmpty) return true;
    if(address.cityCode != cityCode       && cityCode.isNotEmpty) return true;
    if(address.districtCode != districtCode && districtCode.isNotEmpty) return true;
    if(address.currentAddress != currentAddress && currentAddress.isNotEmpty) return true;
    if(address.wardCode != wardCode && wardCode.isNotEmpty) return true;
    return false;
  }

  static Address mock() {
    return Address(
        countryCode: 'Việt Nam',
        cityCode: 'Hà Nội',
        districtCode: 'Hà Đông',
        currentAddress: '10 Trần Phú',
        wardCode: "");
  }

  void setAddress(
      {required String countryCode,
      required String cityCode,
      required String districtCode,
      required String wardCode,
      required String currentAddress}) {
    this.countryCode = countryCode;
    this.cityCode = cityCode;
    this.districtCode = districtCode;
    this.wardCode = wardCode;
    this.currentAddress = currentAddress;
  }

  bool isEmpty() {
    return countryCode.isEmpty &&
        cityCode.isEmpty &&
        districtCode.isEmpty &&
        wardCode.isEmpty &&
        currentAddress.isEmpty;
  }

  static Address empty() {
    return Address(
        currentAddress: '',
        cityCode: '',
        districtCode: '',
        wardCode: '',
        countryCode: '');
  }

  String fullAddress() {
    return '$currentAddress';
  }
}
