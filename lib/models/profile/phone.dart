class Phone {
  String countryCode;
  String flagCode;
  String number;
  int verificationStatus;

  Phone(
      {required this.countryCode,
      required this.flagCode,
      required this.number,
      required this.verificationStatus});

  Phone.fromJsonMap(Map<String, dynamic> map)
      : countryCode = map["country_code"],
        flagCode = map["flag_code"],
        number = map["number"],
        verificationStatus = map["verification_status"];

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['country_code'] = countryCode;
    data['flag_code'] = flagCode;
    data['number'] = number;
    data['verification_status'] = verificationStatus;
    return data;
  }

  static Phone empty() {
    return Phone(
        countryCode: '+84',
        flagCode: 'VN',
        number: '912345678',
        verificationStatus: 1);
  }
}
