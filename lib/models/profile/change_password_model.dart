class ChangePwdErrorModel {
  List<String>? errOldPwd;
  List<String>? errNewPwd;

  ChangePwdErrorModel({this.errOldPwd, this.errNewPwd});

  static ChangePwdErrorModel fromJsonMap(Map<String, dynamic> jsonData) {
    var object = ChangePwdErrorModel();
    final errOldPwd = jsonData["old_password"] as List<dynamic>?;
    object.errOldPwd = errOldPwd?.map((e) => e.toString()).toList();
    final errNewPwd = jsonData["new_password"] as List<dynamic>?;
    object.errNewPwd = errNewPwd?.map((e) => e.toString()).toList();

    return object;
  }
}
