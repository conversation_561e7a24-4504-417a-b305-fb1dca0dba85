import 'package:tutorO/models/class/class.dart';

import '../../utils/mixin/error_response_extractor.dart';

class ReportStudent {
  String? classCode;
  String? attendanceStatus;
  int? timeInClass;
  int? openMicTime;
  int? studyRealTime;
  int? scheduleTime;
  DateRes? dateTime;

  ReportStudent(
      {this.classCode,
      this.attendanceStatus,
      this.timeInClass,
      this.openMicTime,
      this.studyRealTime,
      this.scheduleTime,
      this.dateTime});

  ReportStudent.fromJson(dynamic json) {
    classCode = json["class_code"];
    attendanceStatus = json["attendance_status"];
    timeInClass = json["time_in_class"];
    openMicTime = json["open_mic_time"];
    studyRealTime = json["study_real_time"];
    scheduleTime = json["schedule_time"];
    dateTime = DateRes.fromJson(json["datetime"]);
  }

  ReportStudent.empty() {
    classCode = '';
    attendanceStatus = '';
    timeInClass = 0;
    openMicTime = 0;
    studyRealTime = 0;
    scheduleTime = 0;
    dateTime = null;
  }
}

class ReportStudentError with ErrorResponseExtractor {
  String? classCodeError;
  String? attendanceStatusError;
  String? timeInClassError;
  String? openMicTimeError;
  String? studyRealTimeError;
  String? scheduleTimeError;
  String? dateTimeError;

  ReportStudentError(
      {this.classCodeError,
      this.attendanceStatusError,
      this.timeInClassError,
      this.openMicTimeError,
      this.studyRealTimeError,
      this.scheduleTimeError,
      this.dateTimeError});

  ReportStudentError.fromJsonMap(Map<String, dynamic> jsonData) {
    classCodeError = extractErrorString(jsonData, "class_code");
    attendanceStatusError = extractErrorString(jsonData, "attendance_status");
    timeInClassError = extractErrorString(jsonData, "time_in_class");
    openMicTimeError = extractErrorString(jsonData, "open_mic_time");
    studyRealTimeError = extractErrorString(jsonData, "study_real_time");
    scheduleTimeError = extractErrorString(jsonData, "schedule_time");
    dateTimeError = extractErrorString(jsonData, "datetime");
  }
}
