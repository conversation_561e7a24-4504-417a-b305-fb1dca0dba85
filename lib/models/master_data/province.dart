class Province {
  int id;
  String countryCode;
  String code;
  String name;

  Province(
      {required this.id,
      required this.name,
      required this.countryCode,
      required this.code});

  Province.fromJsonMap(Map<String, dynamic> map)
      : id = map["id"],
        countryCode = map["country_code"],
        code = map["code"],
        name = map["name"];

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['country_code'] = countryCode;
    data['code'] = code;
    data['name'] = name;
    return data;
  }

  static Province empty() {
    return Province(id: 0, name: "", countryCode: "", code: "");
  }
}

class ProvinceError {
  String? error;

  ProvinceError({
    this.error,
  });

  ProvinceError.fromJsonMap(Map<String, dynamic> jsonData) {
    final listError = List<String>.from(jsonData["error"]);
    error = listError.isNotEmpty ? listError.first : null;
  }
}
