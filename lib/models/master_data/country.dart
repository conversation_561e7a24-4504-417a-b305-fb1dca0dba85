class Country {
  int id;
  String code;
  String name;

  Country({required this.id, required this.name, required this.code});

  Country.fromJsonMap(Map<String, dynamic> map)
      : id = map["id"],
        code = map["code"],
        name = map["name"];

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['code'] = code;
    data['name'] = name;
    return data;
  }

  static Country empty() {
    return Country(id: 0, name: "", code: "");
  }
}

class CountryError {
  String? error;

  CountryError({
    this.error,
  });

  CountryError.fromJsonMap(Map<String, dynamic> jsonData) {
    final listError = List<String>.from(jsonData["error"]);
    error = listError.isNotEmpty ? listError.first : null;
  }
}
