class District {
  int id;
  String provinceCode;
  String code;
  String name;

  District(
      {required this.id,
      required this.name,
      required this.provinceCode,
      required this.code});

  District.fromJsonMap(Map<String, dynamic> map)
      : id = map["id"],
        code = map["code"],
        provinceCode = map["province_code"],
        name = map["name"];

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['district'] = id;
    data['district_name'] = name;
    data['code'] = code;
    data['district_code'] = provinceCode;
    return data;
  }

  static District empty() {
    return District(id: 0, name: "", code: "", provinceCode: "");
  }
}

class DistrictError {
  String? error;

  DistrictError({
    this.error,
  });

  DistrictError.fromJsonMap(Map<String, dynamic> jsonData) {
    final listError = List<String>.from(jsonData["error"]);
    error = listError.isNotEmpty ? listError.first : null;
  }
}
