class Ward {
  int id;
  String name;
  String code;
  String districtCode;

  Ward(
      {required this.id,
      required this.name,
      required this.code,
      required this.districtCode});

  Ward.fromJsonMap(Map<String, dynamic> map)
      : id = map["id"],
        code = map["code"],
        districtCode = map["district_code"],
        name = map["name"];

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['ward'] = id;
    data['code'] = code;
    data['district_code'] = districtCode;
    data['name'] = name;
    return data;
  }

  static Ward empty() {
    return Ward(id: 0, name: "", districtCode: "", code: "");
  }
}

class WardError {
  String? error;

  WardError({
    this.error,
  });

  WardError.fromJsonMap(Map<String, dynamic> jsonData) {
    final listError = List<String>.from(jsonData["error"]);
    error = listError.isNotEmpty ? listError.first : null;
  }
}
