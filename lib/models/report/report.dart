import '../../utils/mixin/error_response_extractor.dart';

class Report {
  int? sessionId;
  int? attendanceNumbers;
  int? onTimeStudents;
  int? homeworkNumbers;
  int? taskNumbers;
  int? studentFeedbackNumbers;
  int? teacherFeedbackNumbers;
  int? productNumbers;
  int? imageVideoNumbers;

  Report(
      {this.sessionId,
      this.attendanceNumbers,
      this.onTimeStudents,
      this.homeworkNumbers,
      this.taskNumbers,
      this.studentFeedbackNumbers,
      this.teacherFeedbackNumbers,
      this.productNumbers,
      this.imageVideoNumbers});

  Report.fromJson(dynamic json) {
    sessionId = json["session_id"];
    attendanceNumbers = json["attendance_numbers"];
    onTimeStudents = json["on_time_students"];
    homeworkNumbers = json["homework_numbers"];
    taskNumbers = json["task_numbers"];
    studentFeedbackNumbers = json["student_feedback_numbers"];
    teacherFeedbackNumbers = json["teacher_feedback_numbers"];
    productNumbers = json["product_numbers"];
    imageVideoNumbers = json["image_video_numbers"];
  }

  Report.empty() {
    sessionId = 0;
    attendanceNumbers = 0;
    onTimeStudents = 0;
    homeworkNumbers = 0;
    taskNumbers = 0;
    studentFeedbackNumbers = 0;
    teacherFeedbackNumbers = 0;
    productNumbers = 0;
    imageVideoNumbers = 0;
  }
}

class ReportError with ErrorResponseExtractor {
  String? sessionIdError;
  String? attendanceNumbersError;
  String? onTimeStudentsError;
  String? homeworkNumbersError;
  String? taskNumbersError;
  String? studentFeedbackNumbersError;
  String? teacherFeedbackNumbersError;
  String? productNumbersError;
  String? imageVideoNumbersError;

  ReportError(
      {this.sessionIdError,
      this.attendanceNumbersError,
      this.onTimeStudentsError,
      this.homeworkNumbersError,
      this.taskNumbersError,
      this.studentFeedbackNumbersError,
      this.teacherFeedbackNumbersError,
      this.productNumbersError,
      this.imageVideoNumbersError});

  ReportError.fromJsonMap(Map<String, dynamic> jsonData) {
    sessionIdError = extractErrorString(jsonData, "session_id");
    attendanceNumbersError = extractErrorString(jsonData, "attendance_numbers");
    onTimeStudentsError = extractErrorString(jsonData, "on_time_students");
    homeworkNumbersError = extractErrorString(jsonData, "homework_numbers");
    taskNumbersError = extractErrorString(jsonData, "task_numbers");
    studentFeedbackNumbersError =
        extractErrorString(jsonData, "student_feedback_numbers");
    teacherFeedbackNumbersError =
        extractErrorString(jsonData, "teacher_feedback_numbers");
    productNumbersError = extractErrorString(jsonData, "product_numbers");
    imageVideoNumbersError =
        extractErrorString(jsonData, "image_video_numbers");
  }
}
