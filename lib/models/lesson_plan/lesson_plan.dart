import '../../utils/mixin/error_response_extractor.dart';

class LessonPlan {
  int? id;
  String? name;
  String? fileType;
  String? fileSize;
  String? fileUrl;
  String? fileCategory;
  String? thumbnailUrl;

  LessonPlan(
      {this.id,
      this.name,
      this.fileType,
      this.fileSize,
      this.fileUrl,
      this.fileCategory,
      this.thumbnailUrl});

  LessonPlan.fromJson(dynamic json) {
    id = json["id"];
    name = json["name"];
    fileType = json["file_type"];
    fileSize = json["file_size"];
    fileUrl = json["file_url"];
    fileCategory = json["file_category"];
    thumbnailUrl = json["thumbnail_url"];
  }

  LessonPlan.empty() {
    id = 0;
    name = "";
    fileType = "";
    fileSize = "";
    fileUrl = "";
    fileCategory = "";
    thumbnailUrl = "";
  }
}

class LessonPlanError with ErrorResponseExtractor {
  String? idError;
  String? nameError;
  String? fileTypeError;
  String? fileSizeError;
  String? fileUrlError;
  String? fileCategoryError;
  String? thumbnailUrlError;

  LessonPlanError(
      {this.idError,
      this.nameError,
      this.fileTypeError,
      this.fileSizeError,
      this.fileUrlError,
      this.fileCategoryError,
      this.thumbnailUrlError});

  LessonPlanError.fromJsonMap(Map<String, dynamic> jsonData) {
    idError = extractErrorString(jsonData, "id");
    nameError = extractErrorString(jsonData, "name");
    fileTypeError = extractErrorString(jsonData, "file_type");
    fileSizeError = extractErrorString(jsonData, "file_size");
    fileUrlError = extractErrorString(jsonData, "file_url");
    fileCategoryError = extractErrorString(jsonData, "file_category");
    thumbnailUrlError = extractErrorString(jsonData, "thumbnail_url");
  }
}
