/// language_code : "en"
/// name : "Grade 1"
/// slug : "Grade 1"
/// description : "Grade 1"

class Grade {
  late int _id;
  late int _odooGradeId;
  String? _languageCode;
  String? _name;
  String? _slug;
  String? _description;

  int get id => _id;
  int get odooGradeId => _odooGradeId;
  String? get languageCode => _languageCode;
  String? get name => _name;
  String? get slug => _slug;
  String? get description => _description;

  Grade(
      {required int id, required int odooGradeId, String? languageCode, String? name, String? slug, String? description}) {
    _id = id;
    _odooGradeId = odooGradeId;
    _languageCode = languageCode;
    _name = name;
    _slug = slug;
    _description = description;
  }

  Grade.fromJson(dynamic json) {
    _id = json["id"];
    _odooGradeId = json["odoo_grade_id"];
    _languageCode = json["language_code"];
    _name = json["name"];
    _slug = json["slug"];
    _description = json["description"];
  }

  Map<String, dynamic> toJson() {
    var map = <String, dynamic>{};
    map["id"] = _id;
    map["language_code"] = _languageCode;
    map["name"] = _name;
    map["slug"] = _slug;
    map["description"] = _description;
    return map;
  }
}
