enum TeachingMethod {
  online,
  offline,
  omo
}

extension ToString on TeachingMethod {
  String get value {
    switch (this) {
      case TeachingMethod.online:
        return "online";
      case TeachingMethod.offline:
        return "offline";
      case TeachingMethod.omo:
        return "omo";
    }
  }
}

class ClassFilterModel {
  String? startDate;
  String? endDate;
  List<String>? subjectIds;
  List<String>? gradeIds;
  TeachingMethod? teachingMethod;
  String? location;
  int? page;
  int? pageSize;

  Map<String, dynamic> toJson() {
    var map = <String, dynamic>{};
    if (startDate != null) map["start_date"] = startDate;
    if (endDate != null) map["end_date"] = endDate;
    if (subjectIds != null) map["subject_ids"] = subjectIds?.join(",");
    if (gradeIds != null) map["grade_ids"] = gradeIds?.join(",");
    if (teachingMethod != null) map["teaching_model"] = teachingMethod?.value.toUpperCase();
    if (location != null) map["location"] = location;
    if (page != null) map["page"] = page;
    if (pageSize != null) map["per_page"] = pageSize;
    return map;
  }
}