import 'package:tutorO/utils/mixin/error_response_extractor.dart';

class AuthenticationModel {
  String token;

  AuthenticationModel({required this.token});

  AuthenticationModel.fromJsonMap(Map<String, dynamic> mapData)
      : token = mapData["token"];

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};

    data["token"] = token;
    return data;
  }

  static AuthenticationModel empty() {
    return AuthenticationModel(token: '');
  }
}

class AuthenticationErrorModel with ErrorResponseExtractor {
  String? passwordErr;
  String? phoneNumberErr;

  AuthenticationErrorModel({this.passwordErr, this.phoneNumberErr});

  AuthenticationErrorModel.fromJsonMap(Map<String, dynamic> jsonData) {
    passwordErr = extractErrorString(jsonData, "password");
    phoneNumberErr = extractErrorString(jsonData, "mobile_number");
  }

  @override
  String toString() {
    return "$phoneNumberErr ";
  }
}
