// "id": 1,
// "odoo_academic_id": 1,
// "title": "Cử nhân sư phạm",
// "updated_at": "2021-05-09T14:45:33.526745Z"

class AcademicTitle {
  int? _id;
  String? _title;
  String? _updatedAt;

  int? get id => _id;
  String? get title => _title;
  String? get updatedAt => _updatedAt;

  AcademicTitle(
      {int? id, String? title, String? updatedAt}) {
    _id = id;
    _title = title;
    _updatedAt = updatedAt;
  }

  AcademicTitle.fromJson(dynamic json) {
    _id = json["odoo_academic_id"];
    _title = json["title"];
    _updatedAt = json["updated_at"];
  }

  Map<String, dynamic> toJson() {
    var map = <String, dynamic>{};
    map["odoo_academic_id"] = _id;
    map["title"] = _title;
    map["updated_at"] = _updatedAt;
    return map;
  }
}
