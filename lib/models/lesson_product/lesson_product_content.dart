import 'dart:io';

import 'package:dio/dio.dart';
import 'package:tutorO/utils/file/file.dart';
import 'package:tutorO/utils/mixin/error_response_extractor.dart';

class ProductContent {
  int? productId;
  int? sessionId;
  int? studentId;
  String? productName;
  String? description;
  String? videoUrl;
  String? productUrl;
  String? slideUrl;
  File? fileSlideUpload;
  int? productStatus;

  ProductContent(
      {this.productId,
      this.studentId,
      this.productName,
      this.description,
      this.videoUrl,
      this.productUrl,
      this.fileSlideUpload,
      this.productStatus,
      this.sessionId,
      this.slideUrl});

  ProductContent.fromJson(dynamic json) {
    productId = json["product_id"];
    studentId = json["student_id"];
    productName = json["product_name"];
    description = json["description"];
    videoUrl = json["video_url"];
    productUrl = json["product_url"];
    slideUrl = json["slide_url"];
  }

  bool hasProductValue() {
    return (productName ?? "").isNotEmpty &&
        (description ?? "").isNotEmpty &&
        (videoUrl ?? "").isNotEmpty &&
        (productUrl ?? "").isNotEmpty;
  }

  Future<Map<String, dynamic>> toMap() async {
    var data = {
      "student_id": studentId,
      "product_name": productName,
      "description": description,
      "video_url": videoUrl,
      "product_url": productUrl,
      "product_status": productStatus ?? 0,
    };
    if (productId != null) {
      data["product_id"] = productId;
      data["old_slide_url"] = slideUrl;
    }
    if (fileSlideUpload != null) {
      data["slide_url"] = await MultipartFile.fromFile(fileSlideUpload!.path,
          filename: '${FileHelper.getFileName(fileSlideUpload!)}');
      data["old_slide_url"] = slideUrl;
    }
    return data;
  }

  ProductContent.empty() {
    studentId = 0;
    productName = "";
    description = "";
    videoUrl = "";
    productUrl = "";
    fileSlideUpload = File("");
    productStatus = 0;
  }
}

class ProductContentError with ErrorResponseExtractor {
  String? idError;
  String? sessionIdError;
  String? studentIdError;
  String? productNameError;
  String? descriptionError;
  String? videoUrlError;
  String? productUrlError;
  String? slideUrlError;
  String? fileSlideUploadError;
  String? productStatusError;

  ProductContentError(
      {this.idError,
      this.sessionIdError,
      this.studentIdError,
      this.productNameError,
      this.descriptionError,
      this.videoUrlError,
      this.productUrlError,
      this.slideUrlError,
      this.fileSlideUploadError,
      this.productStatusError});

  ProductContentError.fromJsonMap(Map<String, dynamic> jsonData) {
    idError = extractErrorString(jsonData, "product_id");
    studentIdError = extractErrorString(jsonData, "student_id");
    productNameError = extractErrorString(jsonData, "product_name");
    descriptionError = extractErrorString(jsonData, "description");
    videoUrlError = extractErrorString(jsonData, "video_url");
    productUrlError = extractErrorString(jsonData, "product_url");
    slideUrlError = extractErrorString(jsonData, "slide_url");
  }
}
