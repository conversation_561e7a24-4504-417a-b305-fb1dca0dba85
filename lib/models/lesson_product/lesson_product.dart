import 'package:tutorO/models/attendance/attendance.dart';

import '../../utils/mixin/error_response_extractor.dart';

class LessonProducts {
  List<Product>? products;

  LessonProducts({this.products});

  LessonProducts.fromJson(dynamic json) {
    products = (json["list_products"] as List)
        .map((e) => Product.fromJson(e))
        .toList();
  }

  static LessonProducts empty() {
    return LessonProducts(products: []);
  }
}

class LessonProductsError with ErrorResponseExtractor {
  String? errTotalProduct;
  String? errTotalPages;
  String? errListProduct;

  LessonProductsError(
      {this.errTotalProduct, this.errTotalPages, this.errListProduct});

  LessonProductsError.fromJsonMap(Map<String, dynamic> jsonData) {
    errTotalProduct = extractErrorString(jsonData, "total_product	");
    errTotalPages = extractErrorString(jsonData, "total_pages	");
    errListProduct = extractErrorString(jsonData, "list_products");
  }
}

class Product {
  int? studentId;
  String? studentName;
  String? avatarUrl;
  bool? hasProject;
  AttendanceStatusEnum? studentStatus;

  Product(
      {this.studentId,
      this.studentName,
      this.avatarUrl,
      this.hasProject,
      this.studentStatus});

  bool haveProductValue() {
    return hasProject ?? false;
  }

  Product.fromJson(dynamic json) {
    studentId = json["student_id"];
    studentName = json["student_name"];
    avatarUrl = json["avatar_url"];
    hasProject = json["has_project"];
    studentStatus = convertAttendanceStatus(json["student_status"]);
  }

  AttendanceStatusEnum convertAttendanceStatus(String? value) {
    if (value == null) return AttendanceStatusEnum.noInfo;
    switch (value.toLowerCase()) {
      case "yes":
        return AttendanceStatusEnum.yes;
      case "no":
        return AttendanceStatusEnum.no;
      case "no_info":
        return AttendanceStatusEnum.noInfo;
    }
    return AttendanceStatusEnum.noInfo;
  }

  bool hasAttendance() {
    return studentStatus == AttendanceStatusEnum.yes;
  }

  Product.empty() {
    studentId = 0;
    studentName = "";
    avatarUrl = "";
    hasProject = false;
    studentStatus = AttendanceStatusEnum.noInfo;
  }

  Map<String, dynamic> toMapDeleteProduct() => {
        "student_id": studentId,
      };
}

class ProductError with ErrorResponseExtractor {
  String? errStudentId;
  String? errStudentName;
  String? errAvatarUrl;
  String? errHasProject;
  String? errStudentStatus;

  ProductError(
      {this.errStudentId,
      this.errStudentName,
      this.errAvatarUrl,
      this.errHasProject,
      this.errStudentStatus});

  ProductError.fromJsonMap(Map<String, dynamic> jsonData) {
    errStudentId = extractErrorString(jsonData, "student_id");
    errStudentName = extractErrorString(jsonData, "avatar_url");
    errAvatarUrl = extractErrorString(jsonData, "student_name");
    errHasProject = extractErrorString(jsonData, "has_project");
    errStudentStatus = extractErrorString(jsonData, "student_status");
  }
}
