import 'package:intl/intl.dart';

import '../../../utils/mixin/error_response_extractor.dart';

enum RequestClassStatus {
  unregister,
  newClass,
  success,
  fail,
  cancel,
  approve,
  reject,
}

class ClassSchedule {
  int? classId;
  String? code;
  String? name;
  String? thumbnailRrl;
  String? teachingModel;
  String? location;
  DateRes? startDate;
  DateRes? endDate;
  List<DateRes>? schedule;
  String? status;
  int? sessionsNumber;
  int? progress;
  int? studentsNumber;
  RequestClassStatus? requestClassStatus;

  /// Constructor
  ClassSchedule({
    this.classId,
    this.code,
    this.name,
    this.thumbnailRrl,
    this.teachingModel,
    this.location,
    this.startDate,
    this.endDate,
    this.schedule,
    this.status,
    this.sessionsNumber,
    this.progress,
    this.studentsNumber,
    this.requestClassStatus,
  });

  ClassSchedule.fromJson(dynamic json) {
    classId = json["class_id"];
    code = json["code"];
    name = json["name"];
    thumbnailRrl = json["thumbnail_url"];
    teachingModel = json["teaching_model"];
    location = json["location"];
    startDate = DateRes.fromJson(json["start_date"]);
    endDate = DateRes.fromJson(json["end_date"]);
    schedule =
        ((json["schedule"] as List).map((e) => DateRes.fromJson(e))).toList();
    status = json["status"];
    sessionsNumber = json["sessions_number"];
    progress = json["finished_sessions"];
    studentsNumber = json["students_number"];
    requestClassStatus = convertStringToEnum(json["registration_state"] ?? "");
  }

  RequestClassStatus? convertStringToEnum(String value) {
    switch (value) {
      case "UNREGISTER":
        return RequestClassStatus.unregister;
      case "NEW":
        return RequestClassStatus.newClass;
      case "SUCCESS":
        return RequestClassStatus.success;
      case "FAIL":
        return RequestClassStatus.fail;
      case "CANCEL":
        return RequestClassStatus.cancel;
      case "APPROVE":
        return RequestClassStatus.approve;
      case "REJECT":
        return RequestClassStatus.reject;
    }
    return null;
  }

  void updateRequestClassStatus(String value) {
    var newStatus = convertStringToEnum(value);
    if (newStatus != null) {
      requestClassStatus = newStatus;
    }
  }

  bool isAvailableTakeClass() {
    return ![RequestClassStatus.newClass].contains(requestClassStatus);
  }

  bool isAvailableCancelClass() {
    return [RequestClassStatus.cancel].contains(requestClassStatus) ||
        requestClassStatus == null;
  }

  bool isCancellableTeachingClass() {
    return requestClassStatus == RequestClassStatus.approve;
  }

  String friendlyWeekDay() {
    var output = "";
    for (var element in schedule!) {
      output += "Thứ ${element.weekday}, ";
    }
    return output.substring(0, output.length - 2);
  }

  bool isTeaching() {
    return status != null
        ? ["APPROVE", "CONFIRM", "DRAFT"].contains(status)
        : false;
  }
}

class ClassScheduleError with ErrorResponseExtractor {
  String? classIdErr;
  String? codeErr;
  String? nameErr;
  String? thumbnailRrlErr;
  String? teachingModelErr;
  String? locationErr;
  String? startDateErr;
  String? endDateErr;
  String? scheduleErr;
  String? statusErr;
  String? sessionsNumberErr;
  String? progressErr;
  String? studentNumberErr;

  /// Constructor
  ClassScheduleError(
      {this.classIdErr,
      this.codeErr,
      this.nameErr,
      this.thumbnailRrlErr,
      this.teachingModelErr,
      this.locationErr,
      this.startDateErr,
      this.scheduleErr,
      this.statusErr,
      this.endDateErr,
      this.sessionsNumberErr,
      this.progressErr,
      this.studentNumberErr});

  ClassScheduleError.fromJsonMap(Map<String, dynamic> jsonData) {
    classIdErr = extractErrorString(jsonData, "class_id");
    codeErr = extractErrorString(jsonData, "code");
    nameErr = extractErrorString(jsonData, "name");
    thumbnailRrlErr = extractErrorString(jsonData, "thumbnail_url");
    teachingModelErr = extractErrorString(jsonData, "teaching_model");
    locationErr = extractErrorString(jsonData, "location");
    startDateErr = extractErrorString(jsonData, "start_date");
    endDateErr = extractErrorString(jsonData, "end_date	");
    scheduleErr = extractErrorString(jsonData, "schedule");
    statusErr = extractErrorString(jsonData, "status");
    sessionsNumberErr = extractErrorString(jsonData, "sessions_number");
    progressErr = extractErrorString(jsonData, "progress");
    studentNumberErr = extractErrorString(jsonData, "student_number");
  }
}

class DateRes {
  int? day;
  int? month;
  int? year;
  int? weekday;
  int? startTime;
  int? endTime;

  DateRes(
      {this.day,
      this.month,
      this.year,
      this.weekday,
      this.startTime,
      this.endTime});

  DateRes.today() {
    var now = DateTime.now();
    day = now.day;
    month = now.month;
    year = now.year;
  }

  DateRes.fromJson(dynamic json) {
    day = json["day"];
    month = json["month"];
    year = json["year"];
    weekday = json["weekday"];
    startTime = json["start_time"];
    endTime = json["end_time"];
  }

  Map<String, dynamic> toJson() {
    var map = <String, dynamic>{};
    map["day"] = day;
    map["month"] = month;
    map["year"] = year;
    return map;
  }

  String toString() {
    return "$day/$month/$year";
  }

  String friendlyDate() {
    // var date = DateTime(year!, month!, day!);
    // final formatted = DateFormat('EEEE', 'vi').format(date);
    // return "$formatted ${toString()}";
    return "${toString()}";
  }

  String friendlyWeekDate() {
    var date = DateTime(year!, month!, day!);
    final formatted = DateFormat('EEE', 'vi').format(date);
    return "$formatted";
  }

  String friendlyStartTime() {
    var hour = (startTime ?? 0) ~/ 60;
    var min = startTime! - hour * 60;
    return "${hour.toString().padLeft(2, '0')}:${min.toString().padLeft(2, '0')}";
  }

  int startHour() {
    var hour = (startTime ?? 0) ~/ 60;
    // var min = startTime! - hour * 60;
    return hour;
  }

  DateTime startDay() {
    var date = DateTime(year!, month!, day!);
    return date;
  }

  String friendlyEndTime() {
    var hour = (endTime ?? 0) ~/ 60;
    var min = endTime! - hour * 60;
    return "${hour.toString().padLeft(2, '0')}:${min.toString().padLeft(2, '0')}";
  }

  String convertWeekDayIntToString() {
    if (weekday == null) return "";
    if (weekday == 6) {
      return "Chủ nhật";
    } else if ((weekday!) < 6) {
      return "Thứ ${weekday! + 2}";
    }
    return "";
  }
}
