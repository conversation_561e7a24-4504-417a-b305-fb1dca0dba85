import 'package:tutorO/models/class/class.dart';
import 'package:tutorO/utils/mixin/error_response_extractor.dart';

class AvailableClasses {
  int? totalClasses;
  int? totalPages;
  List<ClassSchedule>? listClasses;

  AvailableClasses({this.totalClasses, this.totalPages, this.listClasses});

  AvailableClasses.fromJson(dynamic json) {
    totalClasses = json["total_classes"];
    totalPages = json["total_pages"];
    listClasses = (json["list_classes"] as List)
        .map((e) => ClassSchedule.fromJson(e))
        .toList();
  }

  AvailableClasses.empty() {
    totalClasses = 0;
    totalClasses = 0;
    listClasses = [];
  }
}

class AvailableClassesError with ErrorResponseExtractor {
  String? totalClassesErr;
  String? totalPagesErr;
  String? listClassesErr;

  AvailableClassesError(
      {this.totalClassesErr, this.totalPagesErr, this.listClassesErr});

  AvailableClassesError.fromJsonMap(Map<String, dynamic> jsonData) {
    totalClassesErr = extractErrorString(jsonData, "total_classes");
    totalPagesErr = extractErrorString(jsonData, "total_pages");
    listClassesErr = extractErrorString(jsonData, "list_classes");
  }
}

//class AvailableClassesReq{
//  String? startDate;
//  String? endDate;
//  String? subjectIds;
//  String? gradeIds;
//  String? teachingModel;
//  String? location;
//
//  AvailableClassesReq({
//    this.startDate, this.endDate, this.subjectIds,
//      this.gradeIds, this.teachingModel, this.location
//  });
//
//  Map<String, dynamic> toJson() {
//    final data = <String, dynamic>{};
//    data['country_code'] = countryCode;
//    data['province_code'] = cityCode;
//    data['district_code'] = districtCode;
//    data['current_address'] = currentAddress;
//    data["ward_code"] = wardCode;
//    data["ward_code"] = wardCode;
//    return data;
//  }
//
//}
