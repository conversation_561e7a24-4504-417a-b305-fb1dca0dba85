import 'package:tutorO/models/class/class.dart';
import 'package:tutorO/models/gender/gender.dart';

/// student_id : 4765
/// avatar_url : ""
/// full_name : "<PERSON><PERSON><PERSON><PERSON>"
/// birthday : {"day":12,"month":10,"year":2002}
/// evaluation_info : {"evaluation_status":"False","evaluation_time":""}

class EvaluatingStudentModel {
  int? _studentId;
  String? _avatarUrl;
  String? _fullName;
  DateRes? _birthday;
  Gender? _gender;
  EvaluationInfo? _evaluationInfo;

  int? get studentId => _studentId;
  String? get avatarUrl => _avatarUrl;
  String? get fullName => _fullName;
  Gender? get gender => _gender;
  DateRes? get birthday => _birthday;
  EvaluationInfo? get evaluationInfo => _evaluationInfo;

  EvaluatingStudentModel(
      {int? studentId,
      String? avatarUrl,
      String? fullName,
      Gender? gender,
      DateRes? birthday,
      EvaluationInfo? evaluationInfo}) {
    _studentId = studentId;
    _avatarUrl = avatarUrl;
    _fullName = fullName;
    _gender = gender;
    _birthday = birthday;
    _evaluationInfo = evaluationInfo;
  }

  EvaluatingStudentModel.fromJson(dynamic json) {
    _studentId = json["student_id"];
    _avatarUrl = json["avatar_url"];
    _fullName = json["full_name"];
    _birthday =
        json["birthday"] != null ? DateRes.fromJson(json["birthday"]) : null;
    _gender = GenderHelper.fromString(json["gender"] as String?);
    _evaluationInfo = json["evaluation_info"] != null
        ? EvaluationInfo.fromJson(json["evaluation_info"])
        : null;
  }

  Map<String, dynamic> toJson() {
    var map = <String, dynamic>{};
    map["student_id"] = _studentId;
    map["avatar_url"] = _avatarUrl;
    map["full_name"] = _fullName;
    map["gender"] = _gender;
    if (_birthday != null) {
      map["birthday"] = _birthday?.toJson();
    }
    if (_evaluationInfo != null) {
      map["evaluation_info"] = _evaluationInfo?.toJson();
    }
    return map;
  }
}

/// evaluation_status : "False"
/// evaluation_time : ""

class EvaluationInfo {
  bool _evaluationStatus = false;
  String? _evaluationTime;

  bool get evaluationStatus => _evaluationStatus;
  String? get evaluationTime => _evaluationTime;

  EvaluationInfo({required bool evaluationStatus, String? evaluationTime}) {
    _evaluationStatus = evaluationStatus;
    _evaluationTime = evaluationTime;
  }

  EvaluationInfo.fromJson(dynamic json) {
    _evaluationStatus = json["evaluation_status"];
    _evaluationTime = json["evaluation_time"];
  }

  Map<String, dynamic> toJson() {
    var map = <String, dynamic>{};
    map["evaluation_status"] = _evaluationStatus;
    map["evaluation_time"] = _evaluationTime;
    return map;
  }
}
