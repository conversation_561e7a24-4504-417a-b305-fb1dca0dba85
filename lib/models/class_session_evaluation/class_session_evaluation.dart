import 'package:tutorO/models/evaluating_student/evaluating_student_model.dart';

/// session_id : 1268502
/// total_students : 2
/// total_evaluated_students : 2
/// list_evaluations : [{"center_id":1,"center_name":"TEKY - Center 1; 6 <PERSON><PERSON><PERSON><PERSON> - H<PERSON>","list_students":[{"student_id":4763,"avatar_url":"","full_name":"<PERSON><PERSON><PERSON><PERSON>","birthday":{"day":18,"month":12,"year":2012},"evaluation_info":{"evaluation_status":"False","evaluation_time":""}},{"student_id":4765,"avatar_url":"","full_name":"<PERSON><PERSON><PERSON><PERSON>","birthday":{"day":12,"month":10,"year":2002},"evaluation_info":{"evaluation_status":"False","evaluation_time":""}}]}]

class ClassSessionEvaluation {
  int? _sessionId;
  int? _totalStudents;
  int? _totalEvaluatedStudents;
  List<CenterEvaluationModel>? _listEvaluations;

  int? get sessionId => _sessionId;
  int? get totalStudents => _totalStudents;
  int? get totalEvaluatedStudents => _totalEvaluatedStudents;
  List<CenterEvaluationModel>? get listEvaluations => _listEvaluations;

  ClassSessionEvaluation(
      {int? sessionId,
      int? totalStudents,
      int? totalEvaluatedStudents,
      List<CenterEvaluationModel>? listEvaluations}) {
    _sessionId = sessionId;
    _totalStudents = totalStudents;
    _totalEvaluatedStudents = totalEvaluatedStudents;
    _listEvaluations = listEvaluations;
  }

  ClassSessionEvaluation.fromJson(dynamic json) {
    _sessionId = json["session_id"];
    _totalStudents = json["total_students"];
    _totalEvaluatedStudents = json["total_evaluated_students"];
    if (json["list_evaluations"] != null) {
      _listEvaluations = [];
      json["list_evaluations"].forEach((v) {
        _listEvaluations?.add(CenterEvaluationModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    var map = <String, dynamic>{};
    map["session_id"] = _sessionId;
    map["total_students"] = _totalStudents;
    map["total_evaluated_students"] = _totalEvaluatedStudents;
    if (_listEvaluations != null) {
      map["list_evaluations"] =
          _listEvaluations?.map((v) => v.toJson()).toList();
    }
    return map;
  }
}

/// center_id : 1
/// center_name : "TEKY - Center 1; 6 Nguyễn Thị Thập - HN"
/// list_students : [{"student_id":4763,"avatar_url":"","full_name":"Trần Minh Khang","birthday":{"day":18,"month":12,"year":2012},"evaluation_info":{"evaluation_status":"False","evaluation_time":""}},{"student_id":4765,"avatar_url":"","full_name":"Nguyễn Danh Thành","birthday":{"day":12,"month":10,"year":2002},"evaluation_info":{"evaluation_status":"False","evaluation_time":""}}]

class CenterEvaluationModel {
  int? _centerId;
  String? _centerName;
  List<EvaluatingStudentModel>? _listStudents;

  int? get centerId => _centerId;
  String? get centerName => _centerName;
  List<EvaluatingStudentModel>? get listStudents => _listStudents;

  CenterEvaluationModel(
      {int? centerId,
      String? centerName,
      List<EvaluatingStudentModel>? listStudents}) {
    _centerId = centerId;
    _centerName = centerName;
    _listStudents = listStudents;
  }

  CenterEvaluationModel.fromJson(dynamic json) {
    _centerId = json["center_id"];
    _centerName = json["center_name"];
    if (json["list_students"] != null) {
      _listStudents = [];
      json["list_students"].forEach((v) {
        _listStudents?.add(EvaluatingStudentModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    var map = <String, dynamic>{};
    map["center_id"] = _centerId;
    map["center_name"] = _centerName;
    if (_listStudents != null) {
      map["list_students"] = _listStudents?.map((v) => v.toJson()).toList();
    }
    return map;
  }
}
