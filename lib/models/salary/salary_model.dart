import 'dart:math';

class SalaryModel {
  DataAttendanceInfo? data;

  SalaryModel({
    this.data,
  });

  SalaryModel.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null
        ? new DataAttendanceInfo.fromJson(json['data'])
        : null;
  }
}

class DataAttendanceInfo {
  List<ListTeacherAttendanceInfo>? listTeacherAttendanceInfo;
  int? totalPage;
  int? perPage;
  int? page;
  DataAttendanceInfo({
    this.listTeacherAttendanceInfo,
    this.totalPage,
    this.perPage,
    this.page,
  });

  DataAttendanceInfo.fromJson(Map<String, dynamic> json) {
    totalPage = json['total_page'];
    perPage = json['per_page'];
    page = json['page'];
    if (json['list_teacher_attendance_info'] != null) {
      listTeacherAttendanceInfo = <ListTeacherAttendanceInfo>[];
      json['list_teacher_attendance_info'].forEach((v) {
        listTeacherAttendanceInfo!
            .add(new ListTeacherAttendanceInfo.fromJson(v));
      });
    }
  }
}

class ListTeacherAttendanceInfo {
  int? sessionId;
  String? checkInTime;
  String? checkInState;
  DateTime? sessionStartDatetime;
  DateTime? sessionEndDatetime;
  String? createDate;
  String? attendanceState;
  String? batchCode;
  String? courseName;
  String? facultyName;
  String? companyName;

  ListTeacherAttendanceInfo(
      {this.sessionId,
      this.checkInTime,
      this.checkInState,
      this.sessionStartDatetime,
      this.sessionEndDatetime,
      this.createDate,
      this.attendanceState,
      this.batchCode,
      this.courseName,
      this.facultyName,
      this.companyName});

  ListTeacherAttendanceInfo.fromJson(Map<String, dynamic> json) {
    sessionId = json['session_id'];
    checkInTime = json['check_in_time'];
    checkInState = json['check_in_state'];
    sessionStartDatetime = DateTime.parse(json['session_start_datetime']);
    sessionEndDatetime = DateTime.parse(json['session_end_datetime']);
    createDate = json['create_date'];
    attendanceState = json['attendance_state'];
    batchCode = json['batch_code'];
    courseName = json['course_name'];
    facultyName = json['faculty_name'];
    companyName = json['company_name'];
  }
}

enum CheckInState { good, late, noInfo }

CheckInState checkInStateConvert(String? value) {
  switch (value) {
    case "good":
      return CheckInState.good;
    case "late":
      return CheckInState.late;
    default:
      return CheckInState.noInfo;
  }
}

enum CheckInStateFilter { good, late, all }

String? checkInStateConvertString(CheckInStateFilter state) {
  switch (state) {
    case CheckInStateFilter.good:
      return "good";
    case CheckInStateFilter.late:
      return "late";
    case CheckInStateFilter.all:
      return null;
  }
}

enum AttendanceState { good, late24, late48, noInfo, notValid }

enum AttendanceStateFilter { good, late24, late48, noInfo, notValid, all }

String? attendanceStateConvertString(AttendanceStateFilter state) {
  switch (state) {
    case AttendanceStateFilter.good:
      return '1';
    case AttendanceStateFilter.late24:
      return '0';
    case AttendanceStateFilter.late48:
      return "-1";
    case AttendanceStateFilter.noInfo:
      return "";
    case AttendanceStateFilter.notValid:
      return "n";
    case AttendanceStateFilter.all:
      return null;
  }
}

AttendanceState attendanceStateConvert(String? value) {
  switch (value) {
    case "1":
      return AttendanceState.good;
    case "0":
      return AttendanceState.late24;
    case "-1":
      return AttendanceState.late48;
    case "":
      return AttendanceState.noInfo;
    case "n":
      return AttendanceState.notValid;
    default:
      throw RangeError('');
  }
}

String convertSessionTime(
    DateTime sessionStartDatetime, DateTime sessionEndDatetime) {
  return 'Thời gian: ${sessionStartDatetime.day}-${sessionStartDatetime.month}-${sessionStartDatetime.year}   ${sessionStartDatetime.hour < 10 ? '0${sessionStartDatetime.hour}' : sessionStartDatetime.hour}:${sessionStartDatetime.minute < 10 ? '0${sessionStartDatetime.minute}' : sessionStartDatetime.minute}-${sessionEndDatetime.hour < 10 ? '0${sessionEndDatetime.hour}' : sessionEndDatetime.hour}:${sessionEndDatetime.minute < 10 ? '0${sessionEndDatetime.minute}' : sessionEndDatetime.minute}';
}

enum TimeSalary { thisMonth, lastMonth, last2Months, last3Months }
