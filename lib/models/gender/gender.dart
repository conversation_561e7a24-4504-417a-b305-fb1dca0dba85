enum Gender {
  male,
  female,
  other
}

extension ToString on Gender {
  String get stringValue {
    switch (this) {
      case Gender.male:
        return 'male';
      case Gender.female:
        return 'female';
      case Gender.other:
        return 'other';
    }
  }
}

class GenderHelper {
  static Gender fromString(String? value) {
    if (["male", "1"].contains(value?.toLowerCase())) return Gender.male;
    if (["female", "0"].contains(value?.toLowerCase())) return Gender.female;

    return Gender.other;
  }

  static Gender fromInt(int? value) {
    var convertedToString = value.toString();

    return GenderHelper.fromString(convertedToString);
  }

  static Gender fromUnsureType(dynamic value) {
    if (value is int || value is int?) {
      return GenderHelper.fromInt(value);
    }

    if (value is String || value is String?) {
      return GenderHelper.fromString(value);
    }

    return Gender.other;
  }
}