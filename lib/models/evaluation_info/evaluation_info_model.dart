import 'package:tutorO/models/class/class.dart';

/// student : {"student_id":4763,"avatar":"","gender":"MALE","birthday":{"day":18,"month":12,"year":2012},"full_name":"<PERSON><PERSON><PERSON><PERSON>","status":null,"chat_id":0}
/// evaludation_data : {"id":1,"name":"Đáng giá cuối buổi teky","evaluated_criterias":[{"id":1,"name":"Mức độ tập trung trong buổi học","type":"SINGLE_CHOICE","sequence":"1","answer":"Rất tập trung"}],"evaluation_time":1625068702,"state":"completed"}
/// teacher : {"teacher_id":2911,"avatar":null,"gender":"MALE","full_name":"Nguyễn <PERSON>","is_featured":false}

class EvaluationInfoModel {
  Student? _student;
  EvaluationData? _evaluationData;
  Teacher? _teacher;

  Student? get student => _student;
  EvaluationData? get evaluationData => _evaluationData;
  Teacher? get teacher => _teacher;

  EvaluationInfoModel(
      {Student? student, EvaluationData? evaludationData, Teacher? teacher}) {
    _student = student;
    _evaluationData = evaludationData;
    _teacher = teacher;
  }

  EvaluationInfoModel.fromJson(dynamic json) {
    _student =
        json["student"] != null ? Student.fromJson(json["student"]) : null;
    _evaluationData = json["evaludation_data"] != null
        ? EvaluationData.fromJson(json["evaludation_data"])
        : null;
    _teacher =
        json["teacher"] != null ? Teacher.fromJson(json["teacher"]) : null;
  }

  Map<String, dynamic> toJson() {
    var map = <String, dynamic>{};
    if (_student != null) {
      map["student"] = _student?.toJson();
    }
    if (_evaluationData != null) {
      map["evaludation_data"] = _evaluationData?.toJson();
    }
    if (_teacher != null) {
      map["teacher"] = _teacher?.toJson();
    }
    return map;
  }
}

/// teacher_id : 2911
/// avatar : null
/// gender : "MALE"
/// full_name : "Nguyễn Văn Hoàng"
/// is_featured : false

class Teacher {
  int? _teacherId;
  String? _avatar;
  String? _gender;
  String? _fullName;
  bool? _isFeatured;

  int? get teacherId => _teacherId;
  String? get avatar => _avatar;
  String? get gender => _gender;
  String? get fullName => _fullName;
  bool? get isFeatured => _isFeatured;

  Teacher(
      {int? teacherId,
      String? avatar,
      String? gender,
      String? fullName,
      bool? isFeatured}) {
    _teacherId = teacherId;
    _avatar = avatar;
    _gender = gender;
    _fullName = fullName;
    _isFeatured = isFeatured;
  }

  Teacher.fromJson(dynamic json) {
    _teacherId = json["teacher_id"];
    _avatar = json["avatar"];
    _gender = json["gender"];
    _fullName = json["full_name"];
    _isFeatured = json["is_featured"];
  }

  Map<String, dynamic> toJson() {
    var map = <String, dynamic>{};
    map["teacher_id"] = _teacherId;
    map["avatar"] = _avatar;
    map["gender"] = _gender;
    map["full_name"] = _fullName;
    map["is_featured"] = _isFeatured;
    return map;
  }
}

/// id : 1
/// name : "Đáng giá cuối buổi teky"
/// evaluated_criterias : [{"id":1,"name":"Mức độ tập trung trong buổi học","type":"SINGLE_CHOICE","sequence":"1","answer":"Rất tập trung"}]
/// evaluation_time : 1625068702
/// state : "completed"

class EvaluationData {
  int? _id;
  String? _name;
  List<EvaluatedCriterion>? _evaluatedCriteria;
  int? _evaluationTime;
  String? _state;

  int? get id => _id;
  String? get name => _name;
  List<EvaluatedCriterion>? get evaluatedCriteria => _evaluatedCriteria;
  int? get evaluationTime => _evaluationTime;
  String? get state => _state;

  EvaluationData(
      {int? id,
      String? name,
      List<EvaluatedCriterion>? evaluatedCriterias,
      int? evaluationTime,
      String? state}) {
    _id = id;
    _name = name;
    _evaluatedCriteria = evaluatedCriterias;
    _evaluationTime = evaluationTime;
    _state = state;
  }

  EvaluationData.fromJson(dynamic json) {
    _id = json["id"];
    _name = json["name"];
    if (json["evaluated_criterias"] != null) {
      _evaluatedCriteria = [];
      json["evaluated_criterias"].forEach((v) {
        _evaluatedCriteria?.add(EvaluatedCriterion.fromJson(v));
      });
    }
    _evaluationTime = json["evaluation_time"];
    _state = json["state"];
  }

  Map<String, dynamic> toJson() {
    var map = <String, dynamic>{};
    map["id"] = _id;
    map["name"] = _name;
    if (_evaluatedCriteria != null) {
      map["evaluated_criterias"] =
          _evaluatedCriteria?.map((v) => v.toJson()).toList();
    }
    map["evaluation_time"] = _evaluationTime;
    map["state"] = _state;
    return map;
  }
}

/// id : 1
/// name : "Mức độ tập trung trong buổi học"
/// type : "SINGLE_CHOICE"
/// sequence : "1"
/// answer : "Rất tập trung"

class EvaluatedCriterion {
  int? _id;
  String? _name;
  String? _type;
  String? _sequence;
  String? _answer;

  int? get id => _id;
  String? get name => _name;
  String? get type => _type;
  String? get sequence => _sequence;
  String? get answer => _answer;

  EvaluatedCriterion(
      {int? id, String? name, String? type, String? sequence, String? answer}) {
    _id = id;
    _name = name;
    _type = type;
    _sequence = sequence;
    _answer = answer;
  }

  EvaluatedCriterion.fromJson(dynamic json) {
    _id = json["id"];
    _name = json["name"];
    _type = json["type"];
    _sequence = json["sequence"];
    _answer = json["answer"];
  }

  Map<String, dynamic> toJson() {
    var map = <String, dynamic>{};
    map["id"] = _id;
    map["name"] = _name;
    map["type"] = _type;
    map["sequence"] = _sequence;
    map["answer"] = _answer;
    return map;
  }
}

/// student_id : 4763
/// avatar : ""
/// gender : "MALE"
/// birthday : {"day":18,"month":12,"year":2012}
/// full_name : "Trần Minh Khang"
/// status : null
/// chat_id : 0

class Student {
  int? _studentId;
  String? _avatar;
  String? _gender;
  DateRes? _birthday;
  String? _fullName;
  String? _status;
  String? _chatId;

  int? get studentId => _studentId;
  String? get avatar => _avatar;
  String? get gender => _gender;
  DateRes? get birthday => _birthday;
  String? get fullName => _fullName;
  String? get status => _status;
  String? get chatId => _chatId;

  Student(
      {int? studentId,
      String? avatar,
      String? gender,
      DateRes? birthday,
      String? fullName,
      String? status,
      String? chatId}) {
    _studentId = studentId;
    _avatar = avatar;
    _gender = gender;
    _birthday = birthday;
    _fullName = fullName;
    _status = status;
    _chatId = chatId;
  }

  Student.fromJson(dynamic json) {
    _studentId = json["student_id"];
    _avatar = json["avatar"];
    _gender = json["gender"];
    _birthday =
        json["birthday"] != null ? DateRes.fromJson(json["birthday"]) : null;
    _fullName = json["full_name"];
    _status = json["status"];
    _chatId = json["chat_id"];
  }

  Map<String, dynamic> toJson() {
    var map = <String, dynamic>{};
    map["student_id"] = _studentId;
    map["avatar"] = _avatar;
    map["gender"] = _gender;
    if (_birthday != null) {
      map["birthday"] = _birthday?.toJson();
    }
    map["full_name"] = _fullName;
    map["status"] = _status;
    map["chat_id"] = _chatId;
    return map;
  }
}
