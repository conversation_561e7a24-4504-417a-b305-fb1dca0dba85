import 'package:tutorO/utils/mixin/error_response_extractor.dart';

enum CheckInStatus {
  noInfo,
  late,
  missed,
  inTime,
}

class CheckInModel {
  int? checkinId;
  int? checkinDatetime;
  CheckInStatus? state;
  String? note;

  CheckInModel.empty() {
    checkinId = 0;
    checkinDatetime = 0;
    state = CheckInStatus.noInfo;
    note = '';
  }

  CheckInModel({this.checkinId, this.checkinDatetime, this.state, this.note});

  CheckInModel.fromJson(dynamic json) {
    checkinId = json["checkin_id"];
    checkinDatetime = json["checkin_datetime"];
    state = convertCheckInStatus(json["state"]);
    note = json["note"];
  }

  CheckInStatus convertCheckInStatus(String value) {
    switch (value) {
      case "INTIME":
        return CheckInStatus.inTime;
      case "LATE":
        return CheckInStatus.late;
      case "MISSED":
        return CheckInStatus.missed;
      case "NO_INFO":
        return CheckInStatus.noInfo;
      default:
        return CheckInStatus.noInfo;
    }
  }
}

class CheckInModelError with ErrorResponseExtractor {
  String? checkinIdError;
  String? checkinDatetimeError;
  String? stateError;
  String? noteError;

  CheckInModelError(
      {this.checkinIdError,
      this.checkinDatetimeError,
      this.stateError,
      this.noteError});

  CheckInModelError.fromJson(Map<String, dynamic> jsonData) {
    checkinIdError = extractErrorString(jsonData, "checkin_id");
    checkinDatetimeError = extractErrorString(jsonData, "checkin_datetime");
    stateError = extractErrorString(jsonData, "state");
    noteError = extractErrorString(jsonData, "note");
  }
}
