import '../../utils/mixin/error_response_extractor.dart';


class Recommendations {
  int? inviteCount;
  List<RecommendationItem>? recommendations;
  String? urlRef;

  Recommendations(
      {
        this.inviteCount,
        this.recommendations,
        this.urlRef
      });

  Recommendations.fromJson(dynamic json) {
    inviteCount = json["count"];
    recommendations = (json["results"] as List ).map((e) => RecommendationItem.fromJson(e)).toList();
    urlRef = json["recommendation_url"];
  }

  Recommendations.empty() {
    inviteCount = 0;
    recommendations = [];
    urlRef = "";
  }
}

class RecommendationsError with ErrorResponseExtractor {
  String? inviteCountError;
  String? recommendationsError;
  String? urlRefError;


  RecommendationsError(
      {
        this.inviteCountError,
        this.recommendationsError,
        this.urlRefError
      });

  RecommendationsError.fromJsonMap(Map<String, dynamic> jsonData) {
    inviteCountError = extractErrorString(jsonData, "count");
    recommendationsError = extractErrorString(jsonData, "results");
    urlRefError = extractErrorString(jsonData, "recommendation_url");
  }
}

class RecommendationItem {
  int? user;
  String? recommendationCode;
  String? createdAt;
  String? phoneNumber;
  String? email;
  bool? recommendationStatus;

  RecommendationItem(
      {this.user,
      this.recommendationCode,
      this.createdAt,
      this.phoneNumber,
      this.email,
      this.recommendationStatus});

  RecommendationItem.fromJson(dynamic json) {
    user = json["user"];
    recommendationCode = json["recommendation_code"];
    createdAt = json["created_at"];
    phoneNumber = json["phone_number"];
    email = json["email"];
    recommendationStatus = json["recommendation_status"];
  }

  RecommendationItem.empty() {
    user = 0;
    recommendationCode = "";
    createdAt = "";
    phoneNumber = "";
    email = "";
    recommendationStatus = false;
  }
}

class RecommendationItemError with ErrorResponseExtractor {
  String? userError;
  String? recommendationCodeError;
  String? createdAtError;
  String? phoneNumberError;
  String? emailError;
  String? recommendationStatusError;


  RecommendationItemError(
  {
    this.userError,
      this.recommendationCodeError,
      this.createdAtError,
      this.phoneNumberError,
      this.emailError,
      this.recommendationStatusError});

  RecommendationItemError.fromJsonMap(Map<String, dynamic> jsonData) {
    userError = extractErrorString(jsonData, "user");
    recommendationCodeError = extractErrorString(jsonData, "recommendation_code");
    createdAtError = extractErrorString(jsonData, "created_at");
    phoneNumberError = extractErrorString(jsonData, "phone_number");
    emailError = extractErrorString(jsonData, "email");
    recommendationStatusError = extractErrorString(jsonData, "recommendation_status");
  }
}
