class ListSessionMedialPlanResponse {
  Message? message;
  List<SessionMediaPlan>? listSessionMediaPlan = <SessionMediaPlan>[];

  ListSessionMedialPlanResponse({this.message, this.listSessionMediaPlan});

  ListSessionMedialPlanResponse.fromJson(Map<String, dynamic> json) {
    message =
        json['message'] != null ? new Message.fromJson(json['message']) : null;
    if (json['data'] != null) {
      listSessionMediaPlan = <SessionMediaPlan>[];
      json['data'].forEach((v) {
        print(v);
        try {
          var item = new SessionMediaPlan.fromJson(v);
          listSessionMediaPlan!.add(item);
        } catch (e) {
          print(e);
        }
      });
    }
  }
}

class Message {
  String? status;
  int? statusCode;
  String? text;

  Message({this.status, this.statusCode, this.text});

  Message.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    statusCode = json['status_code'];
    text = json['text'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['status_code'] = this.statusCode;
    data['text'] = this.text;
    return data;
  }
}

enum MediaTypeEnum { document, presentation, noInfo }

class SessionMediaPlan {
  String? title;
  String? materialType;
  String? googleDriveLink;
  String? googleDriveLinkFormated;
  int? ziggeoFileId;
  MediaTypeEnum? mediaType;

  SessionMediaPlan(
      {this.title, this.materialType, this.googleDriveLink, this.ziggeoFileId});

  SessionMediaPlan.fromJson(Map<String, dynamic> json) {
    title = json['title'] ?? "";
    materialType = json['material_type'] ?? "";
    googleDriveLink = json['google_drive_link'] ?? "";
    googleDriveLinkFormated =
        googleDriveLink!.isNotEmpty ? formartIframeToUrl(googleDriveLink) : "";
    mediaType = json['google_drive_link'] != ""
        ? filterType(json['google_drive_link'])
        : MediaTypeEnum.noInfo;
  }

  MediaTypeEnum filterType(String? value) {
    if (value == null) return MediaTypeEnum.noInfo;
    switch (value.contains("document")) {
      case true:
        return MediaTypeEnum.document;
      case false:
        return MediaTypeEnum.presentation;
      default:
        return MediaTypeEnum.noInfo;
    }
  }

  String formartIframeToUrl(lessionPlanIframeString) {
    List<String> tags = lessionPlanIframeString
        .replaceAll('<', ' ')
        .replaceAll('>', ' ')
        .split(' ');
    String srcTag = tags.where((s) => s.startsWith('src=')).first;
    String url = srcTag.substring(5, srcTag.length - 1);
    return url;
  }
}
