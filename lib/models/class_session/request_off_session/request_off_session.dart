import 'package:tutorO/models/class_session/class_session.dart';
import 'package:tutorO/models/reason_off/reason_off.dart';
import 'package:tutorO/utils/mixin/error_response_extractor.dart';

class RequestOffSession {
  int? requestId;
  int? requestDatetime;
  ReasonOff? reason;
  String? state;
  String? sNote;
  String? aNote;
  RequestLessonStatus? requestLessonStatus;

  RequestOffSession(
      {this.requestId,
      this.requestDatetime,
      this.state,
      this.sNote,
      this.aNote,
      this.requestLessonStatus,
      this.reason});

  RequestOffSession.fromJson(dynamic json) {
    requestId = json["request_id"];
    requestDatetime = json["request_datetime"];
    state = json["state"];
    sNote = json["s_note"];
    reason = ReasonOff.fromJson(json["reason"]);
    aNote = json["a_note"];
    requestLessonStatus = convertStringToEnumStatus(json["state"]);
  }

  RequestLessonStatus convertStringToEnumStatus(String text) {
    switch (text) {
      case "NO_INFO":
        return RequestLessonStatus.noInfo;
      case "SUCCESS":
        return RequestLessonStatus.success;
      case "NEW":
        return RequestLessonStatus.newReq;
      case "CANCEL":
        return RequestLessonStatus.cancel;
      case "APPROVE":
        return RequestLessonStatus.approve;
      case "REJECT":
        return RequestLessonStatus.reject;
    }
    return RequestLessonStatus.noInfo;
  }

  bool isAvailableCancelSession() {
    return [RequestLessonStatus.noInfo, RequestLessonStatus.cancel]
        .contains(requestLessonStatus);
  }

  RequestOffSession.empty() {
    requestId = 0;
    requestDatetime = 0;
    state = "";
    sNote = "";
    aNote = "";
    requestLessonStatus = RequestLessonStatus.noInfo;
  }
}

class RequestOffSessionError with ErrorResponseExtractor {
  String? requestIdError;
  String? requestDatetimeError;
  String? stateError;
  String? sNoteError;
  String? reasonError;
  String? aNoteError;

  RequestOffSessionError(
      {this.requestIdError,
      this.requestDatetimeError,
      this.stateError,
      this.sNoteError,
      this.reasonError,
      this.aNoteError});

  RequestOffSessionError.fromJson(dynamic jsonData) {
    requestIdError = extractErrorString(jsonData, "request_id");
    requestDatetimeError = extractErrorString(jsonData, "request_datetime");
    stateError = extractErrorString(jsonData, "state");
    sNoteError = extractErrorString(jsonData, "s_note");
    reasonError = extractErrorString(jsonData, "reason");
    aNoteError = extractErrorString(jsonData, "a_note");
  }
}
