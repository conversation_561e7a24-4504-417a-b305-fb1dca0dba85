import 'package:tutorO/models/check_in/check_in.dart';
import 'package:tutorO/models/class/class.dart';
import 'package:tutorO/utils/utils.dart';

import '../../utils/mixin/error_response_extractor.dart';

import 'request_off_session/request_off_session.dart';

enum RequestLessonStatus {
  noInfo,
  unregister,
  newReq,
  success,
  fail,
  cancel,
  approve,
  reject,
}

class ClassSessions {
  int? totalSessions;
  int? totalPages;
  List<ClassSession>? listClassSessions;

  ClassSessions({this.totalSessions, this.totalPages, this.listClassSessions});

  ClassSessions.fromJson(dynamic json) {
    totalSessions = json["total_sessions"];
    totalPages = json["total_pages"];
    listClassSessions = (json["list_sessions"] as List)
        .map((e) => ClassSession.fromJson(e))
        .toList();
  }

  ClassSessions.empty() {
    totalPages = 0;
    totalSessions = 0;
    listClassSessions = [];
  }
}

///
class ClassSessionsError with ErrorResponseExtractor {
  String? errTotalSessions;
  String? errTotalPages;
  String? errListClassSessions;
  String? detail;

  ///
  ClassSessionsError(
      {this.errTotalSessions, this.errTotalPages, this.errListClassSessions,this.detail});

  ClassSessionsError.fromJsonMap(Map<String, dynamic> jsonData) {
    errTotalSessions = extractErrorString(jsonData, "total_sessions");
    errTotalPages = extractErrorString(jsonData, "total_pages");
    errListClassSessions = extractErrorString(jsonData, "list_sessions");
  
  }
}

enum ClassSessionTypeEnum { normalLearn, entranceTest, mockTest, noInfo }

enum ClassSessionStatusEnum { draft, confirm, done, cancel }

class ClassSession {
  int? sessionId;
  String? classCode;
  String? className;
  int? classSessionsNumber;
  int? sessionNumber;
  Attention? attendances = Attention.empty();
  int? studentsNumber;
  String? sessionChapter;
  String? sessionName;
  DateRes? date;
  String? onlineSessionUrl;
  String? nameSession;
  int? countSessions;
  int? sectionIndex;
  int? sessionIndex;
  int? attendStudent;
  CheckInModel? checkInStatus;
  RequestOffSession? requestOffSession;
  ClassSessionTypeEnum? classSessionType;
  ClassSessionStatusEnum? sessionState;

  ClassSession(
      {this.sessionId,
      this.classCode,
      this.className,
      this.classSessionsNumber,
      this.sessionNumber,
      this.attendances,
      this.studentsNumber,
      this.sessionChapter,
      this.sessionName,
      this.date,
      this.onlineSessionUrl,
      this.nameSession,
      this.countSessions,
      this.sectionIndex,
      this.attendStudent,
      this.checkInStatus,
      this.requestOffSession,
      this.classSessionType,
      this.sessionState});

  ClassSession.empty() {
    sessionId = 0;
    classCode = "";
    className = "";
    classSessionsNumber = 0;
    sessionNumber = 0;
    attendances = Attention.empty();
    studentsNumber = 0;
    sessionChapter = "";
    sessionName = "";
    date = null;
    onlineSessionUrl = "";
    nameSession = "";
    countSessions = 0;
    sectionIndex = 0;
    attendStudent = 0;
    checkInStatus = CheckInModel.empty();
    requestOffSession = RequestOffSession.empty();
    sessionState = ClassSessionStatusEnum.draft;
  }

  ClassSession.fromJson(dynamic json) {
    sessionId = json["session_id"];
    classCode = json["class_code"];
    className = json["class_name"];
    classSessionsNumber = json["class_sessions_number"];
    sessionNumber = json["section_index"];
    attendances = json["attendances"] != null
        ? Attention.fromJson(json["attendances"])
        : Attention.empty();
    studentsNumber = json["students_number"];
    sessionChapter = json["session_chapter"];
    sessionName = json["name"];
    date = DateRes.fromJson(json["datetime"]);
    onlineSessionUrl = json["online_session_url"];
    nameSession = json["section_name"];
    countSessions = json["count_sessions"];
    sectionIndex = json["section_index"];
    sessionIndex = json["session_index"];
    attendStudent = json["attend_student"];
    classSessionType = convertValue(json["session_type"]);
    sessionState = convertClassSessionStatusEnum(json["session_state"]);
  }

  ClassSessionStatusEnum convertClassSessionStatusEnum(String? value) {
    if (value == null) return ClassSessionStatusEnum.draft;
    switch (value.trim().toLowerCase()) {
      case "draft":
        return ClassSessionStatusEnum.draft;
      case "confirm":
        return ClassSessionStatusEnum.confirm;
      case "done":
        return ClassSessionStatusEnum.done;
      case "cancel":
        return ClassSessionStatusEnum.cancel;
      default:
        return ClassSessionStatusEnum.draft;
    }
  }

  ClassSessionTypeEnum convertValue(String? value) {
    if (value == null) return ClassSessionTypeEnum.normalLearn;
    switch (value.trim().toLowerCase()) {
      case "learn":
        return ClassSessionTypeEnum.normalLearn;
      case "entrance_test":
        return ClassSessionTypeEnum.entranceTest;
      case "mock_test":
        return ClassSessionTypeEnum.mockTest;
      default:
        return ClassSessionTypeEnum.normalLearn;
    }
  }

  bool isNormalLesson() => classSessionType == ClassSessionTypeEnum.normalLearn;
  bool isInComingLesson(date) {
    return DateTime(date!.year!, date!.month!, date!.day!, date!.startHour())
        .isAfter(DateTime.now());
  }

  bool isTestLesson() => [
        ClassSessionTypeEnum.entranceTest,
        ClassSessionTypeEnum.mockTest
      ].contains(classSessionType);

  bool isCancelSessionSuccess() {
    return [RequestLessonStatus.success, RequestLessonStatus.approve]
        .contains(requestOffSession?.requestLessonStatus);
  }

  bool isCancelSessionFail() {
    return requestOffSession?.requestLessonStatus == RequestLessonStatus.reject;
  }

  bool isCheckinSuccess() {
    return [CheckInStatus.inTime].contains(checkInStatus?.state);
  }

  bool isMissCheckin() {
    return [CheckInStatus.missed].contains(checkInStatus?.state);
  }

  bool isCheckinLate() {
    return [CheckInStatus.late].contains(checkInStatus?.state);
  }

  bool noInfoCheckin() {
    return [CheckInStatus.noInfo].contains(checkInStatus?.state);
  }

  bool haveCheckinStatus() {
    return [CheckInStatus.inTime, CheckInStatus.late, CheckInStatus.missed]
        .contains(checkInStatus?.state);
  }

  bool canCheckIn() {
    if (requestOffSession == null || date == null) return false;
    var currentDate = DateRes.today();
    if (requestOffSession!.requestLessonStatus == RequestLessonStatus.approve ||
        Utils.equalDateRes(currentDate, date!) == false) return false;
    try {
      var dateTime = DateTime.now();
      var timeStart = dateTime.hour * 60 + dateTime.minute;
      if ((date!.startTime ?? 0) - 15 <= timeStart &&
          (date!.endTime ?? 0) >= timeStart) {
        return true;
      }
      // ignore: avoid_catches_without_on_clauses
    } catch (_) {
      return false;
    }
    return false;
  }
}

class ClassSessionError with ErrorResponseExtractor {
  String? sessionIdError;
  String? classCodeError;
  String? classNameError;
  String? classSessionsNumberError;
  String? sessionNumberError;
  String? attendancesError;
  String? studentsNumberError;
  String? sessionChapterError;
  String? sessionNameError;
  String? dateError;
  String? scheduleError;
  String? onlineSessionUrlError;
  String? studentNumberError;

  ClassSessionError({
    this.sessionIdError,
    this.classCodeError,
    this.classNameError,
    this.classSessionsNumberError,
    this.sessionNumberError,
    this.attendancesError,
    this.studentsNumberError,
    this.sessionChapterError,
    this.sessionNameError,
    this.dateError,
    this.scheduleError,
    this.onlineSessionUrlError,
    this.studentNumberError,
  });

  ClassSessionError.fromJson(dynamic json) {
    sessionIdError = extractErrorString(json, "session_id");
    classCodeError = extractErrorString(json, "class_code");
    classNameError = extractErrorString(json, "class_name");
    classSessionsNumberError =
        extractErrorString(json, "class_sessions_number");
    sessionNumberError = extractErrorString(json, "section_index");
    attendancesError = extractErrorString(json, "attendances");
    studentsNumberError = extractErrorString(json, "students_number");
    sessionChapterError = extractErrorString(json, "session_chapter");
    sessionNameError = extractErrorString(json, "name");
    dateError = extractErrorString(json, "datetime");
    scheduleError = extractErrorString(json, "schedule");
    onlineSessionUrlError = extractErrorString(json, "online_session_url");
    studentNumberError = extractErrorString(json, "student_number");
  }
}

class Attention {
  int? totalStudent;
  int? totalActiveStudent;
  int? totalAttendingStudent;
  int? attend;
  bool? status;

  Attention(
      {this.totalStudent,
      this.totalActiveStudent,
      this.totalAttendingStudent,
      this.attend,
      this.status});

  Attention.fromJson(dynamic json) {
    totalStudent = json["total_student"] ?? json["total_students"];
    totalActiveStudent = json["total_active_student"];
    totalAttendingStudent = json["total_attending_student"];
    attend = json["attend"];
    status = json["status"];
  }

  Attention.empty() {
    totalStudent = 0;
    totalActiveStudent = 0;
    totalAttendingStudent = 0;
    attend = 0;
    status = false;
  }
}

class Session {
  int? sectionId;
  String? name;
  int? countSessions;
  int? sectionIndex;

  Session({this.sectionId, this.name, this.countSessions, this.sectionIndex});

  Session.fromJson(dynamic json) {
    sectionId = json["section_id"];
    name = json["name"];
    countSessions = json["count_sessions"];
    sectionIndex = json["section_index"];
  }
}
