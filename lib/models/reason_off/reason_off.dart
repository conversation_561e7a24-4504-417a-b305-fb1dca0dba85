import '../../utils/mixin/error_response_extractor.dart';

class ReasonOff {
  String? code;
  String? text;

  ReasonOff({
    this.code,
    this.text,
  });

  ReasonOff.fromJson(dynamic json) {
    code = json["code"];
    text = json["text"];
  }

  Map<String, dynamic> toJson() {
    var data = <String, dynamic>{};
    data["reason_code"] = code;
    if ((text ?? "").isNotEmpty) {
      data["s_note"] = text;
    }

    return data;
  }

  ReasonOff.empty() {
    code = '';
    text = '';
  }
}

class ReasonOffError with ErrorResponseExtractor {
  String? codeError;
  String? textError;

  ReasonOffError({this.textError, this.codeError});

  ReasonOffError.fromJsonMap(Map<String, dynamic> jsonData) {
    textError = extractErrorString(jsonData, "code");
    codeError = extractErrorString(jsonData, "text");
  }
}
