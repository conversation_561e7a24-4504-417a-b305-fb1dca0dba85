import '../../utils/mixin/error_response_extractor.dart';

class OTPModel {
  String? mobileNumber;

  OTPModel({
    this.mobileNumber,
  });

  OTPModel.fromJsonMap(Map<String, dynamic> jsonData)
      : mobileNumber = jsonData["mobile_number"];

  Map<String, dynamic> toJson() {
    final jsonData = <String, dynamic>{};
    jsonData["mobile_number"] = mobileNumber;
    return jsonData;
  }
}

class OTPErrorModel with ErrorResponseExtractor {
  String? mobileNumberErr;
  String? recaptchaErr;
  String? recommendationCodeErr;

  OTPErrorModel(
      {this.mobileNumberErr, this.recaptchaErr, this.recommendationCodeErr});

  OTPErrorModel.fromJsonMap(Map<String, dynamic> jsonData) {
    mobileNumberErr = extractErrorString(jsonData, "mobile_number");
    recaptchaErr = extractErrorString(jsonData, "recaptcha");
    recommendationCodeErr = extractErrorString(jsonData, "recommendation_code");
  }
}
