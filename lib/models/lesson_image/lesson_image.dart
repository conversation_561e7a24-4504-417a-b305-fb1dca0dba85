import 'package:tutorO/models/class/class.dart';
import '../../utils/mixin/error_response_extractor.dart';

class LessonImage {
  int? id;
  String? fileType;
  String? fileUrl;
  DateRes? createdAt;

  LessonImage({this.id, this.fileType, this.fileUrl, this.createdAt});

  LessonImage.fromJson(dynamic json) {
    id = json["media_id"];
    fileType = json["file_type"];
    fileUrl = json["file_url"];
    //todo update createAt
    //createdAt = DateRes.fromJson(json["created_at"]);
  }
}

class LessonImageError with ErrorResponseExtractor {
  String? idError;
  String? fileTypeError;
  String? fileUrlError;
  String? createdAtError;

  LessonImageError(
      {this.idError,
      this.fileTypeError,
      this.fileUrlError,
      this.createdAtError});

  LessonImageError.fromJsonMap(Map<String, dynamic> jsonData) {
    idError = extractErrorString(jsonData, "media_id");
    fileTypeError = extractErrorString(jsonData, "file_type");
    fileUrlError = extractErrorString(jsonData, "file_url");
    createdAtError = extractErrorString(jsonData, "created_at");
  }
}
