/// id : 3
/// odoo_training_unit_id : 3
/// title : "<PERSON><PERSON><PERSON> - ĐHQGHN"
/// updated_at : "2021-05-09T14:37:05.481824Z"

class TrainingUnit {
  int? _id;
  String? _title;
  String? _updatedAt;

  int? get id => _id;
  String? get title => _title;
  String? get updatedAt => _updatedAt;

  TrainingUnit(
      {int? id, String? title, String? updatedAt}) {
    _id = id;
    _title = title;
    _updatedAt = updatedAt;
  }

  TrainingUnit.fromJson(dynamic json) {
    _id = json["odoo_training_unit_id"];
    _title = json["title"];
    _updatedAt = json["updated_at"];
  }

  Map<String, dynamic> toJson() {
    var map = <String, dynamic>{};
    map["odoo_training_unit_id"] = _id;
    map["title"] = _title;
    map["updated_at"] = _updatedAt;
    return map;
  }
}
