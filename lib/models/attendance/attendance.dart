import 'package:tutorO/models/student/student.dart';
import '../../utils/mixin/error_response_extractor.dart';

enum AttendanceStatusEnum {
  yes,
  no,
  noInfo,
}

class ListAttendance {
  List<Attendance>? listAttendance;
  String? attendanceMessage;

  ListAttendance({this.listAttendance, this.attendanceMessage});

  ListAttendance.fromJson(dynamic json) {
    attendanceMessage = json["attendance_message"];
    listAttendance = (json["list_students"] as List)
        .map((e) => Attendance.fromJson(e))
        .toList();
  }

  ListAttendance.empty() {
    listAttendance = [];
    attendanceMessage = '';
  }
}

class Attendance {
  int? centerId;
  String? centerName;
  List<StudentAttendance>? listStudents;

  Attendance({
    this.centerId,
    this.centerName,
    this.listStudents,
  });

  Attendance.fromJson(dynamic json) {
    centerId = json["center_id"];
    centerName = json["center_name"];
    listStudents = (json["list_students"] as List)
        .map((e) => StudentAttendance.fromJson(e))
        .toList();
  }

  @override
  String toString() {
    return super.toString();
  }
}

class StudentAttendance {
  int? studentId;
  String? avatar;
  AttendanceStatusEnum? attendanceStatus;
  String? fullName;
  StudentStatus? studentStatus;
  String? studentPortfolioLink;
  bool? validPortfolio;

  StudentAttendance(
      {this.studentId,
      this.avatar,
      this.attendanceStatus,
      this.fullName,
      this.studentStatus,
      this.studentPortfolioLink,
      this.validPortfolio,
      });

  StudentAttendance.fromJson(dynamic json) {
    studentId = json["student_id"];
    avatar = json["avatar"];
    attendanceStatus = convertAttendanceStatus(json["attendance_status"]);
    fullName = json["full_name"];
    studentStatus = convertStudentStatus(json["student_status"] ?? "");
  }

  StudentStatus convertStudentStatus(String? value) {
    if (value == null) return StudentStatus.active;
    switch (value.toUpperCase()) {
      case "UNENROLL":
        {
          return StudentStatus.unEnroll;
        }
      case "ACTIVE":
        {
          return StudentStatus.active;
        }
      case "STOPPED":
        {
          return StudentStatus.stopped;
        }
      case "SAVE":
        {
          return StudentStatus.save;
        }
      default:
        {
          return StudentStatus.active;
        }
    }
  }

  void updateStatus(String value) {
    attendanceStatus = convertAttendanceStatus(value);
  }

  AttendanceStatusEnum convertAttendanceStatus(String? value) {
    if (value == null) return AttendanceStatusEnum.noInfo;
    switch (value.toLowerCase()) {
      case "yes":
        return AttendanceStatusEnum.yes;
      case "no":
        return AttendanceStatusEnum.no;
      case "no_info":
        return AttendanceStatusEnum.noInfo;
    }
    return AttendanceStatusEnum.noInfo;
  }

  bool isAttended() {
    return attendanceStatus == AttendanceStatusEnum.yes;
  }
}

class AttendanceError with ErrorResponseExtractor {
  String? errStudentId;
  String? errAvatar;
  String? errAttendanceStatus;
  String? errFullName;

  AttendanceError(
      {this.errStudentId,
      this.errAvatar,
      this.errAttendanceStatus,
      this.errFullName});

  AttendanceError.fromJsonMap(Map<String, dynamic> jsonData) {
    errStudentId = extractErrorString(jsonData, "student_id");
    errAvatar = extractErrorString(jsonData, "avatar");
    errAttendanceStatus = extractErrorString(jsonData, "attendance_status");
    errFullName = extractErrorString(jsonData, "full_name");
  }
}
