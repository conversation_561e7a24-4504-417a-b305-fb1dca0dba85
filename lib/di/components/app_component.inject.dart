import 'package:dio/dio.dart';
import 'package:tutorO/data/network/apis/academic_level/academic_level_api.dart';
import 'package:tutorO/data/network/apis/authen/authen_api.dart';
import 'package:tutorO/data/network/apis/cerificate/certificate_api.dart';
import 'package:tutorO/data/network/apis/class/class_evaluation_api.dart';
import 'package:tutorO/data/network/apis/class/class_schedule_api.dart';
import 'package:tutorO/data/network/apis/contract/contract_api.dart';
import 'package:tutorO/data/network/apis/firebase/firebase_api.dart';
import 'package:tutorO/data/network/apis/global/global_api.dart';
import 'package:tutorO/data/network/apis/master_data/master_data_api.dart';
import 'package:tutorO/data/network/apis/notification/notification_api.dart';
import 'package:tutorO/data/network/apis/profile/profile_api.dart';
import 'package:tutorO/data/network/apis/salary/salary_api.dart';
import 'package:tutorO/data/network/apis/work_experience/work_experience_api.dart';
import 'package:tutorO/data/repository/academic_level_repository.dart';
import 'package:tutorO/data/repository/authen_repository.dart';
import 'package:tutorO/data/repository/certificate_repository.dart';
import 'package:tutorO/data/repository/class_evaluation_repository.dart';
import 'package:tutorO/data/repository/class_repository.dart';
import 'package:tutorO/data/repository/contract_repository.dart';
import 'package:tutorO/data/repository/master_data_repository.dart';
import 'package:tutorO/data/repository/notification_repository.dart';
import 'package:tutorO/data/repository/profile_repository.dart';
import 'package:tutorO/data/repository/salary_repository.dart';
import 'package:tutorO/data/repository/work_experience_repository.dart';
import '../../data/repository/repository.dart';
import '../../data/network/dio_client.dart';
import '../../data/sharedpref/shared_preference_helper.dart';
import '../../main.dart';
import '../module/local_module.dart';
import '../module/network_module.dart';
import '../module/preference_module.dart';
import 'app_component.dart';

class AppComponent$Injector implements AppComponent {
  final LocalModule _localModule;

  FirebaseAPI? _singletonFirebaseAPI;

  final PreferenceModule _preferenceModule;

  SharedPreferenceHelper? _singletonSharedPreferenceHelper;

  Dio? _singletonDio;

  DioClient? _singletonDioClient;

  GlobalAppAPI? _singletonAppVersionAPI;

  Repository? _singletonRepository;

  AuthenApi? _singletonAuthenApi;

  AuthenticationRepository? _singletonAuthenticationRepository;
  ProfileAPI? _singletonProfileAPI;

  ProfileRepository? _singletonProfileRepository;
  MasterDataApi? _singletonMasterDataAPI;

  MasterDataRepository? _singletonMasterDataRepository;
  ContractAPI? _singletonContractAPI;

  ContractRepository? _singletonContractRepository;
  AcademicLevelAPI? _singletonAcademicLevelAPI;
  WorkExpApi? _singletonWorkExpApi;

  AcademicLevelRepository? _singletonAcademicLevelRepository;
  WorkExpRepository? _singletonWorkExpRepository;

  CertificationAPI? _singletonCertificationAPI;
  CertificationRepository? _singletonCertificationRepository;

  ClassAPI? _singletonClassScheduleApi;
  ClassRepository? _singletonClassScheduleRepository;

  ClassEvaluationAPI? _singletonClassEvaluationAPI;
  ClassEvaluationRepository? _singletonClassEvaluationRepository;

  NotificationAPI? _singletonNotificationAPI;
  NotificationRepository? _singletonNotificationRepository;

  SalaryAPI? _singletonSalaryAPI;
  SalaryRepository? _singletonSalaryRepository;
  AppComponent$Injector._(this._localModule, this._preferenceModule);

  @override
  MyStatefulApp get app => _createMyStatefulApp();

  @override
  AcademicLevelRepository getAcademicLevelRepository() =>
      _createAcademicLevelRepository();

  @override
  WorkExpRepository getWorkExpRepository() => _createWorkExpRepository();

  @override
  AuthenticationRepository getAuthenticationRepository() =>
      _createAuthenticationRepository();

  @override
  CertificationRepository getCertificationRepository() =>
      _createCertificationRepository();
  @override
  ClassRepository getClassRepository() => _createClassScheduleRepository();
  @override
  ContractRepository getContractRepository() => _createContractRepository();
  @override
  SalaryRepository getSalaryRepository() => _createSalaryRepository();
  @override
  MasterDataRepository getMasterDataRepository() =>
      _createMasterDataRepository();
  @override
  ProfileRepository getProfileRepository() => _createProfileRepository();
  @override
  Repository getRepository() => _createRepository();

  @override
  ClassEvaluationRepository getClassEvaluationRepository() =>
      _createClassEvaluationRepository();

  @override
  NotificationRepository getNotificationRepository() =>
      _createNotificationRepository();

  AcademicLevelAPI _createAcademicLevelAPI() => _singletonAcademicLevelAPI ??=
      _localModule.provideAcademicLevelAPI(_createDioClient());
  AcademicLevelRepository _createAcademicLevelRepository() =>
      _singletonAcademicLevelRepository ??= _localModule
          .provideAcademicLevelRepository(_createAcademicLevelAPI());

  SalaryAPI _createSalaryAPI() =>
      _singletonSalaryAPI ??= _localModule.provideSalaryAPI(_createDioClient());
  SalaryRepository _createSalaryRepository() => _singletonSalaryRepository ??=
      _localModule.provideSalaryRepository(_createSalaryAPI());

  WorkExpApi _createWorkExpAPI() => _singletonWorkExpApi ??=
      _localModule.provideWorkExpApi(_createDioClient());
  WorkExpRepository _createWorkExpRepository() =>
      _singletonWorkExpRepository ??=
          _localModule.provideWorkExpRepository(_createWorkExpAPI());

  GlobalAppAPI _createAppVersionAPI() => _singletonAppVersionAPI ??=
      _localModule.provideGlobalAppApi(_createDioClient());

  AuthenApi _createAuthenApi() => _singletonAuthenApi ??=
      _localModule.provideAuthenticationAPI(_createDioClient());

  AuthenticationRepository _createAuthenticationRepository() =>
      _singletonAuthenticationRepository ??=
          _localModule.provideAuthenticationRepository(
              _createSharedPreferenceHelper(), _createAuthenApi());

  CertificationAPI _createCertificationAPI() => _singletonCertificationAPI ??=
      _localModule.provideCertificationAPI(_createDioClient());
  CertificationRepository _createCertificationRepository() =>
      _singletonCertificationRepository ??= _localModule
          .provideCertificationRepository(_createCertificationAPI());

  ClassAPI _createClassScheduleApi() => _singletonClassScheduleApi ??=
      _localModule.provideClassScheduleApi(_createDioClient());
  ClassRepository _createClassScheduleRepository() =>
      _singletonClassScheduleRepository ??= _localModule
          .provideClassScheduleRepository(_createClassScheduleApi());

  ContractAPI _createContractAPI() => _singletonContractAPI ??=
      _localModule.provideContractAPI(_createDioClient());
  ContractRepository _createContractRepository() =>
      _singletonContractRepository ??=
          _localModule.provideContractRepository(_createContractAPI());

  ClassEvaluationAPI _createClassEvaluationAPI() =>
      _singletonClassEvaluationAPI ??=
          _localModule.provideClassEvaluationAPI(_createDioClient());
  ClassEvaluationRepository _createClassEvaluationRepository() =>
      _singletonClassEvaluationRepository ??= _localModule
          .provideClassEvaluationRepository(_createClassEvaluationAPI());

  NotificationAPI _createNotificationAPI() => _singletonNotificationAPI ??=
      _localModule.provideNotificationAPI(_createDioClient());
  NotificationRepository _createNotificationRepository() =>
      _singletonNotificationRepository ??=
          _localModule.provideNotificationRepository(_createNotificationAPI());

  Dio _createDio() => _singletonDio ??=
      _localModule.provideDio(_createSharedPreferenceHelper());
  DioClient _createDioClient() =>
      _singletonDioClient ??= _localModule.provideDioClient(_createDio());

  FirebaseAPI _createFirebaseAPI() =>
      _singletonFirebaseAPI ??= _localModule.provideFirebaseAPI();
  MasterDataApi _createMasterApi() => _singletonMasterDataAPI ??=
      _localModule.provideMasterDataAPI(_createDioClient());
  MasterDataRepository _createMasterDataRepository() =>
      _singletonMasterDataRepository ??=
          _localModule.provideMasterDataRepository(_createMasterApi());
  MyStatefulApp _createMyStatefulApp() => MyStatefulApp();

  ProfileAPI _createProfileAPI() => _singletonProfileAPI ??=
      _localModule.provideProfileAPI(_createDioClient());
  ProfileRepository _createProfileRepository() =>
      _singletonProfileRepository ??=
          _localModule.provideProfileRepository(_createProfileAPI());

  Repository _createRepository() {
    if (_singletonRepository == null) {
      _singletonRepository = _localModule.provideRepository(
          _createFirebaseAPI(),
          _createAppVersionAPI(),
          _createSharedPreferenceHelper());
    }

    return _singletonRepository!;
  }

  SharedPreferenceHelper _createSharedPreferenceHelper() =>
      _singletonSharedPreferenceHelper ??=
          _preferenceModule.provideSharedPreferenceHelper();

  static Future<AppComponent> create(NetworkModule _, LocalModule localModule,
      PreferenceModule preferenceModule) async {
    final injector = AppComponent$Injector._(localModule, preferenceModule);

    return injector;
  }
}
