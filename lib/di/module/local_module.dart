import 'dart:async';

import 'package:tutorO/data/network/apis/academic_level/academic_level_api.dart';
import 'package:tutorO/data/network/apis/authen/authen_api.dart';
import 'package:tutorO/data/network/apis/cerificate/certificate_api.dart';
import 'package:tutorO/data/network/apis/class/class_evaluation_api.dart';
import 'package:tutorO/data/network/apis/class/class_schedule_api.dart';
import 'package:tutorO/data/network/apis/contract/contract_api.dart';
import 'package:tutorO/data/network/apis/firebase/firebase_api.dart';
import 'package:tutorO/data/network/apis/global/global_api.dart';
import 'package:tutorO/data/network/apis/master_data/master_data_api.dart';
import 'package:tutorO/data/network/apis/notification/notification_api.dart';
import 'package:tutorO/data/network/apis/profile/profile_api.dart';
import 'package:tutorO/data/network/apis/salary/salary_api.dart';
import 'package:tutorO/data/network/apis/work_experience/work_experience_api.dart';
import 'package:tutorO/data/repository/academic_level_repository.dart';
import 'package:tutorO/data/repository/authen_repository.dart';
import 'package:tutorO/data/repository/certificate_repository.dart';
import 'package:tutorO/data/repository/class_evaluation_repository.dart';
import 'package:tutorO/data/repository/class_repository.dart';
import 'package:tutorO/data/repository/contract_repository.dart';
import 'package:tutorO/data/repository/master_data_repository.dart';
import 'package:tutorO/data/repository/notification_repository.dart';
import 'package:tutorO/data/repository/repository.dart';
import 'package:tutorO/data/repository/salary_repository.dart';
import 'package:tutorO/data/repository/work_experience_repository.dart';
import 'package:tutorO/data/sharedpref/shared_preference_helper.dart';

import '../../data/repository/profile_repository.dart';
import '/data/local/constants/db_constants.dart';
import '/di/module/network_module.dart';
import '/utils/encryption/xxtea.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sembast/sembast.dart';
import 'package:sembast/sembast_io.dart';

class LocalModule extends NetworkModule {
  /// A singleton preference provider.
  ///
  /// Calling it multiple times will return the same instance.

  /// A singleton database provider.
  ///
  /// Calling it multiple times will return the same instance.
  ///
  ///
  ///
  Repository provideRepository(
    FirebaseAPI firebaseAPI,
    GlobalAppAPI appVersionApi,
    SharedPreferenceHelper preferenceHelper,
  ) =>
      Repository(firebaseAPI, appVersionApi, preferenceHelper);
  AuthenticationRepository provideAuthenticationRepository(
          SharedPreferenceHelper preferenceHelper, AuthenApi authenApi) =>
      AuthenticationRepository(preferenceHelper, authenApi);

  ProfileRepository provideProfileRepository(ProfileAPI profileApi) =>
      ProfileRepository(profileApi: profileApi);

  ContractRepository provideContractRepository(ContractAPI contractAPI) =>
      ContractRepository(contractAPI: contractAPI);

  MasterDataRepository provideMasterDataRepository(
          MasterDataApi masterDataApi) =>
      MasterDataRepository(masterDataApi: masterDataApi);

  AcademicLevelRepository provideAcademicLevelRepository(
          AcademicLevelAPI academicLevelAPI) =>
      AcademicLevelRepository(academicLevelAPI: academicLevelAPI);

  WorkExpRepository provideWorkExpRepository(WorkExpApi workExpApi) =>
      WorkExpRepository(workExpApi: workExpApi);

  CertificationRepository provideCertificationRepository(
          CertificationAPI certificationAPI) =>
      CertificationRepository(certificationAPI: certificationAPI);

  ClassRepository provideClassScheduleRepository(ClassAPI classScheduleAPI) =>
      ClassRepository(classScheduleAPI: classScheduleAPI);

  ClassEvaluationRepository provideClassEvaluationRepository(
          ClassEvaluationAPI classEvaluationAPI) =>
      ClassEvaluationRepository(classEvaluationAPI: classEvaluationAPI);

  NotificationRepository provideNotificationRepository(
          NotificationAPI notificationAPI) =>
      NotificationRepository(notificationAPI: notificationAPI);
  SalaryRepository provideSalaryRepository(SalaryAPI salaryAPI) =>
      SalaryRepository(salaryAPI: salaryAPI);

  static Future<Database> provideDatabase() async {
    // Key for encryption
    var encryptionKey = "";

    // Get a platform-specific directory where persistent app data can be stored
    final appDocumentDir = await getApplicationDocumentsDirectory();

    // Path with the form: /platform-specific-directory/demo.db
    final dbPath = join(appDocumentDir.path, DBConstants.DB_NAME);

    // Check to see if encryption is set, then provide codec
    // else init normal db with path
    var database;
    if (encryptionKey.isNotEmpty) {
      // Initialize the encryption codec with a user password
      var codec = getXXTeaCodec(password: encryptionKey);
      database = await databaseFactoryIo.openDatabase(dbPath, codec: codec);
    } else {
      database = await databaseFactoryIo.openDatabase(dbPath);
    }

    // Return database instance
    return database;
  }
}
