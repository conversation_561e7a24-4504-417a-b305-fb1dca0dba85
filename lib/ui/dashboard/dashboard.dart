import 'dart:math';
import 'package:another_flushbar/flushbar_helper.dart';
import 'package:flutter/gestures.dart';
import 'package:mobx/mobx.dart';
import 'package:skeletons/skeletons.dart';
import 'package:tutorO/constants/colors.dart';
import 'package:tutorO/constants/constant_titles.dart';
import 'package:tutorO/constants/responsive.dart';
import 'package:tutorO/constants/styles.dart';
import 'package:tutorO/models/class/class.dart';
import 'package:tutorO/stores/class_schedule/class_schedule_store.dart';
import 'package:tutorO/stores/master_date/master_data_store.dart';
import 'package:tutorO/stores/personal_info/personal_info_store.dart';
import 'package:tutorO/stores/teaching_manage/teaching_class_item.dart';
import 'package:tutorO/stores/teaching_manage/teaching_manage_store.dart';
import 'package:tutorO/ui/dashboard/teaching_manage/common/header_category.dart';
import 'package:tutorO/ui/sidebar/menu_button.dart';
import 'package:tutorO/ui/sidebar/sidebar.dart';
import 'package:tutorO/utils/date/date_convert.dart';
import 'package:tutorO/utils/ui/ui_utils.dart';
import '/utils/routes/routes.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:provider/provider.dart';

class HomeScreen extends StatefulWidget {
  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  //stores:---------------------------------------------------------------------
  late TeachingManageStore _teachingManageStore;
  late PersonalInfoStore _personalInfoStore;
  late MasterDataStore _masterDataStore;
  late ClassScheduleStore _classScheduleStore;

  bool isFirst = true;
  late ReactionDisposer loading;
  DateRes selectedDayInCalendar = DateRes.today();
  String title = (ConstantTitles
      .listTitle[Random().nextInt(ConstantTitles.listTitle.length)]);
  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    _personalInfoStore = Provider.of<PersonalInfoStore>(context, listen: true);
    _teachingManageStore =
        Provider.of<TeachingManageStore>(context, listen: true);
    _masterDataStore = context.read<MasterDataStore>();
    _classScheduleStore =
        Provider.of<ClassScheduleStore>(context, listen: true);

    if (isFirst) {
      _personalInfoStore.getPersonalInfo().then((value) {
        if (value.isTeacher == true) {
          _teachingManageStore.fetchClassTeaching();
          _classScheduleStore.fetchCountClassToday();
          _teachingManageStore.fetchClassIncoming({
            "from_date":
                DateConvert.formatDate(DateTime.now(), format: 'dd-MM-yyyy'),
            "to_date": DateConvert.formatDate(
                DateTime.now().add(Duration(days: 14)),
                format: 'dd-MM-yyyy')
          });
          var currentDay = DateTime.now();
          loadLessonsTeachingInWeek(
              DateTime(currentDay.year, currentDay.month, currentDay.day));
        }
        else {
          setState(() {
            _teachingManageStore.isLoadingClassTeachingComplete = true;
            _teachingManageStore.loadingComplete = true;
          });
        }
      }).catchError((e) {
        print(e);
      });
      final languageCode = 'vi';
      _masterDataStore.fetchGrades(languageCode);
      _masterDataStore.fetchSubjects(languageCode);
      _masterDataStore.fetchProvinces(countryCode: '');

      setState(() {
        isFirst = false;
      });
    }
    loading = reaction((_) => _teachingManageStore.isLoading, (loading) {
      if (loading == false) {
        if (_teachingManageStore.haveError) {
          UIUtils.showErrorMessage('Lỗi', context);
        }
      }
    });
    // print(MediaQuery.of(context).size.width);
  }

  @override
  void dispose() {
    loading();
    super.dispose();
  }

  int selectedIndex = 0;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      drawer: SidebarLeft(
          selectedIndex: selectedIndex, personalInfoStore: _personalInfoStore),
      body: Observer(builder: (_) {
        return Row(
          children: [
            if (Responsive.isDesktop(context))
              SidebarLeft(
                  selectedIndex: selectedIndex,
                  personalInfoStore: _personalInfoStore),
            Expanded(
              child: _buildBody(),
            ),
          ],
        );
      }),
    );
  }

  // Widget _buildLogoutButton() {
  //   return IconButton(
  //     onPressed: () {
  //       SharedPreferences.getInstance().then((preference) async {
  //         await preference.setBool(Preferences.is_logged_in, false);
  //         await _authStore.logout();
  //         Navigator.of(context, rootNavigator: true)
  //             .pushNamedAndRemoveUntil(Routes.login, (route) => false);
  //       });
  //     },
  //     icon: Icon(
  //       Icons.power_settings_new,
  //     ),
  //   );
  // }

  // body methods:--------------------------------------------------------------
  Widget _buildBody() {
    return Container(
      color: AppColors.orangeBgColor,
      height: MediaQuery.of(context).size.height,
      child: SingleChildScrollView(
        primary: false,
        child: Column(
          children: [_buildTopWidget(), _buildContent()],
        ),
      ),
    );
  }

// Phần header
  Widget _buildTopWidget() {
    return Container(
      height: 120,
      margin: EdgeInsets.only(top: 40, left: 40, right: 40),
      child: _personalInfoStore.isSuccess == true
          ? Row(
              children: [
                if (Responsive.isTablet(context))
                  (MenuButtonSideBar(
                    onTap: () {
                      _scaffoldKey.currentState!.openDrawer();
                    },
                  )),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Xin chào ${_personalInfoStore.personalInfoModel.fullName ?? ""} !!!',
                      style: TextStyle(
                          fontSize: 25,
                          color: AppColors.orangeBtnColor,
                          fontWeight: FontWeight.w600),
                    ),
                    SizedBox(
                      height: 10,
                    ),
                    Text(
                      title,
                      style: TextStyle(
                          fontSize: 18,
                          color: AppColors.secondTextColor,
                          fontWeight: FontWeight.w500),
                    )
                  ],
                ),
              ],
            )
          : _buildLoadingTopWidget(),
    );
  }

// Loading skeleton cho phần header
  Widget _buildLoadingTopWidget() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        if (Responsive.isTablet(context))
          (MenuButtonSideBar(
            onTap: () {
              _scaffoldKey.currentState!.openDrawer();
            },
          )),
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin: EdgeInsets.only(right: 300),
                child: SkeletonLine(
                    style: SkeletonLineStyle(
                  borderRadius: BorderRadius.circular(10),
                  height: 25,
                )),
              ),
              Container(
                margin: EdgeInsets.only(right: 600, top: 10),
                child: SkeletonLine(
                    style: SkeletonLineStyle(
                  borderRadius: BorderRadius.circular(6),
                  height: 16,
                )),
              ),
            ],
          ),
        ),
      ],
    );
  }

// Content
  Widget _buildContent() {
    return Container(
        padding: EdgeInsets.symmetric(horizontal: 40),
        child: Column(children: [
          _buildLessonIncoming(),
          // _buildLessonNotReviewed(),
          _buildTeachingLesson()
        ]));
  }

// Buổi học sắp diễn ra
  Widget _buildLessonIncoming() {
    var listClass = _teachingManageStore
        .listClassSessionsIncoming.listClassSessions
        ?.where((element) {
      return element.isInComingLesson(element.date);
    }).toList();
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCategoryHeader(
            'Buổi học sắp diễn ra ',
            () {
              Navigator.of(context).pushReplacementNamed(Routes.schedule);
            },
          ),
          SizedBox(
            height: 10,
          ),
          _teachingManageStore.loadingComplete == false
              ? _buildLoadingLessonsInDay()
              : listClass != null && listClass.isNotEmpty
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: Responsive.isShowing3items(context)
                          ? listClass.take(2).map((e) {
                              return _buildCategoryItem(
                                e.studentsNumber ?? 0,
                                e.className ?? "",
                                e.classCode ?? "",
                                "${e.date!.friendlyStartTime()} - ${e.date!.friendlyEndTime()}  ${e.date!.friendlyDate()}",
                                () {
                                  Navigator.of(context).pushNamed(
                                      Routes.session_detail_screen,
                                      arguments: {"idSession": e.sessionId});
                                },
                              );
                            }).toList()
                          : Responsive.isShowing4items(context)
                              ? listClass.take(4).map((e) {
                                  return _buildCategoryItem(
                                      e.studentsNumber ?? 0,
                                      e.className ?? "",
                                      e.classCode ?? "",
                                      "${e.date!.friendlyStartTime()} - ${e.date!.friendlyEndTime()}  ${e.date!.friendlyDate()}",
                                      () {
                                    Navigator.of(context).pushNamed(
                                        Routes.session_detail_screen,
                                        arguments: {"idSession": e.sessionId});
                                  });
                                }).toList()
                              : listClass.take(3).map((e) {
                                  return _buildCategoryItem(
                                      e.studentsNumber ?? 0,
                                      e.className ?? "",
                                      e.classCode ?? "",
                                      "${e.date!.friendlyStartTime()} - ${e.date!.friendlyEndTime()}  ${e.date!.friendlyDate()}",
                                      () {
                                    Navigator.of(context).pushNamed(
                                        Routes.session_detail_screen,
                                        arguments: {"idSession": e.sessionId});
                                  });
                                }).toList(),
                    )
                  : Container(
                      height: 288,
                      alignment: Alignment.center,
                      // color: Colors.yellow,
                      child: Text(
                        'Chưa có dữ liệu',
                        style: TextStyle(
                            fontSize: 25, color: AppColors.secondTextColor),
                      ),
                    )
        ],
      ),
    );
  }

// Buổi học chưa đánh giá
  Widget _buildLessonNotReviewed() {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCategoryHeader(
            'Buổi học chưa đánh giá',
            () {
              Navigator.of(context).pushReplacementNamed(Routes.schedule);
            },
          ),
          SizedBox(
            height: 10,
          ),
          Container(
            height: 288,
            alignment: Alignment.center,
            // color: Colors.yellow,
            child: Text(
              'Chưa có dữ liệu',
              style: TextStyle(fontSize: 25, color: AppColors.secondTextColor),
            ),
          )
        ],
      ),
    );
  }

// Category header
  Widget _buildCategoryHeader(String title, void Function()? onMoreTap) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          title,
          style: Styles.defaultFontStyle
              .copyWith(fontSize: 20, fontWeight: FontWeight.w700),
        ),
        InkWell(
            hoverColor: Colors.black.withOpacity(0.05),
            borderRadius: BorderRadius.circular(20),
            onTap: onMoreTap,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
              ),
              padding: EdgeInsets.symmetric(vertical: 10, horizontal: 14),
              child: Text(
                'Xem tất cả  >',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ))
      ],
    );
  }

// Category Item
  Widget _buildCategoryItem(int studentsNumber, String className,
      String classCode, String dateTime, VoidCallback onTap) {
    var listNumber = new List<int>.generate(
        studentsNumber < 3 ? studentsNumber : 4, (i) => i + 1);
    int numberMore = studentsNumber > 4 ? studentsNumber - 4 : 0;
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          width: 334,
          height: 288,
          padding: EdgeInsets.symmetric(vertical: 32, horizontal: 32),
          margin: EdgeInsets.symmetric(vertical: 10, horizontal: 20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title buổi học
                Padding(
                  padding: const EdgeInsets.only(bottom: 10.0),
                  child: Text(
                    className,
                    overflow: TextOverflow.ellipsis,
                    style: Styles.defaultFontStyle
                        .copyWith(fontWeight: FontWeight.w700, fontSize: 16),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(bottom: 10.0),
                  child: Text(
                    classCode,
                    style: TextStyle(
                        color: AppColors.thirdTextColor, fontSize: 13),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(bottom: 10.0),
                  child: Text(
                    dateTime,
                    style: TextStyle(
                        color: AppColors.thirdTextColor, fontSize: 13),
                  ),
                ),
                // Button trạng thái
                _buildActionButton(),
                // Hiển thị danh sách học sinh
                Container(
                  alignment: Alignment.center,
                  margin: EdgeInsets.only(top: 15),
                  constraints: BoxConstraints(minHeight: 60),
                  decoration: BoxDecoration(
                      color: AppColors.orangeBtnColor.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(15)),
                  padding: EdgeInsets.symmetric(horizontal: 15, vertical: 15),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        children: listNumber.map((e) {
                          return Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 5),
                            child: Image.asset(
                              "assets/images/avatar_${e}.png",
                              height: 36,
                              width: 36,
                            ),
                          );
                        }).toList(),
                      ),
                      if (numberMore != 0)
                        Container(
                          decoration: BoxDecoration(
                              border: Border.all(
                                  color: AppColors.orangeBtnColor,
                                  style: BorderStyle.solid,
                                  width: 1),
                              borderRadius: BorderRadius.circular(10)),
                          margin: EdgeInsets.symmetric(horizontal: 5),
                          height: 36,
                          width: 36,
                          child: Center(
                              child: Text(
                            '+${numberMore}',
                            style: TextStyle(fontWeight: FontWeight.w600),
                          )),
                        ),
                    ],
                  ),
                ),
              ]),
        ),
      ),
    );
  }

  // General Methods:-----------------------------------------------------------
  _showErrorMessage(String message) {
    Future.delayed(Duration(milliseconds: 0), () {
      if (message.isNotEmpty) {
        FlushbarHelper.createError(
          message: message,
          title: 'Lỗi',
          duration: Duration(seconds: 3),
        )..show(context);
      }
    });
    return SizedBox.shrink();
  }

  Widget _buildActionButton() {
    // return _buildCheckinButton();
    return SizedBox(
      height: 40,
    );
  }

  Widget _buildCheckinButton() {
    return Container(
      padding: EdgeInsets.all(10),
      decoration: BoxDecoration(
          color: AppColors.greenPositiveTextColor,
          borderRadius: BorderRadius.circular(20)),
      child: InkWell(
        onTap: () {},
        child: Text(
          'Checkin',
          style: TextStyle(
              color: Colors.white, fontWeight: FontWeight.w600, fontSize: 15),
        ),
      ),
    );
  }

  Widget _buildDraftButton() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
      decoration: BoxDecoration(
          color: AppColors.secondTextColor.withOpacity(0.6),
          borderRadius: BorderRadius.circular(20)),
      child: InkWell(
        child: Text(
          'Draft',
          style: TextStyle(
              color: Colors.white, fontWeight: FontWeight.w600, fontSize: 15),
        ),
      ),
    );
  }

// Loading skeleton cho các session ( Responsive )
  Widget _buildLoadingLessonsInDay() {
    var list = List<int>.generate(
        Responsive.isShowing4items(context)
            ? 4
            : Responsive.isShowing3items(context)
                ? 2
                : 3,
        (i) => i + 1);
    return Container(
      child: SingleChildScrollView(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: list.map((e) {
            return Container(
              width: 334,
              height: 288,
              margin: EdgeInsets.symmetric(vertical: 10, horizontal: 20),
              child: SkeletonAvatar(
                style: SkeletonAvatarStyle(
                    borderRadius: BorderRadius.circular(16)),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _headerCategory(String title, String number, bool showArrow,
      double fontSizeTitle, VoidCallback? action) {
    return HeaderCategory(
        title: title,
        number: number,
        showArrow: showArrow,
        fontSizeTitle: fontSizeTitle,
        callback: action);
  }

  String getTitleDate() {
    if (_teachingManageStore.listClassSessionsSpecificDay.listClassSessions !=
            null &&
        _teachingManageStore
            .listClassSessionsSpecificDay.listClassSessions!.isEmpty) return "";
    return selectedDayInCalendar.friendlyDate();
  }

// Các lớp đang giảng dạy
  Widget _buildTeachingLesson() {
    var classStillTeaching = _teachingManageStore.listClassTeaching.listClasses
        ?.where((element) => element.isTeaching());

    return Container(
        margin: EdgeInsets.only(top: 30),
        padding: EdgeInsets.only(bottom: 20),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _headerCategory("Lớp đang dạy", "${classStillTeaching!.length}",
                    true, 20, () {}),
                InkWell(
                    hoverColor: Colors.black.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(20),
                    onTap: () {
                      Navigator.of(context)
                          .pushReplacementNamed(Routes.schedule);
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      padding:
                          EdgeInsets.symmetric(vertical: 10, horizontal: 14),
                      child: Text(
                        'Xem tất cả  >',
                        style: TextStyle(
                            fontSize: 16, fontWeight: FontWeight.w600),
                      ),
                    ))
              ],
            ),
            !_teachingManageStore.isLoadingClassTeachingComplete
                ? _buildLoadingLessonsInDay()
                : classStillTeaching.isNotEmpty
                    ? Container(
                        width: double.infinity,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: classStillTeaching
                              .take(Responsive.isShowing3items(context)
                                  ? 2
                                  : Responsive.isShowing4items(context)
                                      ? 4
                                      : 3)
                              .map((item) {
                            return Container(
                                width: 334,
                                height: 288,
                                padding: EdgeInsets.only(
                                    top: 32, left: 32, right: 32, bottom: 20),
                                margin: EdgeInsets.symmetric(
                                    vertical: 10, horizontal: 20),
                                decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(16)),
                                child: TeachingClassItem(
                                  nameClass: item.name ?? "",
                                  codeClass: item.code ?? "",
                                  dayOfWeek: "${item.friendlyWeekDay()}",
                                  schedule: item.schedule,
                                  numberStudent: item.studentsNumber ?? 0,
                                  hours:
                                      "${item.schedule![0].friendlyStartTime()} - ${item.schedule![0].friendlyEndTime()}",
                                  showThreeDot: false,
                                  isTeaching: item.isTeaching(),
                                  marginBottom: 0,
                                  detailCallback: () {
                                    // Navigator.of(context)
                                    //     .pushNamed(Routes.classDetail, arguments: item);
                                  },
                                ));
                          }).toList(),
                        ),
                      )
                    : Container(
                        height: 288,
                        alignment: Alignment.center,
                        // color: Colors.yellow,
                        child: Text(
                          'Chưa có dữ liệu',
                          style: TextStyle(
                              fontSize: 25, color: AppColors.secondTextColor),
                        ),
                      )
          ],
        ));
  }

  void loadLessonsTeachingInWeek(DateTime time) {
    _teachingManageStore.fetchClassSpecificDay({
      "from_date": DateConvert.formatDate(
          DateTime(time.year, time.month, time.day)
              .subtract(Duration(days: time.weekday - 1)),
          format: 'dd-MM-yyyy'),
      "to_date": DateConvert.formatDate(
          DateTime(time.year, time.month, time.day)
              .add(Duration(days: 7 - time.weekday)),
          format: 'dd-MM-yyyy'),
      "per_page": 200
    });
  }
}

class MyCustomScrollBehavior extends MaterialScrollBehavior {
  // Override behavior methods and getters like dragDevices
  @override
  Set<PointerDeviceKind> get dragDevices => {
        PointerDeviceKind.touch,
        PointerDeviceKind.mouse,
        // etc.
      };
}
