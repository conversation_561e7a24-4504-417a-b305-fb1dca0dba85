import 'package:flutter/material.dart';
import 'package:tutorO/constants/assets.dart';
import 'package:tutorO/constants/colors.dart';
import 'package:tutorO/models/class/class.dart';
import 'package:tutorO/models/class_session/class_session.dart';
import 'package:tutorO/ui/dashboard/teaching_manage/teaching_manage.dart';
import 'package:tutorO/utils/routes/routes.dart';

class ItemTeachingCalendar extends StatelessWidget {
  final ClassSession classSession;
  final ClassSchedule? classSchedule;
  final GlobalKey<TeachingClassDropdownMenuState> _key = GlobalKey();

  ItemTeachingCalendar(
      {required this.classSession, @required this.classSchedule});

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          Navigator.of(context).pushNamed(Routes.session_detail_screen,
              arguments: {
                "idSession": classSession.sessionId,
                "idClass": classSchedule!.classId
              });
        },
        child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: Color(0xffF3F1FF),
              // border: Border.all(color: Color(0xffF8F8F8)),
              // boxShadow: [
              //   BoxShadow(
              //       offset: Offset(0, 10),
              //       blurRadius: 20,
              //       color: Color(0xffCDD8E3).withOpacity(0.25))
              // ],
            ),
            width: double.infinity,
            padding: EdgeInsets.only(
              left: 10,
              right: 13,
              top: 14,
              bottom: 19,
            ),
            child: GestureDetector(
              onTap: () {},
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                      margin: EdgeInsets.only(left: 10, right: 13, top: 10),
                      width: 5,
                      height: 120,
                      decoration: BoxDecoration(
                        color: AppColors.mainColor,
                        borderRadius: BorderRadius.circular(5),
                      )),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(classSession.classCode ?? "",
                                style: TextStyle(
                                    color: Color(0xff593CC1),
                                    fontWeight: FontWeight.w600,
                                    fontSize: 16)),
                            TeachingClassDropdownMenu(
                              key: _key,
                              classSession: classSession,
                            )
                          ],
                        ),
                        Container(
                          margin: EdgeInsets.only(top: 6),
                          child: Text(classSession.sessionChapter ?? "",
                              style: TextStyle(
                                  color: AppColors.mainTextColor,
                                  fontWeight: FontWeight.w700,
                                  fontSize: 18)),
                        ),
                        Container(
                            margin: EdgeInsets.only(top: 9),
                            child: Row(
                              children: [
                                Container(
                                  margin: EdgeInsets.only(right: 11),
                                  child: Image.asset(
                                    Assets.timeClockIcon,
                                    width: 16,
                                    height: 16,
                                  ),
                                ),
                                Text(
                                  // ignore: lines_longer_than_80_chars
                                  "${classSession.date!.friendlyStartTime()} - ${classSession.date!.friendlyEndTime()}",
                                  style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600,
                                      color: Color(0xff7F88A3)),
                                )
                              ],
                            )),
                        Container(
                          margin: EdgeInsets.only(top: 10),
                          child: Row(
                            children: [
                              Container(
                                  margin: EdgeInsets.only(right: 7),
                                  child: Image.asset(
                                    Assets.threeUsersIcon,
                                    width: 20,
                                    height: 20,
                                  )),
                              Text(
                                '${classSession.studentsNumber ?? 0} học sinh',
                                style: TextStyle(
                                    color: Color(0xff7F88A3),
                                    fontWeight: FontWeight.w600,
                                    fontSize: 16),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            )),
      ),
    );
  }
}
