import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:tutorO/constants/colors.dart';
import 'package:tutorO/models/class/class.dart';
import 'package:tutorO/models/class_session/class_session.dart';
import 'package:tutorO/utils/utils.dart';

class TeachingCalendar extends StatefulWidget {
  final Function? changeDateSelectedCallback;
  final Function? swapNewPage;
  final List<ClassSession> listClassSession;
  final bool? isLeftChevronVisible;
  final bool? isRightChevronVisible;
  TeachingCalendar(
      {this.changeDateSelectedCallback,
      this.isLeftChevronVisible = true,
      this.isRightChevronVisible = true,
      required this.listClassSession,
      this.swapNewPage});

  @override
  _TeachingCalendarState createState() => _TeachingCalendarState();
}

class _TeachingCalendarState extends State<TeachingCalendar> {
  CalendarFormat _calendarFormat = CalendarFormat.week;
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay = DateTime.now();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: _buildTableCalendarWithBuilders(context),
    );
  }

  bool isHover = false;
  Widget _buildTableCalendarWithBuilders(BuildContext context) {
    return TableCalendar(
      formatAnimationDuration: Duration(microseconds: 1),
      key: Key("TableCalendar"),
      startingDayOfWeek: StartingDayOfWeek.monday,
      weekendDays: [],
      calendarFormat: _calendarFormat,
      focusedDay: _focusedDay,
      availableCalendarFormats: {
        CalendarFormat.month: 'Tháng',
        CalendarFormat.week: 'Tuần',
      },
      availableGestures: AvailableGestures.horizontalSwipe,
      firstDay: DateTime.utc(2010, 10, 16),
      lastDay: DateTime.utc(2030, 3, 14),
      rowHeight: 60,
      onFormatChanged: (format) {
        setState(() {
          _calendarFormat = format;
        });
      },
      onPageChanged: (day) {
        if (widget.swapNewPage != null) {
          widget.swapNewPage!(day);
        }
        setState(() {
          // _selectedDay = day;
          _focusedDay = day; // update `_focusedDay` here as well
        });
      },
      selectedDayPredicate: (day) {
        return isSameDay(_selectedDay, day);
      },
      headerStyle: HeaderStyle(
          leftChevronVisible: widget.isLeftChevronVisible!,
          rightChevronVisible: widget.isRightChevronVisible!,
          formatButtonTextStyle: TextStyle(
              color: AppColors.secondTextColor,
              fontSize: 18,
              fontWeight: FontWeight.w600),
          formatButtonShowsNext: true,
          formatButtonVisible: false,
          titleCentered: false,
          titleTextStyle: TextStyle(
              color: AppColors.secondTextColor,
              fontSize: 20,
              fontWeight: FontWeight.w700)),
      calendarStyle: CalendarStyle(
        cellMargin: EdgeInsets.only(top: 10),
        outsideTextStyle:
            TextStyle(color: AppColors.orangeBtnColor.withOpacity(0.5)),
        outsideDaysVisible: true,
        isTodayHighlighted: false,
      ),
      locale: "vi",
      calendarBuilders: CalendarBuilders(
          selectedBuilder: (context, date, _) {
            var dateConvert =
                DateRes(day: date.day, month: date.month, year: date.year);
            var needMarkDate = widget.listClassSession
                .where(
                    (element) => Utils.equalDateRes(dateConvert, element.date!))
                .toList()
                .isNotEmpty;
            return Container(
              width: 124,
              decoration: BoxDecoration(
                  color: AppColors.orangeBtnColor.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(16)),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '${date.day}',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: needMarkDate ? Colors.red : null,
                    ),
                    height: 8,
                  )
                ],
              ),
            );
          },
          defaultBuilder: (context, date, _) {
            return defaultDateBuilder(context, date, isOutSide: false);
          },
          outsideBuilder: (context, date, _) {
            return defaultDateBuilder(context, date, isOutSide: true);
          },
          dowBuilder: dowBuilder),
      daysOfWeekHeight: 60,
    );
  }

  Widget dowBuilder(BuildContext context, DateTime date) {
    var stringDate = DateFormat.E("vi").format(date).replaceAll("Th ", "T");
    return Center(
        child: Text(stringDate,
            style: TextStyle(
                color: AppColors.mainTextColor,
                fontWeight: FontWeight.w400,
                fontSize: 16)));
  }

  Widget defaultDateBuilder(BuildContext context, DateTime date,
      {required bool isOutSide}) {
    var dateConvert =
        DateRes(day: date.day, month: date.month, year: date.year);
    var needMarkDate = widget.listClassSession
        .where((element) => Utils.equalDateRes(dateConvert, element.date!))
        .toList()
        .isNotEmpty;

    return Container(
      width: 124,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
      ),
      child: MouseRegion(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              alignment: Alignment.center,
              child: Text(
                '${date.day}',
                style: TextStyle(
                    color: (isOutSide
                        ? AppColors.orangeBtnColor
                        : Color(0xff363E59)),
                    fontSize: 20,
                    fontWeight: FontWeight.w500),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: needMarkDate ? Colors.red : null,
              ),
              width: 8,
              height: 8,
            )
          ],
        ),
      ),
    );
  }
}
