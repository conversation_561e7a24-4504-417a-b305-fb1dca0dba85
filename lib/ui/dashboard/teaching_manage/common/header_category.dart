import 'package:flutter/material.dart';
import 'package:tutorO/constants/colors.dart';
import 'package:tutorO/widgets/number_badge.dart';
import 'package:validators/validators.dart';

class HeaderCategory extends StatelessWidget {
  final String title;
  final String number;
  final bool showArrow;
  final Function? callback;
  final double fontSizeTitle;

  HeaderCategory(
      {required this.title,
      required this.number,
      required this.showArrow,
      this.callback,
      required this.fontSizeTitle});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Container(
              margin: EdgeInsets.only(right: 10),
              child: Text(
                title,
                style: TextStyle(
                    fontSize: fontSizeTitle,
                    fontWeight: FontWeight.bold,
                    color: AppColors.mainTextColor),
              ),
            ),
            Visibility(
              visible: isNumeric(number) && int.parse(number) > 0,
              child: NumberBadge(
                value: isNumeric(number) ? int.parse(number) : 0,
              ),
            )
          ],
        ),
      ],
    );
  }
}
