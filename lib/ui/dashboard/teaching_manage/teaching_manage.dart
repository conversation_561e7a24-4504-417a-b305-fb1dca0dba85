import 'package:flutter/material.dart';
import 'package:tutorO/models/class_session/class_session.dart';
import 'package:tutorO/ui/common/select_down_box.dart';

class TeachingClassDropdownMenu extends StatefulWidget {
  final ClassSession classSession;
  final Key? key;

  TeachingClassDropdownMenu({this.key, required this.classSession})
      : super(key: key);

  @override
  TeachingClassDropdownMenuState createState() =>
      TeachingClassDropdownMenuState();
}

class TeachingClassDropdownMenuState extends State<TeachingClassDropdownMenu> {
  late ClassSession classSession;
  @override
  void initState() {
    super.initState();
    classSession = widget.classSession;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    var items = [SelectDownBoxItem(tile: "view_detail", callback: () {})];
    return SelectDownBox(items);
  }
}
