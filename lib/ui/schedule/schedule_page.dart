import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:mobx/mobx.dart';
import 'package:provider/provider.dart';
import 'package:tutorO/constants/colors.dart';
import 'package:tutorO/constants/responsive.dart';
import 'package:tutorO/constants/styles.dart';
import 'package:tutorO/models/class/class.dart';
import 'package:tutorO/models/class_session/class_session.dart';
import 'package:tutorO/stores/personal_info/personal_info_store.dart';
import 'package:tutorO/stores/teaching_manage/teaching_manage_store.dart';
import 'package:tutorO/ui/common/menu_button.dart';
import 'package:tutorO/ui/dashboard/teaching_calendar/teaching_calendar.dart';
import 'package:tutorO/ui/sidebar/sidebar.dart';
import 'package:tutorO/utils/date/date_convert.dart';
import 'package:tutorO/utils/ui/ui_utils.dart';
import 'package:tutorO/utils/utils.dart';

import '../../utils/routes/routes.dart';

class ScheduleScreen extends StatefulWidget {
  ScheduleScreen({Key? key}) : super(key: key);

  @override
  State<ScheduleScreen> createState() => _ScheduleScreenState();
}

int selectedIndex = 1;

class _ScheduleScreenState extends State<ScheduleScreen> {
  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  late TeachingManageStore _teachingManageStore;
  late PersonalInfoStore _personalInfoStore;
  bool isFirst = true;
  late ReactionDisposer loading;
  ScrollController _scrollController = ScrollController();
  DateRes selectedDayInCalendar = DateRes.today();
  double _scrollOffsetInit = 50;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _teachingManageStore =
        Provider.of<TeachingManageStore>(context, listen: true);

    _personalInfoStore = Provider.of<PersonalInfoStore>(context, listen: true);
    var currentDay = DateTime.now();
    if (isFirst) {
      loadLessonsTeachingInWeek(currentDay);
      // _personalInfoStore.getPersonalInfo().then((value) {
      //   setState(() {
      //     isFirst = false;
      //   });
      // });

      setState(() {
        isFirst = false;
      });
    }
    loading = reaction((_) => _teachingManageStore.isLoading, (loading) {
      if (loading == false && _teachingManageStore.isLoading != null) {
        if (_teachingManageStore.haveError) {
    
        }
      }
    });
  }

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(() {
      setState(() {
        _scrollController.offset > _scrollOffsetInit
            ? _teachingManageStore.setButtonVisible(true)
            : _teachingManageStore.setButtonVisible(false);
      }); // <-- This is it.
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: SidebarLeft(
          selectedIndex: selectedIndex, personalInfoStore: _personalInfoStore),
      key: _scaffoldKey,
      body: Observer(builder: (_) {
        return Row(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (Responsive.isDesktop(context))
              SidebarLeft(
                selectedIndex: selectedIndex,
                personalInfoStore: _personalInfoStore,
              ),
            Expanded(child: _buildContent()),
          ],
        );
      }),
    );
  }

  Widget _buildContent() {
    return Stack(
      children: [
        Container(
          height: MediaQuery.of(context).size.height,
          color: AppColors.orangeBgColor,
          padding: EdgeInsets.all(32),
          child: SingleChildScrollView(
            controller: _scrollController,
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                SideBarButton(onTap: () {
                  _scaffoldKey.currentState!.openDrawer();
                }),
                // _buildTopContent(context),
                _buildBodyContent(context),
              ],
            ),
          ),
        ),
        if (_teachingManageStore.isButtonVisible == true)
          Positioned(
              bottom: 50,
              right: 50,
              child: Container(
                height: 50,
                width: 50,
                decoration: BoxDecoration(
                    color: AppColors.orangeBtnColor.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(99)),
                child: InkWell(
                  borderRadius: BorderRadius.circular(99),
                  onTap: () {
                    _scrollController.animateTo(0,
                        duration: Duration(milliseconds: 200),
                        curve: Curves.linear);
                  },
                  child: Icon(
                    Icons.arrow_upward_rounded,
                    color: Colors.white,
                  ),
                ),
              ))
      ],
    );
  }

  // Widget _buildTopContent(BuildContext context) {
  //   return Row(
  //     children: [
  //       if (!Responsive.isDesktop(context))
  //         (MenuButtonSideBar(
  //           onTap: () {
  //             _scaffoldKey.currentState!.openDrawer();
  //           },
  //         ))
  //       else
  //         (SizedBox(
  //           width: 32,
  //         )),
  //     ],
  //   );
  // }

  Widget _buildBodyContent(BuildContext context) {
    return Column(
      children: [_calendarInMonth(context)],
    );
  }

  Widget _calendarInMonth(BuildContext context) {
    var listClassSession = _teachingManageStore
        .listClassSessionsSpecificDay.listClassSessions
        ?.where((element) {
      return Utils.equalDateRes(element.date!, selectedDayInCalendar) &&
          element.isNormalLesson();
    }).toList();

    return Container(
        width: double.infinity,
        margin: EdgeInsets.only(top: 40),
        padding: EdgeInsets.symmetric(horizontal: 30, vertical: 30),
        decoration: BoxDecoration(
            color: AppColors.orangeBtnColor.withOpacity(0.2),
            borderRadius: BorderRadius.circular(24)),
        child: Column(
          children: [
            TeachingCalendar(
              isLeftChevronVisible: !_teachingManageStore.isLockedSwapPage,
              isRightChevronVisible: !_teachingManageStore.isLockedSwapPage,
              listClassSession: _teachingManageStore
                      .listClassSessionsSpecificDay.listClassSessions ??
                  [],
              swapNewPage: (date) {
                loadLessonsTeachingInWeek(date);
                setState(() {
                  selectedDayInCalendar = DateRes(
                      day: date.day, month: date.month, year: date.year);
                });
              },
              changeDateSelectedCallback: (date) {
                setState(() {
                  selectedDayInCalendar = DateRes(
                      day: date.day, month: date.month, year: date.year);
                });
              },
            ),
            _buildSectionMorning(),
            _buildSectionAfternoon(),
            _buildSectionEvening(),
          ],
        ));
  }

  // SectionLoading skeleton
  Widget _buildSectionLoading() {
    return Container(
        padding: EdgeInsets.symmetric(horizontal: 50),
        height: 200,
        // margin: EdgeInsets.symmetric(vertical: 10, horizontal: 20),
        child: Center(
            child: CircularProgressIndicator(
          strokeWidth: 6.5,
          color: AppColors.orangeBtnColor,
        )));
  }

  // Section "Lịch dạy buổi sáng"
  Widget _buildSectionMorning() {
    var listClassSessionMorning = _teachingManageStore
        .listClassSessionsSpecificDay.listClassSessions
        ?.where((e) {
      return (e.date!.startTime! ~/ 60) < 12;
    }).toList();
    return Container(
      padding: EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        color: AppColors.orangeBtnColor.withOpacity(0.2),
      ),
      constraints: BoxConstraints(minWidth: double.infinity),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                margin: EdgeInsets.only(top: 10, bottom: 10, left: 15),
                child: Text(
                  'Sáng',
                  style: TextStyle(
                      fontSize: 20,
                      color: AppColors.secondTextColor,
                      fontWeight: FontWeight.w600),
                ),
              ),
              Expanded(
                child: Container(
                  margin: EdgeInsets.symmetric(horizontal: 20),
                  height: 2,
                  color: AppColors.orangeBtnColor,
                ),
              ),
            ],
          ),
          SizedBox(
            height: 25,
          ),
          _teachingManageStore.isLoadingWeeklyClassComplete == false
              ? _buildSectionLoading()
              : listClassSessionMorning!.isNotEmpty
                  ? Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: _buildRowItem(listClassSessionMorning))
                  : Container(
                      constraints: BoxConstraints(minHeight: 200),
                      alignment: Alignment.center,
                      child: Text(
                        'Bạn không có lịch dạy nào vào buổi sáng',
                        style: Styles.defaultFontStyle.copyWith(fontSize: 22),
                      ),
                    ),
        ],
      ),
    );
  }

// Section "Lịch dạy buổi chiều"
  Widget _buildSectionAfternoon() {
    var listClassSessionAfternoon = _teachingManageStore
        .listClassSessionsSpecificDay.listClassSessions
        ?.where((e) {
      return (e.date!.startTime! ~/ 60) > 12 && (e.date!.startTime! ~/ 60) < 18;
    }).toList();
    return Container(
      padding: EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        color: AppColors.orangeBtnColor.withOpacity(0.2),
      ),
      constraints: BoxConstraints(minWidth: double.infinity),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                margin: EdgeInsets.only(bottom: 10, left: 15),
                child: Text(
                  'Chiều',
                  style: TextStyle(
                      fontSize: 20,
                      color: AppColors.secondTextColor,
                      fontWeight: FontWeight.w600),
                ),
              ),
              Expanded(
                child: Container(
                  margin: EdgeInsets.only(left: 20, right: 20),
                  height: 2,
                  color: AppColors.orangeBtnColor,
                ),
              ),
            ],
          ),
          SizedBox(
            height: 25,
          ),
          _teachingManageStore.isLoadingWeeklyClassComplete == false
              ? _buildSectionLoading()
              : listClassSessionAfternoon!.isNotEmpty
                  ? Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: _buildRowItem(listClassSessionAfternoon))
                  : Container(
                      constraints: BoxConstraints(minHeight: 200),
                      alignment: Alignment.center,
                      child: Text(
                        'Bạn không có lịch dạy nào vào buổi chiều',
                        style: Styles.defaultFontStyle.copyWith(fontSize: 22),
                      ),
                    ),
        ],
      ),
    );
  }

  // Section "Lịch dạy buổi tối"
  Widget _buildSectionEvening() {
    var listClassSessionEvening = _teachingManageStore
        .listClassSessionsSpecificDay.listClassSessions
        ?.where((e) {
      return (e.date!.startTime! ~/ 60) > 17 && (e.date!.startTime! ~/ 60) < 24;
    }).toList();
    return Container(
      padding: EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
          color: AppColors.orangeBtnColor.withOpacity(0.2),
          borderRadius: BorderRadius.vertical(bottom: Radius.circular(20))),
      constraints: BoxConstraints(minWidth: double.infinity),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                margin: EdgeInsets.only(bottom: 10, left: 15),
                child: Text(
                  'Tối',
                  style: TextStyle(
                      fontSize: 20,
                      color: AppColors.secondTextColor,
                      fontWeight: FontWeight.w600),
                ),
              ),
              Expanded(
                child: Container(
                  margin: EdgeInsets.only(left: 20, right: 20),
                  height: 2,
                  color: AppColors.orangeBtnColor,
                ),
              ),
            ],
          ),
          SizedBox(
            height: 25,
          ),
          _teachingManageStore.isLoadingWeeklyClassComplete == false
              ? _buildSectionLoading()
              : listClassSessionEvening!.isNotEmpty
                  ? Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: _buildRowItem(listClassSessionEvening))
                  : Container(
                      constraints: BoxConstraints(minHeight: 200),
                      alignment: Alignment.center,
                      child: Text(
                        'Bạn không có lịch dạy nào vào buổi tối',
                        style: Styles.defaultFontStyle.copyWith(fontSize: 22),
                      ),
                    ),
          SizedBox(
            height: 20,
          ),
        ],
      ),
    );
  }

  var listNumber = List<int>.generate(7, (i) => i + 1);
  // Get list item lịch dạy buổi sáng
  List<Widget> _buildRowItem(List<ClassSession>? listClassSession) {
    return listNumber.map((index) {
      return _buildTeachingItem(listClassSession, index - 1);
    }).toList();
  }

  Widget _buildTeachingItem(List<ClassSession>? listClassSession, int index) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: Container(
        child: Column(
          children: listClassSession!.map((e) {
            return Container(
              margin: EdgeInsets.only(
                  bottom: e.date!.weekday == index ? 10 : 0, right: 1),
              child: InkWell(
                borderRadius: BorderRadius.circular(15),
                onTap: () {
                  Navigator.of(context).pushNamed(Routes.session_detail_screen,
                      arguments: {"idSession": e.sessionId});
                },
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                  width: 127,
                  height: e.date!.weekday == index ? 200 : 0,
                  decoration: BoxDecoration(
                    color: e.date!.weekday == index
                        ? e.date!.startDay().day == DateTime.now().day
                            ? AppColors.orangeBtnColor.withOpacity(0.5)
                            : e.date!.startDay().isAfter(DateTime.now())
                                ? Color(0xff4B9F47).withOpacity(0.5)
                                : Color(0xff091B3D).withOpacity(0.3)
                        : null,
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: e.date!.weekday == index
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            Text(
                              e.className!,
                              maxLines: 1,
                              softWrap: false,
                              style: TextStyle(
                                fontWeight: FontWeight.w700,
                                color: AppColors.secondTextColor,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            Text(
                              '${e.sessionChapter}',
                              maxLines: 3,
                              overflow: TextOverflow.fade,
                              style: TextStyle(
                                color: AppColors.secondTextColor,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              '${e.classCode}',
                              overflow: TextOverflow.fade,
                              maxLines: 1,
                              softWrap: false,
                              style: TextStyle(
                                fontSize: 12,
                                color: AppColors.secondTextColor,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              '${e.date!.friendlyStartTime()} - ${e.date!.friendlyEndTime()}',
                              style: TextStyle(
                                color: AppColors.secondTextColor,
                                fontWeight: FontWeight.w500,
                                fontSize: 12,
                              ),
                            ),
                            Text(
                              '${e.date!.friendlyDate()}',
                              style: TextStyle(
                                color: AppColors.secondTextColor,
                                fontWeight: FontWeight.w500,
                                fontSize: 12,
                              ),
                            ),
                            Text(
                              '${e.studentsNumber} học sinh',
                              style: TextStyle(
                                color: AppColors.secondTextColor,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        )
                      : null,
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  void loadLessonTeachingInMonth(DateTime firstDateInMonth) {
    _teachingManageStore.fetchClassSpecificDay({
      "from_date": DateConvert.formatDate(
          DateTime(firstDateInMonth.year, firstDateInMonth.month, 1)
              .subtract(Duration(days: 6)),
          format: 'dd-MM-yyyy'),
      "to_date": DateConvert.formatDate(
          DateTime(firstDateInMonth.year, firstDateInMonth.month + 1, 1)
              .add(Duration(days: 6)),
          format: 'dd-MM-yyyy'),
      "per_page": 200
    });
  }

  void loadLessonsTeachingInWeek(DateTime time) {
    _teachingManageStore.fetchClassSpecificDay({
      "from_date": DateConvert.formatDate(
          DateTime(time.year, time.month, time.day)
              .subtract(Duration(days: time.weekday - 1)),
          format: 'dd-MM-yyyy'),
      "to_date": DateConvert.formatDate(
          DateTime(time.year, time.month, time.day)
              .add(Duration(days: 7 - time.weekday)),
          format: 'dd-MM-yyyy'),
      "per_page": 200
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}
