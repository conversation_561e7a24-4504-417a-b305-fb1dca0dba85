import 'package:buttons_tabbar/buttons_tabbar.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:tutorO/constants/assets.dart';
import 'package:tutorO/constants/colors.dart';
import 'package:tutorO/constants/font_family.dart';
import 'package:tutorO/constants/responsive.dart';
import 'package:tutorO/stores/personal_info/personal_info_store.dart';
import 'package:tutorO/ui/report/bar_chart.dart';
import 'package:tutorO/ui/sidebar/menu_button.dart';
import 'package:tutorO/ui/sidebar/sidebar.dart';
import 'package:tutorO/widgets/in_development_widget.dart';

class ReportScreen extends StatefulWidget {
  const ReportScreen({Key? key}) : super(key: key);

  @override
  State<ReportScreen> createState() => _ReportScreenState();
}

int selectedIndex = 2;

class _ReportScreenState extends State<ReportScreen>
    with TickerProviderStateMixin {
  late PersonalInfoStore _personalInfoStore;
  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  bool isFirst = true;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _personalInfoStore = Provider.of<PersonalInfoStore>(context, listen: true);
    if (isFirst) {
      // _personalInfoStore.getPersonalInfo().then((value) => {
      //       setState(() {
      //         isFirst = false;
      //       })
      //     });
      setState(() {
        isFirst = false;
      });
    }
  }

  int touchedIndex = -1;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: SidebarLeft(
          selectedIndex: selectedIndex, personalInfoStore: _personalInfoStore),
      key: _scaffoldKey,
      body: Observer(builder: (_) {
        return Row(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (Responsive.isDesktop(context))
              SidebarLeft(
                selectedIndex: selectedIndex,
                personalInfoStore: _personalInfoStore,
              ),
            Expanded(child: InDevelopmentWidget())
          ],
        );
      }),
    );
  }

  Widget _buildContent() {
    return Container(
      width: double.infinity,
      height: MediaQuery.of(context).size.height,
      padding: EdgeInsets.all(Responsive.isTablet(context) ? 20 : 32),
      decoration: BoxDecoration(color: AppColors.orangeBgColor),
      child: Column(
        children: [
          _buildTopContent(context),
          Expanded(child: _buildBodyContent())
        ],
      ),
    );
  }

  Widget _buildTopContent(BuildContext context) {
    return Row(
      children: [
        if (!Responsive.isDesktop(context))
          (MenuButtonSideBar(
            onTap: () {
              _scaffoldKey.currentState!.openDrawer();
            },
          ))
        else
          (SizedBox(
            width: 32,
          )),
      ],
    );
  }

// button tabbar
  int initialIndex = 0;
  Widget _buildBodyContent() {
    TabController _tabController =
        TabController(length: 2, initialIndex: initialIndex, vsync: this);
    return Container(
      width: double.infinity,
      child: Column(
        children: [
          Container(
            width: 373,
            padding: EdgeInsets.all(5),
            margin: EdgeInsets.only(bottom: 20),
            decoration: BoxDecoration(
                color: AppColors.orangeBtnColor.withOpacity(0.3),
                borderRadius: BorderRadius.circular(99)),
            child: ButtonsTabBar(
              radius: 99,
              decoration: BoxDecoration(
                color: AppColors.orangeBtnColor,
              ),
              contentPadding: EdgeInsets.symmetric(horizontal: 18),
              unselectedDecoration: BoxDecoration(
                  color: AppColors.orangeBtnColor.withOpacity(0.05)),
              controller: _tabController,
              unselectedLabelStyle: TextStyle(
                  color: AppColors.orangeBtnColor,
                  fontWeight: FontWeight.bold,
                  fontFamily: FontFamily.quickSand,
                  fontSize: 16),
              labelStyle: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontFamily: FontFamily.quickSand,
                  fontSize: 16),
              onTap: (p0) {
                setState(() {
                  initialIndex = p0;
                });
              },
              tabs: [
                Tab(
                  text: ' Báo cáo dự án ',
                ),
                Tab(
                  text: 'Báo cáo kỹ năng 4C',
                ),
              ],
            ),
          ),
          Expanded(
              child: TabBarView(
            controller: _tabController,
            children: [
              _buildProjectReportView(),
              _build4CSkillView(),
            ],
          ))
        ],
      ),
    );
  }

// tab báo cáo dự án
  Widget _buildProjectReportView() {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildDetailReport(context),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Responsive.isReportBigSize(context)
                  ? Expanded(
                      child: Padding(
                          padding: EdgeInsets.only(right: 80, left: 40),
                          child: _buildPieChart()))
                  : _buildPieChart(),
              Responsive.isReportBigSize(context)
                  ? Expanded(child: _buildBarChart())
                  : _buildBarChart()
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDetailReport(context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 32, horizontal: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Container(
            width: Responsive.isReportBigSize(context) ? 350 : 300,
            height: 240,
            padding: EdgeInsets.symmetric(horizontal: 10),
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(16)),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Text(
                  'Tổng số học sinh ghi danh',
                  style: TextStyle(fontWeight: FontWeight.w700, fontSize: 16),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '10',
                      style:
                          TextStyle(fontWeight: FontWeight.w700, fontSize: 20),
                    ),
                    SvgPicture.asset(Assets.userIcon)
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Column(
                      children: [
                        Container(
                          height: 50,
                          width: 125,
                          decoration: BoxDecoration(
                              color: Color(0xff4B9F47).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8)),
                          child: Center(
                            child: Text(
                              'Đi học',
                              style: TextStyle(
                                  fontWeight: FontWeight.w700, fontSize: 16),
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              '10',
                              style: TextStyle(
                                  fontWeight: FontWeight.w700, fontSize: 20),
                            ),
                            SvgPicture.asset(Assets.userIcon)
                          ],
                        ),
                      ],
                    ),
                    Container(
                      margin: EdgeInsets.only(left: 10, right: 10),
                      width: 3,
                      height: 50,
                      decoration: BoxDecoration(
                          color: AppColors.thirdTextColor,
                          borderRadius: BorderRadius.circular(5)),
                    ),
                    Column(
                      children: [
                        Container(
                          height: 50,
                          width: 125,
                          decoration: BoxDecoration(
                              color: Color(0xffF5B544).withOpacity(0.08),
                              borderRadius: BorderRadius.circular(8)),
                          child: Center(
                            child: Text(
                              'Bảo lưu',
                              style: TextStyle(
                                  fontWeight: FontWeight.w700, fontSize: 16),
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              '10',
                              style: TextStyle(
                                  fontWeight: FontWeight.w700, fontSize: 20),
                            ),
                            SvgPicture.asset(Assets.userIcon)
                          ],
                        ),
                      ],
                    ),
                  ],
                )
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 10),
            width: Responsive.isReportBigSize(context) ? 350 : 300,
            height: 240,
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(16)),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Text(
                  'Thời gian trung bình học sinh truy cập vào hệ thống',
                  style: TextStyle(fontWeight: FontWeight.w700, fontSize: 16),
                  textAlign: TextAlign.center,
                ),
                SvgPicture.asset(Assets.iconClock),
                Text(
                  '100 phút/ngày',
                  style: TextStyle(fontWeight: FontWeight.w700, fontSize: 25),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 10),
            width: Responsive.isReportBigSize(context) ? 350 : 300,
            height: 240,
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(16)),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Text(
                  'Số lượt thích',
                  style: TextStyle(fontWeight: FontWeight.w700, fontSize: 16),
                  textAlign: TextAlign.center,
                ),
                SvgPicture.asset(Assets.iconHeart),
                Text(
                  '2',
                  style: TextStyle(fontWeight: FontWeight.w700, fontSize: 25),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Biểu đồ tròn
  Widget _buildPieChart() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 60),
      constraints: BoxConstraints(minWidth: 320, maxWidth: 365),
      height: 517,
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(16)),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Text(
          //   'Tỷ lệ điểm danh',
          //   style: TextStyle(
          //       fontSize: 18,
          //       color: AppColors.secondTextColor,
          //       fontWeight: FontWeight.w700),
          // ),
          Container(
            height: Responsive.isReportBigSize(context) ? 300 : 200,
            width: Responsive.isReportBigSize(context) ? 300 : 200,
            child: PieChart(
              PieChartData(
                  sectionsSpace: 0,
                  centerSpaceRadius: 40,
                  pieTouchData: PieTouchData(
                    touchCallback: (FlTouchEvent event, pieTouchResponse) {
                      setState(() {
                        if (!event.isInterestedForInteractions ||
                            pieTouchResponse == null ||
                            pieTouchResponse.touchedSection == null) {
                          touchedIndex = -1;
                          return;
                        }
                        touchedIndex = pieTouchResponse
                            .touchedSection!.touchedSectionIndex;
                      });
                    },
                  ),
                  sections: showingSections()),
              swapAnimationDuration: Duration(milliseconds: 150), // Optional
              swapAnimationCurve: Curves.easeInOut, // Optional
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 60),
            child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDescription(AppColors.orangeBtnColor, 'Hoàn thiện'),
                      SizedBox(
                        height: 15,
                      ),
                      _buildDescription(AppColors.redChartColor, 'Ý tưởng'),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDescription(AppColors.blueChartColor, 'Lập trình'),
                      SizedBox(
                        height: 15,
                      ),
                      _buildDescription(AppColors.greenChartColor, 'Thiết kế')
                    ],
                  ),
                ]),
          )
        ],
      ),
    );
  }

// biểu đồ cột
  Widget _buildBarChart() {
    return Container(
        margin: EdgeInsets.only(
            right: Responsive.isReportBigSize(context) ? 40 : 0),
        constraints: BoxConstraints(minWidth: 600),
        height: 517,
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(16)),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildBarChartItem('Lập trình'),
                _buildBarChartItem('Hoàn thiện'),
              ],
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildBarChartItem('Ý tưởng'),
                _buildBarChartItem('Thiết kế'),
              ],
            ),
          ],
        ));
  }

// section cho biểu đồ tròn
  List<PieChartSectionData> showingSections() {
    return List.generate(4, (i) {
      final isTouched = i == touchedIndex;
      final fontSize = isTouched ? 25.0 : 16.0;
      final radius = isTouched
          ? Responsive.isReportBigSize(context)
              ? 120.0
              : 85.0
          : Responsive.isReportBigSize(context)
              ? 100.0
              : 65.0;

      switch (i) {
        case 0:
          return PieChartSectionData(
            color: AppColors.blueChartColor,
            value: 40,
            title: '40%',
            radius: radius,
            titleStyle: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          );
        case 1:
          return PieChartSectionData(
            color: AppColors.orangeBtnColor,
            value: 30,
            title: '30%',
            radius: radius,
            titleStyle: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          );
        case 2:
          return PieChartSectionData(
            color: AppColors.redChartColor,
            value: 15,
            title: '15%',
            radius: radius,
            titleStyle: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          );
        case 3:
          return PieChartSectionData(
            color: AppColors.greenChartColor,
            value: 15,
            title: '15%',
            radius: radius,
            titleStyle: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          );
        default:
          throw Error();
      }
    });
  }

  Widget _buildDescription(Color chartColor, String title) {
    return Row(
      children: [
        Container(
          margin: EdgeInsets.symmetric(horizontal: 5),
          height: 27,
          width: 27,
          decoration: BoxDecoration(
              color: chartColor, borderRadius: BorderRadius.circular(99)),
        ),
        Text(
          title,
          style: TextStyle(fontWeight: FontWeight.w700),
        )
      ],
    );
  }

  Widget _buildBarChartItem(String text) {
    return Container(
      decoration: BoxDecoration(
          color: AppColors.orangeBgColor,
          borderRadius: BorderRadius.circular(16)),
      width: 250,
      height: 220,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(left: 10),
            padding: EdgeInsets.symmetric(vertical: 3, horizontal: 7),
            decoration: BoxDecoration(
                color: AppColors.orangeBtnColor,
                borderRadius: BorderRadius.circular(99)),
            child: Text(text,
                style: TextStyle(
                    color: Colors.white, fontWeight: FontWeight.w700)),
          ),
          BarChartWidget(),
        ],
      ),
    );
  }

  Widget _build4cBarChartItem(String text) {
    return Container(
      margin: EdgeInsets.all(10),
      decoration: BoxDecoration(
          color: AppColors.orangeBgColor,
          borderRadius: BorderRadius.circular(16)),
      width: 300,
      height: 220,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(left: 10),
            padding: EdgeInsets.symmetric(vertical: 3, horizontal: 7),
            decoration: BoxDecoration(
                color: AppColors.orangeBtnColor,
                borderRadius: BorderRadius.circular(99)),
            child: Text(text,
                style: TextStyle(
                    color: Colors.white, fontWeight: FontWeight.w700)),
          ),
          BarChartWidget(),
        ],
      ),
    );
  }

// tab kỹ năng 4c
  Widget _build4CSkillView() {
    return Container(
      child: SingleChildScrollView(
        child: Column(
          children: [
            SizedBox(
              height: 50,
            ),
            _buildPieChart4CSkill(),
            SizedBox(
              height: 20,
            ),
            _build4cBarChart()
          ],
        ),
      ),
    );
  }

// biểu đồ tròn cho kỹ năng 4c
  Widget _buildPieChart4CSkill() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 60),
      height: 517,
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(16)),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Text(
          //   'Tỷ lệ điểm danh',
          //   style: TextStyle(
          //       fontSize: 18,
          //       color: AppColors.secondTextColor,
          //       fontWeight: FontWeight.w700),
          // ),
          Container(
            height: 300,
            width: 300,
            child: PieChart(
              PieChartData(
                  pieTouchData: PieTouchData(
                    touchCallback: (FlTouchEvent event, pieTouchResponse) {
                      setState(() {
                        if (!event.isInterestedForInteractions ||
                            pieTouchResponse == null ||
                            pieTouchResponse.touchedSection == null) {
                          touchedIndex = -1;
                          return;
                        }
                        touchedIndex = pieTouchResponse
                            .touchedSection!.touchedSectionIndex;
                      });
                    },
                  ),
                  sectionsSpace: 0,
                  centerSpaceRadius: 40,
                  sections: showing4cSkillSections()),
              swapAnimationDuration: Duration(milliseconds: 150), // Optional
              swapAnimationCurve: Curves.linear, // Optional
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 60),
            child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDescription(
                          AppColors.orangeBtnColor, 'Kỹ năng tư duy phản biện'),
                      SizedBox(
                        height: 15,
                      ),
                      _buildDescription(
                          AppColors.redChartColor, 'Kỹ năng giao tiếp'),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDescription(
                          AppColors.blueChartColor, 'Kỹ năng sáng tạo'),
                      SizedBox(
                        height: 15,
                      ),
                      _buildDescription(
                          AppColors.greenChartColor, 'Kỹ năng hợp tác')
                    ],
                  ),
                ]),
          )
        ],
      ),
    );
  }
// section cho kỹ năng 4c

  List<PieChartSectionData> showing4cSkillSections() {
    return List.generate(4, (i) {
      final isTouched = i == touchedIndex;
      final fontSize = isTouched ? 25.0 : 16.0;
      final radius = isTouched ? 150.0 : 130.0;

      switch (i) {
        case 0:
          return PieChartSectionData(
            color: AppColors.blueChartColor,
            value: 40,
            title: '40%',
            radius: radius,
            titleStyle: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          );
        case 1:
          return PieChartSectionData(
            color: AppColors.orangeBtnColor,
            value: 30,
            title: '30%',
            radius: radius,
            titleStyle: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          );
        case 2:
          return PieChartSectionData(
            color: AppColors.redChartColor,
            value: 15,
            title: '15%',
            radius: radius,
            titleStyle: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          );
        case 3:
          return PieChartSectionData(
            color: AppColors.greenChartColor,
            value: 15,
            title: '15%',
            radius: radius,
            titleStyle: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          );
        default:
          throw Error();
      }
    });
  }

// Biểu đồ cột kỹ năng 4c
  Widget _build4cBarChart() {
    return Container(
        padding: EdgeInsets.symmetric(vertical: 20, horizontal: 20),
        constraints: BoxConstraints(minWidth: 500),
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(16)),
        child: MediaQuery.of(context).size.width < 1650
            ? Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Column(
                    children: [
                      _build4cBarChartItem('Tư duy phản biện'),
                      _build4cBarChartItem('Sáng tạo'),
                    ],
                  ),
                  Column(
                    children: [
                      _build4cBarChartItem('Giao tiếp'),
                      _build4cBarChartItem('Hợp tác'),
                    ],
                  ),
                ],
              )
            : Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _build4cBarChartItem('Tư duy phản biện'),
                  _build4cBarChartItem('Sáng tạo'),
                  _build4cBarChartItem('Giao tiếp'),
                  _build4cBarChartItem('Hợp tác'),
                ],
              ));
  }
}
