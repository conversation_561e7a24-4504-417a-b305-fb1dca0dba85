import 'package:flutter/material.dart';
import 'package:flutter_screen_scaling/flutter_screen_scaling.dart';
import 'package:tutorO/ui/common/triangle_painter.dart';
import '../../constants/colors.dart';

class PopOverItemModel {
  String codeString;

  PopOverItemModel({required this.codeString});
}

class PopOverItems extends StatelessWidget {
  final List<PopOverItemModel> listItem;
  final double width;

  const PopOverItems({Key? key, required this.listItem, this.width = 145})
      : assert(listItem.length > 0),
        super(key: key);

  static const heightArrow = 7;
  static const heightItem = 40;
  static const heightBorder = 4;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: ScreenScale.convertWidth(width),
      child: Stack(
        children: [
          Positioned(
            top: 2,
            right: 80,
            child: Container(
              width: width,
              height: 45,
              margin: EdgeInsets.only(top: heightArrow * 1),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(color: Color(0xffD7DEF5)),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Column(
                  children: listItem
                      .map((item) => Column(
                            children: [
                              InkWell(
                                onTap: () {
                                  var index = listItem.indexOf(item);
                                  Navigator.pop(context, {"index": index});
                                },
                                child: Container(
                                  alignment: Alignment.centerLeft,
                                  padding: EdgeInsets.only(left: 12),
                                  height: 40,
                                  child: Text(
                                    'Xem chi tiết',
                                    style: TextStyle(
                                      color: AppColors.mainTextColor,
                                      fontWeight: FontWeight.w600,
                                      fontSize: 15,
                                    ),
                                  ),
                                ),
                              ),
                              if (listItem.last != item)
                                Divider(
                                  thickness: 1,
                                  height: 0,
                                  color: Color(0xffE3E8F9),
                                )
                            ],
                          ))
                      .toList()),
            ),
          ),
          Positioned(
            top: 1.8,
            right: 80,
            child: CustomPaint(
              painter: TrianglePainter(
                strokeWidth: 10,
                paintingStyle: PaintingStyle.fill,
              ),
              child: Container(
                width: 20,
                height: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
