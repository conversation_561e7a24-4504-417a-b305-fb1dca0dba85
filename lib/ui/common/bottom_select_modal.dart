import 'package:flutter/material.dart';
import 'package:flutter_screen_scaling/flutter_screen_scaling.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:tutorO/constants/assets.dart';
import 'package:tutorO/ui/settings/common/divider.dart';

class BottomSelectItem {
  String key;
  String value;

  BottomSelectItem({required this.key, required this.value});
}

class BottomSelectModal extends StatelessWidget {
  final List<BottomSelectItem> listItem;
  final Function? callback;
  final BottomSelectItem? defaultItem;
  final String? title;

  final TextStyle textStyleCommon =
      TextStyle(fontWeight: FontWeight.w600, fontSize: (16));

  BottomSelectModal(
      {required this.listItem,
      this.callback,
      this.title = "",
      this.defaultItem});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: (300),
      padding: EdgeInsets.only(top: (15), left: (20), right: (20)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: const Radius.circular(20),
          topRight: const Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          Container(
              margin: EdgeInsets.only(bottom: (10)),
              height: (5),
              width: (62),
              color: Color(0xffD9D9D9)),
          buildTitle(),
          Container(
            height: (230),
            child: ListView.separated(
                itemBuilder: (context, index) {
                  return GestureDetector(
                      onTap: () {
                        if (callback != null) {
                          callback!(listItem[index].value, listItem[index].key);
                        }
                      },
                      child: Container(
                        height: (50),
                        color: Colors.transparent,
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              listItem[index].key,
                              style: textStyleCommon,
                            ),
                            if (defaultItem != null &&
                                defaultItem!.key == listItem[index].key)
                              Container(
                                  child: SvgPicture.asset(
                                      Assets.iconGreenCheckSvg)),
                          ],
                        ),
                      ));
                },
                separatorBuilder: (context, index) {
                  return SectionDivider(
                    color: Color(0xffebf3fb),
                  );
                },
                itemCount: listItem.length),
          ),
        ],
      ),
    );
  }

  Widget buildTitle() {
    if ((title ?? "").isEmpty) return Container();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: EdgeInsets.only(bottom: (15)),
          child: Text(
            title!,
            style: textStyleCommon,
          ),
        ),
        SectionDivider(
          color: Color(0xffebf3fb),
        )
      ],
    );
  }
}
