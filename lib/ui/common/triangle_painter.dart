import 'dart:ui';

import 'package:flutter/material.dart';

class TrianglePainter extends CustomPainter {
  final Color strokeColor;
  final PaintingStyle paintingStyle;
  final double strokeWidth;

  TrianglePainter(
      {this.strokeColor = Colors.black,
      this.strokeWidth = 3,
      this.paintingStyle = PaintingStyle.stroke});

  @override
  void paint(Canvas canvas, Size size) {
    final pointMode = PointMode.polygon;
    var x = 14.0;
    var y = 8.0;
    final points = [
      Offset(0, y),
      Offset(x / 2, 0),
      Offset(x, y),
    ];
    final paint = Paint()
      ..color = Color(0xffD7DEF5)
      ..strokeWidth = 1
      ..strokeCap = StrokeCap.round;
    canvas.drawPoints(pointMode, points, paint);
    final paint2 = Paint()
      ..color = Colors.white
      ..strokeWidth = 2
      ..strokeCap = StrokeCap.round;
    canvas.drawPath(getTrianglePath(x, y), paint2);
  }

  Path getTrianglePath(double x, double y) {
    return Path()
      ..moveTo(0 + 0.5, y + 1)
      ..lineTo(x / 2, 1)
      ..lineTo(x - 0.5, y + 1)
      ..lineTo(0, y + 1);
  }

  @override
  bool shouldRepaint(TrianglePainter oldDelegate) {
    return oldDelegate.strokeColor != strokeColor ||
        oldDelegate.paintingStyle != paintingStyle ||
        oldDelegate.strokeWidth != strokeWidth;
  }
}
