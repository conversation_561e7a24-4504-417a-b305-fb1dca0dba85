import 'package:flutter/material.dart';

import 'package:popover/popover.dart';
import 'package:tutorO/constants/assets.dart';
import 'package:tutorO/constants/colors.dart';
import 'package:tutorO/ui/common/pop_over_item.dart';

class SelectDownBoxItem {
  String tile;
  Function callback;

  SelectDownBoxItem({required this.tile, required this.callback});
}

class SelectDownBox extends StatefulWidget {
  final List<SelectDownBoxItem> listItems;

  SelectDownBox(this.listItems);

  @override
  State<SelectDownBox> createState() => _SelectDownBoxState();
}

class _SelectDownBoxState extends State<SelectDownBox> {
  bool isHover = false;
  @override
  Widget build(BuildContext context) {
    var listPopOverItem = widget.listItems
        .map((e) => PopOverItemModel(codeString: e.tile))
        .toList();
    return MouseRegion(
      onEnter: (event) {
        setState(() {
          isHover = true;
        });
      },
      onExit: (event) {
        setState(() {
          isHover = false;
        });
      },
      cursor: SystemMouseCursors.click,
      child: LayoutBuilder(
        builder: (context, viewportConstraints) {
          return GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () async {
              final boxTemp = context.findRenderObject() as RenderBox;
              final pos = boxTemp.localToGlobal(Offset.zero);
              var result = await showPopover(
                  context: context,
                  bodyBuilder: (context) => PopOverItems(
                        listItem: listPopOverItem,
                      ),
                  arrowDyOffset: pos.dy - 5,
                  barrierColor: Colors.transparent,
                  marginRight: 22,
                  radius: 4,
                  height: widget.listItems.length > 1 ? 100 : 60,
                  shadow: [],
                  backgroundColor: Colors.transparent);
              if (result != null) {
                var indexChoose = result["index"];
                widget.listItems[indexChoose].callback();
              }
            },
            child: AnimatedContainer(
              duration: Duration(milliseconds: 200),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(99),
                  color: isHover ? AppColors.mainColor.withOpacity(0.1) : null),
              // height: 35,
              child: Image.asset(
                Assets.threeDotsIcon,
                width: 35,
                height: 35,
              ),
            ),
          );
        },
      ),
    );
  }
}
