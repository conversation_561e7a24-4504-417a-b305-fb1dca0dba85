import 'package:flutter/material.dart';
import 'package:tutorO/widgets/status_badges/status_badges.dart';

class StatusLabel extends StatelessWidget {
  final bool isPositive;
  final String positiveCodeString;
  final String negativeCodeString;
  final int positiveColorBg;
  final int negativeColorBg;
  final int positiveColorText;
  final int negativeColorText;

  StatusLabel({
    required this.isPositive,
    this.positiveCodeString = 'Đang dạy',
    this.negativeCodeString = 'Ngừng hoạt động',
    this.positiveColorBg = 0xffDDF0DD,
    this.negativeColorBg = 0xffFFE4E0,
    this.positiveColorText = 0xff27AE60,
    this.negativeColorText = 0xffD35D52,
  });

  @override
  Widget build(BuildContext context) {
    return StatusBadge(
      text: isPositive ? '${positiveCodeString}' : '${negativeCodeString}',
      textColor:
          isPositive ? Color(positiveColorText) : Color(negativeColorText),
      backgroundColor:
          isPositive ? Color(positiveColorBg) : Color(negativeColorBg),
    );
  }
}
