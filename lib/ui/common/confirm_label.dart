import 'package:flutter/material.dart';
import 'package:flutter_screen_scaling/flutter_screen_scaling.dart';

class ConfirmLabel extends StatelessWidget {
  final String title;
  final bool enable;
  final Color? colorText;
  final int? fontSize;
  final FontWeight? fontWeight;

  ConfirmLabel(
      {required this.title,
      this.colorText = Colors.grey,
      this.enable = true,
      this.fontSize = 21,
      this.fontWeight = FontWeight.w700});

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      decoration:
          BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(28))),
      child: Text(title,
          style: TextStyle(
            fontSize: fontSize! * 1,
            color: colorText,
            fontWeight: FontWeight.w700,
          )),
    );
  }
}
