import 'package:flutter/material.dart';
import 'package:flutter_screen_scaling/flutter_screen_scaling.dart';
import '../../../../widgets/rounded_button_widget.dart';

class ConfirmButton extends StatefulWidget {
  final String title;
  final bool isLoading;
  final Function? callback;
  final bool enable;
  final Color? colorDisable;

  ConfirmButton(
      {required this.title,
      this.isLoading = false,
      this.colorDisable = Colors.grey,
      this.callback,
      this.enable = true});

  @override
  _ConfirmButtonState createState() => _ConfirmButtonState();
}

class _ConfirmButtonState extends State<ConfirmButton> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(10),
      child: InkWell(
        onTap: () {
          if (widget.callback != null && !widget.isLoading && widget.enable) {
            widget.callback!();
          }
        },
        child: Text('<PERSON><PERSON><PERSON> nhận'),
      ),
    );
  }
}
