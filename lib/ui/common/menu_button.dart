import 'package:flutter/material.dart';
import 'package:tutorO/constants/responsive.dart';
import 'package:tutorO/ui/sidebar/menu_button.dart';

class SideBarButton extends StatefulWidget {
  SideBarButton({Key? key, this.onTap}) : super(key: key);
  final void Function()? onTap;
  @override
  State<SideBarButton> createState() => _SideBarButtonState();
}

class _SideBarButtonState extends State<SideBarButton> {
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        if (!Responsive.isDesktop(context))
          (MenuButtonSideBar(onTap: widget.onTap
              // () {
              //   // _scaffoldKey.currentState!.openDrawer();
              // },
              ))
        else
          (SizedBox(
            width: 32,
          )),
      ],
    );
  }
}
