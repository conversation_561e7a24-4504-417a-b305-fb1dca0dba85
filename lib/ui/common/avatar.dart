import 'package:flutter/material.dart';
import 'package:tutorO/constants/colors.dart';

class Avatar extends StatelessWidget {
  final double width;
  final double padding;
  final bool needShadow;
  final String? url;
  final Function? callback;

  Avatar(
      {this.width = 80,
      this.padding = 12,
      this.needShadow = false,
      this.url,
      this.callback});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (callback != null) {
          callback!();
        }
      },
      child: Container(
        height: width,
        width: width,
        padding: EdgeInsets.only(top: padding, left: padding, right: padding),
        decoration: BoxDecoration(
          color: AppColors.orangeBtnColor.withOpacity(0.3),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          alignment: Alignment.bottomCenter,
          decoration: BoxDecoration(
              image: DecorationImage(
                  image: NetworkImage(
                    (url)!,
                  ),
                  fit: BoxFit.fitHeight),
              boxShadow: needShadow
                  ? [
                      BoxShadow(
                          blurRadius: 5,
                          offset: Offset(0, 3),
                          color: Color(0xff000000).withOpacity(0.06))
                    ]
                  : []),
        ),
      ),
    );
  }
}
