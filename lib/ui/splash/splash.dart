import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:mobx/mobx.dart';
import 'package:provider/provider.dart';
import 'package:tutorO/constants/assets.dart';
import 'package:tutorO/constants/colors.dart';
import 'package:tutorO/data/network/apis/global/global_api.dart';
import 'package:tutorO/data/repository/repository.dart';
import 'package:tutorO/stores/app_status/app_status_store.dart';
import 'package:tutorO/stores/user/authen/authen_store.dart';
import 'package:tutorO/utils/routes/routes.dart';
import 'package:tutorO/utils/ui/ui_utils.dart';
import 'package:tutorO/widgets/app_icon_widget.dart';
import 'package:flutter/material.dart';

class SplashScreen extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  late AppStatusStore _appStatusStore;
  late AuthenStore _authStore;
  late ReactionDisposer _disposer;
  late GlobalAppAPI globalAppAPI;

  Repository? repository;
  bool? canUpdate;
  String? text;
  String appVersion = "";

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    _authStore = context.read<AuthenStore>();
    _appStatusStore = Provider.of<AppStatusStore>(context);
    _appStatusStore.getDeviceId();
    _disposer = when((_) => _appStatusStore.initializeFirebaseDone, () {
      _appStatusStore.requestNotificationPermission();
    });
    // _appStatusStore.initializeFlutterFire();
    // navigate();
    startTimer();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Center(
          child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
              child: Container(
                  constraints: BoxConstraints(maxWidth: 400),
                  child: AppIconWidget(image: Assets.logo))),
          Container(
            padding: EdgeInsets.only(bottom: 100),
            child: CircularProgressIndicator(
              color: AppColors.mainColor,
            ),
          )
        ],
      )),
    );
  }

  startTimer() {
    var _duration = Duration(milliseconds: 2000);
    return Timer(_duration, navigate);
  }

  navigate() async {
    if (_authStore.isLoggedIn) {
      Navigator.of(context)
          .pushNamedAndRemoveUntil(Routes.home, (route) => false);
    } else {
      Navigator.of(context)
          .pushNamedAndRemoveUntil(Routes.login, (route) => false);
    }
    _checkCurrentConnection();
  }

  void _checkCurrentConnection() async {
    if (await _appStatusStore.repository.checkConnectivity() ==
        ConnectivityResult.none) {
      UIUtils.showErrorMessage('Không có kết nối Internet', context);
    }
  }

  @override
  void dispose() {
    super.dispose();
  }
}
