import 'package:flutter/material.dart';
import '../../../constants/assets.dart';
import '../../../constants/colors.dart';

class SectionItem extends StatelessWidget {
  final String iconAssetPath;
  final String title;
  final Widget? subTitleWidget;
  final bool rightArrow;
  final Function? callback;
  final Color? colorText;
  final Widget? subRightArrow;
  final EdgeInsets? padding;

  SectionItem(
      this.iconAssetPath,
      this.title,
      this.subTitleWidget,
      // ignore: avoid_positional_boolean_parameters
      this.rightArrow,
      this.callback,
      this.subRightArrow,
      {this.colorText = AppColors.mainTextColor,
      this.padding});

  @override
  Widget build(BuildContext context) {
    final double iconSize = 36;
    return InkWell(
      onTap: () {
        if (callback != null) {
          callback!();
        }
      },
      child: Container(
        height: 75,
        width: MediaQuery.of(context).size.width,
        color: Colors.white,
        padding: padding ?? EdgeInsets.fromLTRB(40, 0, 26, 0),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Padding(
                  padding: EdgeInsets.only(
                    right: 16,
                  ),
                  child: Image.asset(
                    iconAssetPath,
                    width: iconSize,
                    height: iconSize,
                  ),
                ),
                Expanded(
                    child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: colorText),
                    ),
                    subTitleWidget ?? SizedBox.shrink()
                  ],
                )),
                subRightArrow ?? SizedBox.shrink(),
                rightArrow ? Image.asset(Assets.iconRightArrow) : Container(),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
