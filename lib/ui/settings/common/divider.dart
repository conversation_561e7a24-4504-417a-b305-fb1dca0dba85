import 'package:flutter/material.dart';

class SectionDivider extends StatelessWidget {
  final double? endIndent;
  final double? beginIndent;
  final Color color;

  SectionDivider(
      {this.beginIndent = 0,
      this.endIndent = 0,
      this.color = const Color(0xfff0f0f0)});

  @override
  Widget build(BuildContext context) {
    return Divider(
      color: color,
      indent: beginIndent ?? 40,
      endIndent: endIndent,
      thickness: 1.0,
      height: 1.0,
    );
  }
}
