import 'package:flutter/material.dart';

import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:intl/intl.dart';
import 'package:jumping_dot/jumping_dot.dart';
import 'package:provider/provider.dart';
import 'package:tutorO/constants/colors.dart';
import 'package:tutorO/constants/responsive.dart';
import 'package:tutorO/stores/academic_level/academic_level_store.dart';
import 'package:tutorO/stores/certificate_store/certificate_store.dart';
import 'package:tutorO/stores/job_info/job_info_store.dart';
import 'package:tutorO/stores/personal_info/personal_info_store.dart';
import 'package:tutorO/stores/work_exp/work_exp_store.dart';
import 'package:tutorO/ui/sidebar/menu_button.dart';
import 'package:tutorO/ui/sidebar/sidebar.dart';

class SettingScreen extends StatefulWidget {
  const SettingScreen({Key? key}) : super(key: key);

  @override
  State<SettingScreen> createState() => _SettingScreenState();
}

int selectedIndex = 3;

class _SettingScreenState extends State<SettingScreen> {
  late PersonalInfoStore _personalInfoStore;
  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  late JobInfoStore _jobInfoStore;

  late AcademicLevelStore _academicLevelStore;
  late CertificationStore _certificationStore;
  late WorkExpStore _workExpStore;
  bool isFirst = true;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _personalInfoStore = Provider.of<PersonalInfoStore>(context, listen: true);
    _workExpStore = Provider.of<PersonalInfoStore>(context).workExpStore;
    _jobInfoStore = Provider.of<JobInfoStore>(context, listen: true);
    _certificationStore =
        Provider.of<PersonalInfoStore>(context).certificationStore;
    _academicLevelStore = _personalInfoStore.academicLevelStore;
    if (isFirst) {
      _jobInfoStore.fetchJobInfo();
      _workExpStore.fetchProfessionalExp();
      _certificationStore.fetchCertifications();
      _personalInfoStore.getPersonalInfo().then((value) => {
            _academicLevelStore.fetchAcademicLevels(),
            setState(() {
              isFirst = false;
            })
          });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: SidebarLeft(
          selectedIndex: selectedIndex, personalInfoStore: _personalInfoStore),
      key: _scaffoldKey,
      body: Observer(builder: (_) {
        return Row(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (Responsive.isDesktop(context))
              SidebarLeft(
                selectedIndex: selectedIndex,
                personalInfoStore: _personalInfoStore,
              ),
            Expanded(
                child: Container(
              height: MediaQuery.of(context).size.height,
              padding: EdgeInsets.all(32),
              color: AppColors.orangeBgColor,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    _buildTopContent(context),
                    _buildBodyContent(),
                  ],
                ),
              ),
            ))
          ],
        );
      }),
    );
  }

  Widget _buildTopContent(BuildContext context) {
    return Row(
      children: [
        if (!Responsive.isDesktop(context))
          (MenuButtonSideBar(
            onTap: () {
              _scaffoldKey.currentState!.openDrawer();
            },
          ))
        else
          (SizedBox(
            width: 32,
          )),
      ],
    );
  }

  Widget _buildBodyContent() {
    return Container(
      width: double.infinity,
      child: Column(
        mainAxisSize: MainAxisSize.max,
        children: [
          _buildPersonalInfo(),
          _buildJobInfo(),
          _buildAcademic(),
        ],
      ),
    );
  }

  Widget _buildPersonalInfo() {
    var personalInfo = _personalInfoStore.personalInfoModel;
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(bottom: 30),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Thông tin cá nhân',
            style: TextStyle(
                color: AppColors.secondTextColor,
                fontSize: 20,
                fontWeight: FontWeight.w600),
          ),
          Container(
            margin: EdgeInsets.only(top: 20),
            padding: EdgeInsets.all(20),
            width: double.infinity,
            constraints: BoxConstraints(minHeight: 220),
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(16)),
            child: _personalInfoStore.isSuccess
                ? Row(
                    children: [
                      Expanded(
                          child: Container(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            _builDetailItem(
                                'Họ và tên', personalInfo.fullName ?? ''),
                            _builDetailItem(
                                'Ngày sinh',
                                formatFriendlyDate(
                                    personalInfo.getBirthdayEdit())),
                            _builDetailItem('Giới tính',
                                personalInfo.convertGender() ?? ''),
                            _builDetailItem('Địa chỉ Facebook',
                                personalInfo.facebookAddress ?? ''),
                          ],
                        ),
                      )),
                      Expanded(
                          child: Container(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            _builDetailItem(
                                'Số điện thoại', personalInfo.phone ?? ''),
                            _builDetailItem(
                                'CMND,CCCD', personalInfo.numberIdCard ?? ''),
                            _builDetailItem('Ngày cấp',
                                personalInfo.getDateRangeIdCardEdit()),
                            _builDetailItem(
                                'Nơi cấp', personalInfo.issuedIdCardBy ?? ''),
                          ],
                        ),
                      )),
                    ],
                  )
                : SizedBox(
                    height: 20,
                    width: 20,
                    child: JumpingDots(
                      color: AppColors.orangeBtnColor,
                      radius: 10,
                      numberOfDots: 3,
                      animationDuration: Duration(milliseconds: 200),
                    ),
                  ),
          )
        ],
      ),
    );
  }

  Widget _buildJobInfo() {
    var jobInfo = _jobInfoStore.jobInfo;
    var personalInfo = _personalInfoStore.personalInfoModel;
    return Container(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Thông tin công việc',
            style: TextStyle(
                color: AppColors.secondTextColor,
                fontSize: 20,
                fontWeight: FontWeight.w600),
          ),
          Container(
            margin: EdgeInsets.only(top: 20),
            padding: EdgeInsets.all(20),
            width: double.infinity,
            constraints: BoxConstraints(minHeight: 270),
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(16)),
            child: _jobInfoStore.success
                ? Row(
                    children: [
                      Expanded(
                          child: Container(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            _builDetailItem('Thông tin công việc',
                                jobInfo.typeOfWork ?? ''),
                            _builDetailItem(
                                'Phòng ban', jobInfo.department ?? ''),
                            _builDetailItem('Tình trạng công việc',
                                jobInfo.workStatus ?? ''),
                            _builDetailItem(
                                'Bộ môn dạy', jobInfo.subject ?? ''),
                            _builDetailItem(
                                'Mã số thuế', jobInfo.taxCode ?? ''),
                          ],
                        ),
                      )),
                      Expanded(
                          child: Container(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            _builDetailItem(
                                'Nơi làm việc', jobInfo.location ?? ''),
                            _builDetailItem(
                                'Email', personalInfo.getEmailEdit().email),
                            _builDetailItem('Ngày tiếp nhận chính thức',
                                jobInfo.officialReceptionDate ?? ''),
                            _builDetailItem('Quản lý trực tiếp',
                                jobInfo.directManager ?? ''),
                            _builDetailItem(
                                'STK Ngân hàng', jobInfo.bankInfo ?? ''),
                          ],
                        ),
                      )),
                    ],
                  )
                : SizedBox(
                    height: 20,
                    width: 20,
                    child: JumpingDots(
                      color: AppColors.orangeBtnColor,
                      radius: 10,
                      numberOfDots: 3,
                      animationDuration: Duration(milliseconds: 200),
                    ),
                  ),
          )
        ],
      ),
    );
  }

  Widget _builDetailItem(String title, String description) {
    return Container(
      constraints: BoxConstraints(minHeight: 45),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SizedBox(
            width: 150,
            child: Text('${title}',
                maxLines: 2,
                style: TextStyle(
                    color: AppColors.orangeBtnColor,
                    fontSize: 18,
                    fontWeight: FontWeight.w600)),
          ),
          Text(':',
              style: TextStyle(
                  color: AppColors.orangeBtnColor,
                  fontSize: 18,
                  fontWeight: FontWeight.w600)),
          SizedBox(width: 30),
          Expanded(
            child: Text(description,
                style: TextStyle(
                    color: AppColors.thirdTextColor,
                    fontSize: 18,
                    fontWeight: FontWeight.w600)),
          ),
        ],
      ),
    );
  }

  Widget _buildAcademic() {
    return Container(
      margin: EdgeInsets.only(top: 20, bottom: 10),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Trình độ chuyên môn',
            style: TextStyle(
                color: AppColors.secondTextColor,
                fontSize: 20,
                fontWeight: FontWeight.w600),
          ),
          Container(
            margin: EdgeInsets.only(top: 20),
            padding: EdgeInsets.all(20),
            width: double.infinity,
            constraints: BoxConstraints(minHeight: 270),
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(16)),
            child: !_academicLevelStore.fetchingAcademicLevels
                ? Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(child: _buildAcademicContent()),
                      Expanded(child: _buildCertificateContent()),
                      Expanded(child: _buildWorkExpContent()),
                    ],
                  )
                : SizedBox(
                    height: 20,
                    width: 20,
                    child: JumpingDots(
                      color: AppColors.orangeBtnColor,
                      radius: 10,
                      numberOfDots: 3,
                      animationDuration: Duration(milliseconds: 200),
                    ),
                  ),
          )
        ],
      ),
    );
  }

  Widget _buildAcademicContent() {
    var academicInfo = _academicLevelStore.arrAcademicLevels;
    return Container(
      constraints: BoxConstraints(minHeight: 200),
      child: Column(mainAxisAlignment: MainAxisAlignment.start, children: [
        Text(
          'Trình độ học vấn',
          style: TextStyle(
            fontSize: 18,
            color: AppColors.orangeBtnColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        Column(
          children: academicInfo.map((e) {
            return Container(
              width: double.infinity,
              constraints: BoxConstraints(minHeight: 110),
              margin: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
              padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              decoration: BoxDecoration(
                  color: AppColors.orangeBgColor,
                  borderRadius: BorderRadius.circular(15)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Text(
                    e.trainingUnitAsText,
                    style: TextStyle(
                        fontSize: 18,
                        color: AppColors.secondTextColor,
                        fontWeight: FontWeight.w700),
                  ),
                  Text(
                    e.major,
                    style: TextStyle(
                        fontSize: 16,
                        color: AppColors.thirdTextColor,
                        fontWeight: FontWeight.w500),
                  ),
                  Text(
                    'Xếp loại: ${e.grade}',
                    style: TextStyle(
                        fontSize: 16,
                        color: AppColors.thirdTextColor,
                        fontWeight: FontWeight.w500),
                  ),
                ],
              ),
            );
          }).toList(),
        )
      ]),
    );
  }

  Widget _buildCertificateContent() {
    var certInfo = _certificationStore.arrCertificationModels;
    return Container(
      constraints: BoxConstraints(minHeight: 200),
      child: Column(children: [
        Text(
          'Chứng chỉ',
          style: TextStyle(
            fontSize: 18,
            color: AppColors.orangeBtnColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        Column(
          children: certInfo.map((e) {
            return Container(
              constraints: BoxConstraints(minHeight: 110),
              width: double.infinity,
              margin: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
              padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              decoration: BoxDecoration(
                  color: AppColors.orangeBgColor,
                  borderRadius: BorderRadius.circular(15)),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    e.certificateName ?? '',
                    style: TextStyle(
                        fontSize: 18,
                        color: AppColors.secondTextColor,
                        fontWeight: FontWeight.w700),
                  ),
                  Text(
                    'Xếp loại: ${e.grade ?? ''}',
                    style: TextStyle(
                        fontSize: 16,
                        color: AppColors.thirdTextColor,
                        fontWeight: FontWeight.w600),
                  ),
                  Text(
                    'Đơn vị cấp: ${e.certificateAuthority ?? ''}',
                    style: TextStyle(
                        fontSize: 16,
                        color: AppColors.thirdTextColor,
                        fontWeight: FontWeight.w600),
                  ),
                ],
              ),
            );
          }).toList(),
        )
      ]),
    );
  }

  Widget _buildWorkExpContent() {
    var workExpInfo = _workExpStore.listProfessionalExperience;
    return Container(
      constraints: BoxConstraints(minHeight: 200),
      child: Column(children: [
        Text(
          'Kinh nghiệm chuyên môn',
          style: TextStyle(
            fontSize: 18,
            color: AppColors.orangeBtnColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        Column(
          children: workExpInfo.map((e) {
            return Container(
              width: double.infinity,
              constraints: BoxConstraints(minHeight: 110),
              margin: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
              padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              decoration: BoxDecoration(
                  color: AppColors.orangeBgColor,
                  borderRadius: BorderRadius.circular(15)),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    e.workspaceName ?? '',
                    style: TextStyle(
                        fontSize: 18,
                        color: AppColors.secondTextColor,
                        fontWeight: FontWeight.w700),
                  ),
                  Text(
                    'Chức danh: ${e.jobTitle}',
                    style: TextStyle(
                        fontSize: 16,
                        color: AppColors.thirdTextColor,
                        fontWeight: FontWeight.w500),
                  ),
                ],
              ),
            );
          }).toList(),
        )
      ]),
    );
  }

  String formatFriendlyDate(String date) {
    final formatDate = DateFormat('dd/MM/yyyy');
    if (DateTime.tryParse(date) != null) {
      final label = formatDate.format(DateTime.tryParse(date)!);
      return label;
    }
    return date;
  }
}
