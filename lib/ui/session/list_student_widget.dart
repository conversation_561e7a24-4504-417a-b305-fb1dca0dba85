import 'package:flutter/material.dart';
import 'package:jumping_dot/jumping_dot.dart';
import 'package:provider/provider.dart';
import 'package:tutorO/models/class_session/class_session.dart';
import 'package:tutorO/stores/lesson_store/lesson_store.dart';
import 'package:tutorO/widgets/report_button_widget.dart';
import 'package:tutorO/ui/session/session_detail_screen.dart';
import 'package:tutorO/models/class/class.dart';
import 'package:expand_widget/expand_widget.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../constants/assets.dart';
import '../../constants/colors.dart';
import 'package:tutorO/models/student/student.dart';

import '../../models/attendance/attendance.dart';
import '../../data/network/apis/class/class_schedule_api.dart';
import 'package:tutorO/data/repository/class_repository.dart';

class StudentListWidget extends StatefulWidget {
  final List<StudentAttendance>? listStudent;
  final bool? isLoading;
  final int? itemCount;
  final int? sessionId;
  final ClassSessionStatusEnum? sessionState;
  StudentListWidget({
    Key? key,
    required this.listStudent,
    required this.itemCount,
    this.isLoading = true,
    this.sessionId,
    this.sessionState,
  }) : super(key: key);

  @override
  _StudentListWidgetState createState() => _StudentListWidgetState();
}

class _StudentListWidgetState extends State<StudentListWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 200),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> listStudent() {
      return widget.listStudent!
          .where((s) => s.studentStatus == StudentStatus.active)
          .map((e) {
        return StudentItemWidget(
            studentPortfolioLink: e.studentPortfolioLink,
            validPortfolio: e.validPortfolio,
            name: e.fullName,
            studentId: e.studentId,
            sessionId: widget.sessionId,
            sessionState: widget.sessionState);
      }).toList();
    }

    ;

    return Column(
      children: [
        Container(
          width: double.infinity,
          margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
          padding: EdgeInsets.fromLTRB(16, 16, 16, 0),
          decoration: BoxDecoration(
            color: Color(0xFFF5B544).withOpacity(0.2),
            borderRadius: BorderRadius.all(
              Radius.circular(15),
            ),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Text("Học sinh",
                      style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w700,
                          color: AppColors.mainTextColor)),
                  Image.asset(
                    Assets.singleDotIcon,
                    color: AppColors.mainTextColor,
                    width: 20,
                    height: 20,
                  ),
                  Text("${widget.itemCount}",
                      style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w700,
                          color: AppColors.mainColor))
                ],
              ),
              widget.listStudent!.length > 0
                  ? Column(
                      children: [
                        Column(
                          children: listStudent().take(3).toList(),
                        ),
                        ExpandChild(
                          child: Column(
                            children: listStudent().skip(3).toList(),
                          ),
                        ),
                      ],
                    )
                  : Column(
                      children: [
                        Text(
                          "Buổi học này không có học sinh nào",
                          style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.w700,
                              color: AppColors.mainTextColor),
                        ),
                        Container(
                          height: 10,
                        )
                      ],
                    ),
              SizedBox(
                height: 10,
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class StudentItemWidget extends StatefulWidget {
  final String? name;
  final String? gender;
  final String? birth;
  final int? studentId;
  final int? sessionId;
  final bool? validPortfolio;
  final String? studentPortfolioLink;
  // final LessonStore? store;
  final ClassSessionStatusEnum? sessionState;
  bool get showPortfolioLink => this.sessionState != RequestClassStatus.success;
  StudentItemWidget({
    Key? key,
    this.name = "",
    this.gender = "",
    this.birth = "",
    this.studentId,
    this.sessionId,
    this.sessionState,
    this.validPortfolio,
    this.studentPortfolioLink,
  }) : super(key: key);

  @override
  State<StudentItemWidget> createState() => _StudentItemWidgetState();
}

class _StudentItemWidgetState extends State<StudentItemWidget> {
  bool loading = true;
  bool? check;

  Future<void> _openStudentPortfolioLink() async {
    var portfolio_link =
        "http://student-portfolio.teky.vn/student-portfolios/get-or-create-then-redirect/?student_id=${widget.studentId}&session_id=${widget.sessionId}";
    final Uri _url = Uri.parse(portfolio_link);
    if (!await launchUrl(_url)) {}
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 5),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(
          Radius.circular(15),
        ),
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 25, vertical: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  child: Row(
                    children: [
                      Container(
                        width: 48,
                        height: 48,
                        color: Colors.grey,
                      ),
                      SizedBox(
                        width: 20,
                      ),
                      Container(
                        child: Container(
                          width: 200,
                          child: Text(
                            widget.name!,
                            style: TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.w700,
                                color: AppColors.mainTextColor),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  child: Visibility(
                   visible: widget.showPortfolioLink,
                    child:
                        AttendenceCheckButton(
                        hasCheck: false,
                        isJoined: widget.validPortfolio!,
                        name: 'Xem portfolio',
                        onTap: _openStudentPortfolioLink,
                      ),
                   ),
                ),
                // Container(
                //   child: EvaluateButtonWidget(
                //     isEvaluated: false,
                //     onTap: () {},
                //   ),
                // ),
                // Container(
                //   child: ViewReportButtonWidget(
                //     onTap: () {},
                //   ),
                // )
              ],
            ),
          )
        ],
      ),
    );
  }
}

class ListStudentLoading extends StatelessWidget {
  const ListStudentLoading({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
      padding: EdgeInsets.fromLTRB(16, 16, 16, 0),
      decoration: BoxDecoration(
        color: Color(0xFFF5B544).withOpacity(0.2),
        borderRadius: BorderRadius.all(
          Radius.circular(15),
        ),
      ),
      child: Column(
        children: [
          Align(
            alignment: Alignment.topLeft,
            child: Text("Học sinh",
                style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                    color: AppColors.mainTextColor)),
          ),
          Container(
            width: double.infinity,
            margin: EdgeInsets.symmetric(vertical: 5),
            padding: EdgeInsets.symmetric(vertical: 10),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(
                Radius.circular(8),
              ),
            ),
            child: SizedBox(
                width: 50,
                height: 50,
                child: Center(
                  child: JumpingDots(
                    color: AppColors.orangeBtnColor,
                    radius: 10,
                    numberOfDots: 3,
                    animationDuration: Duration(milliseconds: 200),
                  ),
                )),
          ),
          SizedBox(
            height: 15,
          )
        ],
      ),
    );
  }
}
