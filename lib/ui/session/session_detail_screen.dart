import 'package:flutter/material.dart';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:tutorO/models/class_session/class_session.dart';
import 'package:tutorO/models/student/student.dart';
import 'package:tutorO/ui/common/confirm_label.dart';
import 'package:tutorO/ui/session/cancel_lesson.dart';
import 'package:tutorO/utils/ui/ui_utils.dart';
import 'package:tutorO/widgets/empty_app_bar_widget.dart';
import 'package:flutter_mobx/src/observer.dart';
import 'package:tutorO/constants/colors.dart';
import 'package:tutorO/constants/assets.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import '../../constants/responsive.dart';
import '../../stores/lesson_images/lesson_images_store.dart';
import '../../stores/personal_info/personal_info_store.dart';
import '../../widgets/session_detail_primary_button.dart';
import '../sidebar/sidebar.dart';
import '/stores/lesson_store/lesson_store.dart';
import 'package:tutorO/ui/session/list_student_widget.dart';
import 'document_widget.dart';

class SessionDetailScreen extends StatefulWidget {
  const SessionDetailScreen({Key? key}) : super(key: key);

  @override
  State<SessionDetailScreen> createState() => _SessionDetailScreenState();
}

class _SessionDetailScreenState extends State<SessionDetailScreen> {
  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  late LessonStore _lessonStore;

  late PersonalInfoStore _personalInfoStore;
  int? idSession;
  int? idClass;
  late final LessonImagesStore _lessonImagesStore;
  bool? isFirst = true;
  @override
  void didChangeDependencies() {
    final Map arguments = ModalRoute.of(context)!.settings.arguments as Map;
    _lessonImagesStore = Provider.of<LessonImagesStore>(context, listen: true);
    ;
    _personalInfoStore = Provider.of<PersonalInfoStore>(context, listen: true);
    _lessonStore = Provider.of<LessonStore>(context, listen: true);

    if (arguments != null) {
      idSession = arguments['idSession'];
    }
    if (isFirst = true) {
      // _personalInfoStore.getPersonalInfo();
      setState(() {
        _lessonStore.fetchFetchListMediaPlanCompleted = false;
      });
      _lessonStore.fetchListMediaPlan(idSession!);
      _lessonStore.attendanceStore.fetchAttendances(idSession ?? 0, 'vi');
      _lessonStore.fetchDetailSession(idSession!);
      _lessonStore.fetchCheckinStatus(idSession!);
      _lessonStore.fetchRequestOffStatus(idSession!);
      _lessonImagesStore.fetchListLessonAndVideo(idSession ?? 0);

      setState(() {
        isFirst = false;
      });
    }
    super.didChangeDependencies();
  }

  int selectedIndex = 1;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      drawer: SidebarLeft(
          selectedIndex: selectedIndex, personalInfoStore: _personalInfoStore),
      primary: true,
      appBar: EmptyAppBar(),
      body: Row(
        mainAxisSize: MainAxisSize.max,
        children: [
          if (Responsive.isDesktop(context))
            SidebarLeft(
                selectedIndex: selectedIndex,
                personalInfoStore: _personalInfoStore),
          Expanded(
            child: Container(
              child: Stack(
                children: [
                  Container(
                    color: AppColors.orangeBgColor,
                  ),
                  Container(
                    // color: Colors.amber,
                    child: Scrollbar(
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Container(
                                // color: Colors.red,
                                padding: EdgeInsets.fromLTRB(30, 0, 30, 30),
                                child: _buildBody()),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Observer(builder: (context) {
                    return Align(
                        alignment: Alignment.topCenter,
                        child: _lessonStore.fetchDetailSessionCompleted == true
                            ? Container(
                                // padding: EdgeInsets.fromLTRB(30, 40, 30, 30),
                                padding: EdgeInsets.fromLTRB(25, 0, 0, 15),
                                child: Container(
                                    color: Color(0xFFF5B544).withOpacity(0.2),
                                    child: _buildHeader()))
                            : Container());
                  })
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Material(
      color: Color(0xFFfaf3e8),
      child: Observer(
        builder: (_) {
          return Container(
            padding: EdgeInsets.fromLTRB(5, 0, 50, 0),
            margin: EdgeInsets.only(bottom: 10, top: 20),
            height: 120,
            // margin: align ,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    onTap: Navigator.of(context).pop,
                    child: Container(
                      // margin: EdgeInsets.only(left: 15, right: 10),
                      width: 30,
                      height: 20,
                      child: SvgPicture.asset(
                        Assets.leftArrowBlack,
                      ),
                    ),
                  ),
                ),
                if (_lessonStore.fetchDetailSessionCompleted == true)
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // _buildTopWidget(),
                      Expanded(
                        child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Text(
                                    "Buổi ${_lessonStore.classSession.sectionIndex}/${_lessonStore.classSession.countSessions}",
                                    style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.w700,
                                        color: AppColors.mainTextColor),
                                  ),
                                  Icon(
                                    Icons.remove,
                                    color: AppColors.mainTextColor,
                                    size: 20.0,
                                  ),
                                  Text(
                                    "${_lessonStore.classSession.date != null ? _lessonStore.classSession.date!.friendlyDate() : ""}   ${_lessonStore.classSession.date != null ? _lessonStore.classSession.date?.friendlyStartTime() : ""} - ${_lessonStore.classSession.date != null ? _lessonStore.classSession.date?.friendlyEndTime() : ""}",
                                    style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.w700,
                                        color: AppColors.thirdTextColor),
                                  ),
                                ],
                              ),
                              Text(
                                "${_lessonStore.classSession.sessionChapter}",
                                style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w700,
                                    color: AppColors.mainTextColor),
                              ),
                            ]),
                      ),
                      Container(
                        child: Row(
                          children: [
                            // _buildCheckIn(context),
                            // SizedBox(
                            //   width: 15,
                            // ),
                            // if (_lessonStore.classSession.requestOffSession !=
                            //     null)
                            //   SessionDetailPrimaryButtonWidget(
                            //     lessonStore: _lessonStore,
                            //     sessionId:
                            //         _lessonStore.classSession.sessionId ?? 0,
                            //     text: _lessonStore
                            //             .classSession.requestOffSession!
                            //             .isAvailableCancelSession()
                            //         ? 'Xin nghỉ'
                            //         : 'Hủy xin nghỉ',
                            //     buttonColor: AppColors.redNegativeTextColor,
                            //   ),
                            //         Container(
                            //   height: 100,
                            //   width: 100,
                            //   child: CancelLessonWidget(
                            // lessonStore: _lessonStore,
                            // sessionId:
                            //     _lessonStore.classSession.sessionId ?? 0,
                            //   ),
                            // )
                          ],
                        ),
                      ),
                    ],
                  )
                else
                  Container(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildBody() {
    return Observer(builder: (_) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          _lessonStore.attendanceStore.fetchAttendancesCompleted == true
              ? Container(
                  margin: EdgeInsets.only(
                      top: _lessonStore.attendanceStore
                                      .fetchAttendancesCompleted ==
                                  true &&
                              _lessonStore.fetchDetailSessionCompleted == true
                          ? 180
                          : 30),
                  child: StudentListWidget(
                    listStudent: _lessonStore.attendanceStore.listAllStudent
                        .where((e) => e.studentStatus == StudentStatus.active)
                        .toList(),
                    itemCount:
                        _lessonStore.attendanceStore.listAllStudent.where((e) => e.studentStatus == StudentStatus.active)
                        .toList().length,
                    sessionId: idSession,
                    sessionState: _lessonStore.classSession.sessionState,
                    // store: _lessonStore,
                  ),
                )
              : Container(
                  margin: EdgeInsets.only(
                      top: _lessonStore.attendanceStore
                                      .fetchAttendancesCompleted ==
                                  true ||
                              _lessonStore.fetchDetailSessionCompleted == true
                          ? 200
                          : 30),
                  child: ListStudentLoading()),
          Container(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _lessonStore.fetchFetchListMediaPlanCompleted == true
                    ? DocumentWidget(
                        listSessionMediaPlan: _lessonStore.listSessionMediaPlan)
                    : ListDocumentLoading(),
                // ImageVideoSessionWidget(),
                // StudenProjectWidget(),
              ],
            ),
          ),
        ],
      );
    });
  }

  Widget _buildCheckIn(BuildContext context) {
    var classSession = _lessonStore.classSession;
    switch (classSession.sessionState) {
      case ClassSessionStatusEnum.draft:
        {
          return _buildCheckinWhenDraft(context);
        }
      case ClassSessionStatusEnum.done:
        {
          return _buildCheckinWhenDone(context);
        }
      case ClassSessionStatusEnum.confirm:
        {
          return _buildCheckInWhenConfirm(context);
        }
      case ClassSessionStatusEnum.cancel:
        {
          return _buildCheckinWhenCancel(context);
        }
      default:
        return Container();
    }
  }

  Widget _buildCheckinWhenCancel(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 32),
      child: ConfirmLabel(
          title: 'Buổi học đã bị huỷ',
          enable: false,
          colorText: Color(0xffEB5757),
          fontWeight: FontWeight.w400,
          fontSize: 16),
    );
  }

  Widget _buildCheckinWhenDraft(BuildContext context) {
    return Container(
        margin: EdgeInsets.symmetric(vertical: 32),
        child: ConfirmLabel(
          title: 'Buổi học dự kiến diễn ra',
          enable: false,
          colorText: Colors.green.shade400,
        ));
  }

  Widget _buildCheckinWhenDone(BuildContext context) {
    var classSession = _lessonStore.classSession;
    var checkinInTime = classSession.isCheckinSuccess();
    var checkinLate = classSession.isCheckinLate();

    if (checkinLate || checkinInTime) {
      var timeCheckin = DateTime.fromMillisecondsSinceEpoch(
          (classSession.checkInStatus!.checkinDatetime ?? 0) * 1000);
      var timeCheckInString =
          "${timeCheckin.hour.toString().padLeft(2, '0')}:${timeCheckin.minute.toString().padLeft(2, '0')}:${timeCheckin.second.toString().padLeft(2, '0')}";
      return Column(
        children: [
          Container(
              margin: EdgeInsets.only(top: 32, bottom: 11),
              child: ConfirmLabel(
                title: 'Thời gian checkin $timeCheckInString',
                enable: false,
                colorText: Colors.green.shade400,
                fontSize: 16,
              )),
          if (checkinInTime)
            ConfirmLabel(
              title: 'Check in đúng giờ',
              enable: false,
              colorText: Color(0xff2F80ED),
              fontWeight: FontWeight.w400,
              fontSize: 14,
            ),
          if (checkinLate)
            ConfirmLabel(
                title: 'Check in muộn giờ quy định',
                enable: false,
                colorText: Color(0xffEB5757),
                fontWeight: FontWeight.w400,
                fontSize: 14),
        ],
      );
    }

    if (classSession.isMissCheckin()) {
      return Container(
          margin: EdgeInsets.symmetric(vertical: 32),
          child: ConfirmLabel(
            title: 'Bạn đã quên checkin buổi học này',
            enable: false,
            colorText: Colors.red.shade300,
            fontSize: 16,
          ));
    }

    if (classSession.noInfoCheckin()) {
      return Container(
          margin: EdgeInsets.symmetric(vertical: 32),
          child: ConfirmLabel(
            title: "Không có thông tin check in",
            enable: false,
            colorText: Colors.red.shade300,
            fontSize: 16,
          ));
    }

    return Container();
  }

  Widget _buildCheckInWhenConfirm(BuildContext context) {
    var classSession = _lessonStore.classSession;
    if (classSession.checkInStatus != null &&
        classSession.haveCheckinStatus()) {
      return _buildCheckinWhenDone(context);
    }

    if (classSession.checkInStatus == null ||
        classSession.isCancelSessionSuccess()) return Container();
    return GestureDetector(
      onTap: () {
        if (classSession.canCheckIn()) {
          _lessonStore.checkInLesson(classSession.sessionId ?? 0);
        } else {
          UIUtils.showErrorMessage(
              "Bạn chỉ được Check in trước 15 phút", context);
        }
      },
      child: Container(
        margin: EdgeInsets.only(left: 4, right: 4, top: 25),
        width: double.infinity,
        height: 42,
        decoration: BoxDecoration(
            color: classSession.canCheckIn()
                ? AppColors.orange
                : AppColors.thirdBorderColor,
            borderRadius: BorderRadius.circular(30)),
        alignment: Alignment.center,
        child: Text(
          "Check in",
          style: TextStyle(
              color: Colors.white, fontWeight: FontWeight.w700, fontSize: 16),
        ),
      ),
    );
  }
}

class AttendenceCheckButton extends StatefulWidget {
  AttendenceCheckButton({
    Key? key,
    required this.name,
    required this.onTap,
    required this.isJoined,
    required this.hasCheck,
  }) : super(key: key);
  final String name;
  final VoidCallback onTap;
  final bool? isJoined;
  final bool? hasCheck;

  @override
  State<AttendenceCheckButton> createState() => _AttendenceCheckButtonState();
}

class _AttendenceCheckButtonState extends State<AttendenceCheckButton> {
  bool? isHover = false;
  Color? bgColor;
  Color? textColor;
  String surfixIconpath = "";
  getColor() {
    if (widget.hasCheck == false) {
      if (isHover == true && widget.isJoined == true) {
        setState(() {
          bgColor = AppColors.greenPositiveBgColor;
          textColor = AppColors.greenPositiveTextColor;
          surfixIconpath = Assets.iconRoundedGreenCheck;
        });
      } else if (isHover == true && widget.isJoined == false) {
        setState(() {
          bgColor = AppColors.redNegativeBgColor;
          textColor = AppColors.redNegativeTextColor;
          surfixIconpath = Assets.iconRoundedRedCross;
        });
      } else {
        setState(() {
          bgColor = AppColors.mainBgColor;
          textColor = AppColors.mainTextColor;
          surfixIconpath = widget.isJoined == true
              ? Assets.iconRoundedGrayCheckX2
              : Assets.iconRoundedGrayCross;
        });
      }
    } else {
      if (widget.isJoined == true) {
        setState(() {
          bgColor = AppColors.greenPositiveBgColor;
          textColor = AppColors.greenPositiveTextColor;
          surfixIconpath = Assets.iconRoundedGreenCheck;
        });
      } else {
        setState(() {
          bgColor = AppColors.redNegativeBgColor;
          textColor = AppColors.redNegativeTextColor;
        });
      }
    }
  }

  @override
  void initState() {
    getColor();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final hoveredTransform = Matrix4.identity()..scale(1.1);
    final transform1 = isHover == true ? hoveredTransform : Matrix4.identity();
    return MouseRegion(
      onHover: (event) {
        setState(() {
          isHover = true;
        });
        getColor();
      },
      onExit: (event) {
        setState(() {
          isHover = false;
        });
        getColor();
      },
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: widget.onTap,
        child: AnimatedContainer(
          transform: transform1,
          duration: const Duration(milliseconds: 250),
          margin: EdgeInsets.symmetric(vertical: 5),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                margin: EdgeInsets.all(5),
                padding: EdgeInsets.symmetric(horizontal: 25, vertical: 8),
                decoration: BoxDecoration(
                  color: bgColor,
                  boxShadow: [
                    BoxShadow(
                        color: AppColors.mainShadowColor,
                        offset: Offset(1, 2),
                        blurRadius: 3)
                  ],
                  borderRadius: BorderRadius.all(Radius.circular(10)),
                  // border: Border.all(width: 0.05),
                ),
                child: Row(
                  children: [
                    surfixIconpath != null
                        ? SizedBox(
                            width: 19,
                            height: 19,
                            child: Image.asset(
                              surfixIconpath,
                              fit: BoxFit.contain,
                            ),
                          )
                        : Container(),
                    SizedBox(
                      width: 5,
                    ),
                    Container(
                      child: Text(
                        widget.name,
                        style: TextStyle(
                            fontSize: 15,
                            fontWeight: FontWeight.w400,
                            color: textColor),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class EvaluateButtonWidget extends StatefulWidget {
  EvaluateButtonWidget({
    Key? key,
    required this.isEvaluated,
    required this.onTap,
  }) : super(key: key);
  bool isEvaluated;
  final VoidCallback onTap;

  @override
  State<EvaluateButtonWidget> createState() => _EvaluateButtonWidgetState();
}

class _EvaluateButtonWidgetState extends State<EvaluateButtonWidget> {
  Color? backgroundColor = AppColors.mainBgColor;
  Color? hoverColor = AppColors.mainColor;
  Color? textColor = AppColors.mainColor;
  bool? isHover = false;
  @override
  getColor() {
    if (widget.isEvaluated = true) {
      backgroundColor = AppColors.greenPositiveBgColor;
      textColor = AppColors.greenPositiveTextColor;
    } else {
      backgroundColor = AppColors.mainBgColor;
      textColor = AppColors.mainTextColor;
      ;
    }
    if (isHover == true) {
      if (widget.isEvaluated = true) {
        setState(() {
          backgroundColor = AppColors.greenPositiveBgColor;
          textColor = AppColors.greenPositiveTextColor;
        });
      } else {
        setState(() {
          backgroundColor = AppColors.mainBgColor;
          textColor = AppColors.mainTextColor;
        });
      }
    } else if (widget.isEvaluated == true) {
      setState(() {
        backgroundColor = AppColors.greenPositiveBgColor;
        textColor = AppColors.greenPositiveTextColor;
      });
    } else {
      setState(() {
        backgroundColor = AppColors.mainBgColor;
        textColor = AppColors.mainTextColor;
      });
    }
  }

  Widget build(BuildContext context) {
    final hoveredTransform = Matrix4.identity()..scale(1.1);
    final transform1 = isHover == true && widget.isEvaluated == false
        ? hoveredTransform
        : Matrix4.identity();
    return MouseRegion(
      onHover: (event) {
        setState(() {
          isHover = true;
          getColor();
        });
      },
      onExit: (event) {
        setState(() {
          isHover = false;
          getColor();
        });
      },
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: widget.onTap,
        child: AnimatedContainer(
          transform: transform1,
          duration: const Duration(milliseconds: 250),
          margin: EdgeInsets.symmetric(vertical: 5),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                margin: EdgeInsets.all(5),
                padding: EdgeInsets.symmetric(horizontal: 25, vertical: 8),
                decoration: BoxDecoration(
                    color: backgroundColor,
                    boxShadow: [
                      BoxShadow(
                          color: AppColors.mainShadowColor,
                          offset: Offset(1, 2),
                          blurRadius: 3)
                    ],
                    borderRadius: BorderRadius.all(Radius.circular(10)),
                    border: Border.all(width: 0.05, color: Colors.blueGrey)),
                child: Text(
                  widget.isEvaluated == true ? "Đã đánh giá" : "Chưa đánh giá",
                  style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: textColor),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class StudenProjectWidget extends StatefulWidget {
  const StudenProjectWidget({Key? key}) : super(key: key);

  @override
  State<StudenProjectWidget> createState() => _StudenProjectWidgetState();
}

class _StudenProjectWidgetState extends State<StudenProjectWidget> {
  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Observer(
        builder: (_) {
          return Container(
            margin: EdgeInsets.fromLTRB(0, 20, 20, 0),
            child: Container(
              padding: EdgeInsets.fromLTRB(15, 15, 15, 15),
              decoration: BoxDecoration(
                color: Color(0xFFF5B544).withOpacity(0.2),
                borderRadius: BorderRadius.all(
                  Radius.circular(15),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: EdgeInsets.only(bottom: 5),
                    width: 390,
                    child: Row(
                      children: [
                        Expanded(
                          child: Text("Dự án học sinh",
                              style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w700,
                                  color: AppColors.mainTextColor)),
                        ),
                        Row(
                          children: [
                            Text("Thêm mới",
                                style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w700,
                                    color: Colors.blueAccent)),
                            Icon(
                              Icons.add,
                              size: 20,
                              color: Colors.blueAccent,
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
                  // SizedBox(
                  //   width: 50,
                  // ),
                  Container(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        StudentProjectItemWidget(
                          studentName: "Nguyễn Văn A ",
                          projectName: "Dự án 1",
                        ),
                      ],
                    ),
                  )
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

class StudentProjectItemWidget extends StatefulWidget {
  final String? studentName;
  final String? projectName;
  StudentProjectItemWidget({Key? key, this.studentName = "", this.projectName})
      : super(key: key);

  @override
  State<StudentProjectItemWidget> createState() =>
      _StudentProjectItemWidgetState();
}

class _StudentProjectItemWidgetState extends State<StudentProjectItemWidget> {
  Color? backgroundColor = AppColors.mainBgColor;
  Color? hoverColor = AppColors.mainColor;
  Color? textColor = AppColors.mainColor;
  bool? isHover = false;
  @override
  Widget build(BuildContext context) {
    final hoveredTransform = Matrix4.identity()..scale(1.05);
    final transform1 = isHover == true ? hoveredTransform : Matrix4.identity();
    return MouseRegion(
      onHover: (event) {
        setState(() {
          isHover = true;
        });
      },
      onExit: (event) {
        setState(() {
          isHover = false;
        });
      },
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        child: AnimatedContainer(
          transform: transform1,
          duration: const Duration(milliseconds: 250),
          margin: EdgeInsets.symmetric(vertical: 5),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 400,
                margin: EdgeInsets.symmetric(vertical: 5),
                padding: EdgeInsets.symmetric(horizontal: 25, vertical: 10),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.all(
                    Radius.circular(15),
                  ),
                ),
                child: Row(
                  // mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Expanded(
                      child: Container(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.studentName!,
                              style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w700,
                                  color: AppColors.mainTextColor),
                            ),
                            Text(
                              widget.projectName!,
                              style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w700,
                                  color: AppColors.mainTextColor),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        image: DecorationImage(
                          image: CachedNetworkImageProvider(
                              "https://via.placeholder.com/50x50"),
                        ),
                        borderRadius: BorderRadius.all(
                          Radius.circular(15),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
