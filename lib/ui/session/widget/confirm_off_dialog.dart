import 'package:flutter/material.dart';

import 'package:flutter_svg/flutter_svg.dart';
import 'package:mobx/mobx.dart';
import 'package:provider/provider.dart';
import 'package:collection/collection.dart';
import 'package:tutorO/constants/assets.dart';
import 'package:tutorO/constants/colors.dart';
import 'package:tutorO/stores/master_date/master_data_store.dart';
import 'package:tutorO/ui/common/bottom_select_modal.dart';
import 'package:tutorO/ui/common/confirm_button.dart';

class ConfirmOffClassDialog extends StatefulWidget {
  final BuildContext dialogContext;
  final Function? btnConfirmCallback;
  final Widget? childWidget;
  final bool needCodeOff;

  ConfirmOffClassDialog(
      {required this.dialogContext,
      this.btnConfirmCallback,
      this.childWidget,
      this.needCodeOff = false});

  @override
  _ConfirmOffClassDialogState createState() => _ConfirmOffClassDialogState();
}

class _ConfirmOffClassDialogState extends State<ConfirmOffClassDialog> {
  late MasterDataStore _masterDataStore;
  late ReactionDisposer _reactionDisposerLoadRequestOffCode;
  bool isFirst = true;
  List<BottomSelectItem> listRequestOffCode = [];
  BottomSelectItem requestOff = BottomSelectItem(key: "", value: "");
  final TextEditingController _textEditingController = TextEditingController();

  final TextStyle textStyleCommon = TextStyle(
      color: AppColors.thirdTextColor,
      fontSize: 16,
      fontWeight: FontWeight.w600);

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (isFirst && widget.needCodeOff) {
      isFirst = false;
      _masterDataStore = Provider.of<MasterDataStore>(context, listen: true);
      _masterDataStore.fetchListReasonOff();
    }

    _reactionDisposerLoadRequestOffCode =
        reaction((_) => _masterDataStore.fetchReasonOffCompleted, (result) {
      if (result == true) {
        var listRequestOffCode = _masterDataStore.listReasonOff.map((e) {
          return BottomSelectItem(key: e.text ?? "", value: e.code ?? "");
        }).toList();
        setState(() {
          this.listRequestOffCode = listRequestOffCode;
          requestOff = listRequestOffCode[0];
        });
      }
    });
  }

  @override
  void dispose() {
    _reactionDisposerLoadRequestOffCode();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white.withOpacity(0.3),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: Container(
        width: 345,
        decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Color(0xffe3e8f9)),
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                  blurRadius: 20,
                  offset: Offset(0, (10)),
                  color: Color(0xffb1bcc7).withOpacity(0.25))
            ]),
        child: IntrinsicHeight(
          child: Stack(
            children: [
              Container(
                padding: EdgeInsets.only(top: (82)),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: (24)),
                      margin: EdgeInsets.only(bottom: (33)),
                      child: Text(
                        "Bạn muốn xin nghỉ dạy buổi học này?",
                        style: TextStyle(
                            fontWeight: FontWeight.w700,
                            fontSize: (22),
                            color: AppColors.mainTextColor),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    if (listRequestOffCode.isNotEmpty && widget.needCodeOff)
                      GestureDetector(
                        onTap: () {
                          showDropDown(context);
                        },
                        child: Container(
                          color: Colors.transparent,
                          margin: EdgeInsets.only(
                              bottom: (30), left: (20), right: (20)),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "Lý do xin nghỉ",
                                    style: TextStyle(
                                        color: AppColors.mainTextColor,
                                        fontSize: (16),
                                        fontWeight: FontWeight.w700),
                                  ),
                                  Container(
                                    margin: EdgeInsets.only(top: (3)),
                                    child: Text(
                                      requestOff.key,
                                      style: textStyleCommon,
                                    ),
                                  ),
                                ],
                              ),
                              Container(
                                child: SvgPicture.asset(Assets.downArrow),
                              )
                            ],
                          ),
                        ),
                      ),
                    widget.childWidget ?? Container(),
                    Container(
                      width: (295),
                      height: (78),
                      margin: EdgeInsets.only(bottom: (18)),
                      child: TextField(
                        controller: _textEditingController,
                        decoration: InputDecoration(
                            hintText: 'Ghi chú',
                            hintStyle: TextStyle(
                                fontSize: (16),
                                color: AppColors.thirdTextColor,
                                fontWeight: FontWeight.w600),
                            border: OutlineInputBorder(
                                borderSide: BorderSide(
                                    width: 1, color: Color(0xffcbd9e5)),
                                borderRadius: BorderRadius.circular((4)))),
                        keyboardType: TextInputType.multiline,
                        minLines: 2,
                        maxLines: 6,
                        style: TextStyle(
                            fontSize: (16),
                            color: AppColors.thirdTextColor,
                            fontWeight: FontWeight.w600),
                      ),
                    ),
                    InkWell(
                      child: Container(
                          color: AppColors.mainColor,
                          padding: EdgeInsets.symmetric(horizontal: (33)),
                          child: Text('Xác nhận')),
                      onTap: () {
                        Navigator.of(widget.dialogContext).pop();
                        if (widget.btnConfirmCallback != null) {
                          widget.btnConfirmCallback!(
                              _textEditingController.text, requestOff.value);
                        }
                      },
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: (33)),
                      margin: EdgeInsets.only(bottom: (30)),
                      child: TextButton(
                        onPressed: () {
                          Navigator.of(widget.dialogContext).pop();
                        },
                        child: Text(
                          'Hủy',
                          style: TextStyle(
                              fontSize: (16),
                              fontWeight: FontWeight.w600,
                              color: Color(0xff4b5574)),
                        ),
                      ),
                    )
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  void showDropDown(BuildContext context) {
    if (listRequestOffCode.isEmpty) return;
    var defaultItem = listRequestOffCode
        .firstWhereOrNull((element) => element.key == requestOff.key);
    showModalBottomSheet<void>(
        backgroundColor: Colors.transparent,
        context: context,
        builder: (context) => BottomSelectModal(
            listItem: listRequestOffCode,
            title: "Chọn lý do",
            defaultItem: defaultItem,
            callback: (value, key) {
              setState(() {
                requestOff = BottomSelectItem(key: key, value: value);
              });
              Navigator.of(context).pop();
            }));
  }
}
