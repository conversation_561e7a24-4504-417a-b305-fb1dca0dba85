import 'dart:io';
import 'package:path/path.dart' as p;
import 'package:cached_network_image/cached_network_image.dart';
import 'package:desktop_webview_window/desktop_webview_window.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:jumping_dot/jumping_dot.dart';
import 'package:path_provider/path_provider.dart';
import '../../constants/colors.dart';
import '../../models/class_session/class_session_media_plan/class_session_media_plan.dart';
import '../../utils/routes/routes.dart';

class DocumentWidget extends StatefulWidget {
  final List<SessionMediaPlan>? listSessionMediaPlan;
  DocumentWidget({Key? key, this.listSessionMediaPlan = const []})
      : super(key: key);

  @override
  State<DocumentWidget> createState() => _DocumentWidgetState();
}

class _DocumentWidgetState extends State<DocumentWidget> {
  late String? documentUrl = widget.listSessionMediaPlan!
      .where((e) => e.mediaType == MediaTypeEnum.document)
      .toList()
      .first
      .googleDriveLinkFormated;
  late String? presentationUrl = widget.listSessionMediaPlan!
      .where((e) => e.mediaType == MediaTypeEnum.presentation)
      .toList()
      .first
      .googleDriveLinkFormated;
  bool? hasPresent;
  bool? hasDoc;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    hasPresent = widget.listSessionMediaPlan!
        .any((element) => element.mediaType == MediaTypeEnum.presentation);
    hasDoc = widget.listSessionMediaPlan!
        .any((element) => element.mediaType == MediaTypeEnum.document);
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        margin: EdgeInsets.fromLTRB(0, 20, 20, 0),
        child: Container(
          padding: EdgeInsets.fromLTRB(15, 15, 15, 15),
          decoration: BoxDecoration(
            color: Color(0xFFF5B544).withOpacity(0.2),
            borderRadius: BorderRadius.all(
              Radius.circular(15),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text("Tài liệu giảng dạy",
                  style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w700,
                      color: AppColors.mainTextColor)),
              SizedBox(
                height: 10,
              ),
              Container(
                  child: widget.listSessionMediaPlan!.length != 0
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            hasPresent == true
                                ? DocumentItemWidget(
                                    callback: () {
                                      _onTap(presentationUrl!);
                                    },
                                    name: "Slide",
                                  )
                                : SizedBox(),
                            hasDoc == true
                                ? DocumentItemWidget(
                                    name: "Giáo án",
                                    callback: () {
                                      _onTap(documentUrl!);
                                    },
                                  )
                                : SizedBox(),
                            // DocumentItemWidget(
                            //   name: "Video",
                            // ),
                          ],
                        )
                      : Container(
                          padding: EdgeInsets.fromLTRB(16, 15, 16, 15),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.all(
                              Radius.circular(15),
                            ),
                            color: Colors.white,
                          ),
                          width: 250,
                          child: SizedBox(
                            child: Center(
                              child: Text(
                                "Chưa có tài liệu!",
                                style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w700,
                                    color: AppColors.mainTextColor),
                              ),
                            ),
                          ),
                        ))
            ],
          ),
        ),
      ),
    );
  }

  void _onTap(String launchUrl) async {
    final webview = await WebviewWindow.create(
      configuration: CreateConfiguration(
        userDataFolderWindows: await _getWebViewPath(),
        titleBarTopPadding: Platform.isMacOS ? 20 : 0,
      ),
    );
    webview
      ..setBrightness(Brightness.dark)
      ..setApplicationNameForUserAgent(" WebviewExample/1.0.0")
      ..launch(launchUrl)
      ..addOnUrlRequestCallback((url) {
        debugPrint('url: $url');
        final uri = Uri.parse(url);
        if (uri.path == '/login_success') {
          debugPrint('login success. token: ${uri.queryParameters['token']}');
          webview.close();
        }
      })
      ..onClose.whenComplete(() {
        debugPrint("on close");
      });
  }
}

class DocumentItemWidget extends StatefulWidget {
  final String? name;
  final VoidCallback? callback;
  DocumentItemWidget({Key? key, this.name = "", this.callback})
      : super(key: key);

  @override
  State<DocumentItemWidget> createState() => _DocumentItemWidgetState();
}

class _DocumentItemWidgetState extends State<DocumentItemWidget> {
  bool? isHover = false;
  @override
  Widget build(BuildContext context) {
    final hoveredTransform = Matrix4.identity()..scale(1.05);
    final transform = isHover == true ? hoveredTransform : Matrix4.identity();
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onHover: (event) {
        setState(() {
          isHover = true;
        });
      },
      onExit: (event) {
        setState(() {
          isHover = false;
        });
      },
      child: GestureDetector(
        onTap: widget.callback,
        child: AnimatedContainer(
          transform: transform,
          duration: const Duration(milliseconds: 250),
          width: 250,
          margin: EdgeInsets.symmetric(vertical: 5),
          padding: EdgeInsets.symmetric(horizontal: 25, vertical: 10),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(
              Radius.circular(15),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: CachedNetworkImageProvider(
                        "https://via.placeholder.com/50x50"),
                  ),
                  borderRadius: BorderRadius.all(
                    Radius.circular(15),
                  ),
                ),
              ),
              SizedBox(
                width: 15,
              ),
              Container(
                child: Text(
                  widget.name!,
                  style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w700,
                      color: AppColors.mainTextColor),
                ),
              ),
              SizedBox(
                width: 70,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

Future<String> _getWebViewPath() async {
  final document = await getApplicationDocumentsDirectory();
  return p.join(
    document.path,
    'desktop_webview_window',
  );
}

class ListDocumentLoading extends StatelessWidget {
  const ListDocumentLoading({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.fromLTRB(0, 20, 20, 0),
      child: Container(
        padding: EdgeInsets.fromLTRB(15, 15, 15, 15),
        decoration: BoxDecoration(
          color: Color(0xFFF5B544).withOpacity(0.2),
          borderRadius: BorderRadius.all(
            Radius.circular(15),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text("Tài liệu giảng dạy",
                style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: AppColors.mainTextColor)),
            SizedBox(
              height: 10,
            ),
            Container(
              padding: EdgeInsets.fromLTRB(16, 5, 16, 0),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.all(
                  Radius.circular(15),
                ),
                color: Colors.white,
              ),
              width: 250,
              child: SizedBox(
                width: 50,
                height: 50,
                child: Center(
                  child: JumpingDots(
                    color: AppColors.orangeBtnColor,
                    radius: 10,
                    numberOfDots: 3,
                    animationDuration: Duration(milliseconds: 200),
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
