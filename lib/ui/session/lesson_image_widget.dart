import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import '../../constants/colors.dart';

class ImageVideoSessionWidget extends StatefulWidget {
  final List<String>? listImages;
  ImageVideoSessionWidget({Key? key, this.listImages = const []})
      : super(key: key);

  @override
  State<ImageVideoSessionWidget> createState() =>
      _ImageVideoSessionWidgetState();
}

class _ImageVideoSessionWidgetState extends State<ImageVideoSessionWidget> {
  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Observer(
        builder: (_) {
          return Container(
            margin: EdgeInsets.fromLTRB(0, 20, 20, 0),
            child: Container(
              padding: EdgeInsets.fromLTRB(15, 15, 15, 15),
              decoration: BoxDecoration(
                color: Color(0xFFF5B544).withOpacity(0.2),
                borderRadius: BorderRadius.all(
                  Radius.circular(15),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    constraints: BoxConstraints(maxWidth: 320, minWidth: 250),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text("Hình ảnh, video",
                              style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w700,
                                  color: AppColors.mainTextColor)),
                        ),
                        MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: Container(
                            child: Text("Xem tất cả",
                                style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w700,
                                    color: Colors.blueAccent)),
                          ),
                        )
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: 400, minWidth: 250),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Wrap(
                          direction: Axis.horizontal,
                          children: listProductImageAddDefault(),
                        )
                      ],
                    ),
                  )
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  List<Widget> listProductImage() {
    return [
      ProductImageItemWidget(),
      ProductImageItemWidget(),
      ProductImageItemWidget(),
      ProductImageItemWidget(),
      ProductImageItemWidget(),
    ];
  }

  List<Widget> listProductImageAddDefault() {
    return listProductImage()
      ..add(ProductImageItemWidget(
        isDefaultItem: true,
      ));
  }
}

class ProductImageItemWidget extends StatefulWidget {
  final bool? isDefaultItem;

  const ProductImageItemWidget({Key? key, this.isDefaultItem = false})
      : super(key: key);

  @override
  State<ProductImageItemWidget> createState() => _ProductImageItemWidgetState();
}

class _ProductImageItemWidgetState extends State<ProductImageItemWidget> {
  bool? isHover = false;
  @override
  Widget build(BuildContext context) {
    final hoveredTransform = Matrix4.identity()..scale(1.05);
    final transform = isHover == true ? hoveredTransform : Matrix4.identity();
    return Container(
      child: widget.isDefaultItem == false
          ? MouseRegion(
              cursor: SystemMouseCursors.click,
              onHover: (event) {
                setState(() {
                  isHover = true;
                });
              },
              onExit: (event) {
                setState(() {
                  isHover = false;
                });
              },
              child: AnimatedContainer(
                transform: transform,
                duration: const Duration(milliseconds: 250),
                child: Container(
                  margin: EdgeInsets.only(right: 10, bottom: 10),
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: CachedNetworkImageProvider(
                          "https://via.placeholder.com/100x100"),
                    ),
                    borderRadius: BorderRadius.all(
                      Radius.circular(15),
                    ),
                  ),
                ),
              ),
            )
          : MouseRegion(
              cursor: SystemMouseCursors.click,
              onHover: (event) {
                setState(() {
                  isHover = true;
                });
              },
              onExit: (event) {
                setState(() {
                  isHover = false;
                });
              },
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 250),
                transform: transform,
                child: Container(
                  margin: EdgeInsets.only(right: 5, bottom: 5),
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.all(
                      Radius.circular(15),
                    ),
                  ),
                  child: Icon(
                    Icons.add,
                    size: 18,
                    color: Colors.black,
                  ),
                ),
              ),
            ),
    );
  }
}
