import 'package:flutter/material.dart';
import 'package:tutorO/models/reason_off/reason_off.dart';
import 'package:tutorO/stores/lesson_store/lesson_store.dart';
import 'package:tutorO/ui/common/confirm_label.dart';
import 'package:tutorO/ui/session/widget/confirm_off_dialog.dart';
import 'package:tutorO/utils/utils.dart';

import 'package:loader_overlay/loader_overlay.dart';

class CancelLessonWidget extends StatelessWidget {
  final LessonStore lessonStore;
  final int sessionId;

  CancelLessonWidget({required this.lessonStore, required this.sessionId});

  @override
  Widget build(BuildContext context) {
    var classSession = lessonStore.classSession;
    if (classSession.requestOffSession == null ||
        classSession.checkInStatus == null) return Container();
    if (classSession.isCancelSessionSuccess()) {
      return Container(
          margin: EdgeInsets.symmetric(vertical: 32),
          child: ConfirmLabel(
            title: "Đơn xin nghỉ của bạn đã được duyệt",
            enable: false,
            colorText: Colors.green.shade400,
          ));
    }

    if (classSession.isCancelSessionFail()) {
      return Container(
          margin: EdgeInsets.symmetric(vertical: 32),
          child: ConfirmLabel(
              title: "Đơn xin nghỉ của bạn đã không được duyệt",
              enable: false,
              colorText: Colors.red.shade400));
    }
    if (classSession.isCheckinSuccess() ||
        classSession.isMissCheckin() ||
        classSession.isCheckinLate() ||
        Utils.isStartLesson(classSession)) {
      return Container();
    }

    return GestureDetector(
      onTap: () {
        if (classSession.requestOffSession!.isAvailableCancelSession()) {
          showConfirmOffClassDialog(context);
        } else {
          lessonStore.cancelPermissionForm(classSession.sessionId ?? 0,
              classSession.requestOffSession!.reason!);
        }
      },
      child: Container(
        height: 56,
        margin: EdgeInsets.symmetric(vertical: 32),
        width: double.infinity,
        decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                  offset: Offset(0, 1),
                  blurRadius: 5,
                  color: Color.fromRGBO(205, 216, 227, 0.3))
            ],
            color: Colors.white,
            borderRadius: BorderRadius.circular(40),
            border: Border.all(color: Color(0xffEBF3FB))),
        alignment: Alignment.center,
        child: Text(
          classSession.requestOffSession!.isAvailableCancelSession()
              ? 'Xin nghỉ'
              : 'Hủy xin nghỉ',
          style: TextStyle(
              color: classSession.requestOffSession!.isAvailableCancelSession()
                  ? Color(0xff4B5574)
                  : Color(0xffF2994A),
              fontSize: 18,
              fontWeight: FontWeight.w600),
        ),
      ),
    );
  }

  void showConfirmOffClassDialog(BuildContext context) {
    showDialog(
        context: context,
        builder: (dialogContext) {
          return ConfirmOffClassDialog(
            needCodeOff: true,
            dialogContext: dialogContext,
            btnConfirmCallback: (text, code) {
              if (code != null && code.isNotEmpty) {
                lessonStore.cancelTeachSession(
                    sessionId, ReasonOff(code: code, text: text));
              }
            },
          );
        });
  }
}
