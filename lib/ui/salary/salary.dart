import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:jiffy/jiffy.dart';
import 'package:jumping_dot/jumping_dot.dart';
import 'package:provider/provider.dart';
import 'package:tutorO/constants/assets.dart';
import 'package:tutorO/constants/colors.dart';
import 'package:tutorO/constants/font_family.dart';
import 'package:tutorO/constants/responsive.dart';
import 'package:tutorO/models/salary/salary_model.dart';
import 'package:tutorO/stores/personal_info/personal_info_store.dart';
import 'package:tutorO/stores/salary/salary_store.dart';
import 'package:tutorO/ui/salary/widget/lesson_item.dart';
import 'package:tutorO/ui/sidebar/menu_button.dart';
import 'package:tutorO/ui/sidebar/sidebar.dart';
import 'package:carousel_slider/carousel_slider.dart';

class SalaryScreen extends StatefulWidget {
  const SalaryScreen({Key? key}) : super(key: key);

  @override
  State<SalaryScreen> createState() => _SalaryScreenState();
}

// Tạm thời bỏ tính năng báo cáo nên config index về 2
// int selectedIndex = 3;
int selectedIndex = 2;

class _SalaryScreenState extends State<SalaryScreen> {
  var now = new DateTime.now();
  TimeSalary? selectedTime = TimeSalary.thisMonth;
  CheckInStateFilter? selectedCheckin = CheckInStateFilter.all;
  AttendanceStateFilter? selectedAttendance = AttendanceStateFilter.all;

  ScrollController _scrollController = ScrollController();
  int activePage = 1;

  DateTimeRange dateRange = DateTimeRange(
      start: DateTime(DateTime.now().year, DateTime.now().month, 1),
      end: DateTime.now().month < 12
          ? DateTime(DateTime.now().year, DateTime.now().month + 1, 0)
          : DateTime(DateTime.now().year + 1, 1, 0));
  late PersonalInfoStore _personalInfoStore;
  late SalaryStore _salaryStore;
  double _scrollOffsetInit = 50;
  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  bool isFirst = true;
  @override
  void didChangeDependencies() {

    super.didChangeDependencies();
    _personalInfoStore = Provider.of<PersonalInfoStore>(context, listen: true);
    _salaryStore = Provider.of<SalaryStore>(context, listen: true);

    if (isFirst) {
     _salaryStore.setTimeRange(selectedTime!, dateRange.start, dateRange.end);
      _salaryStore.getSalaryInfo().then((_) {
        setState(() {
          if (_salaryStore.dataAttendanceInfo != null) {
           
          }
        });
      });
      setState(() {
        isFirst = false;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(() {
      setState(() {
        _scrollController.offset > _scrollOffsetInit
            ? _salaryStore.setButtonVisible(true)
            : _salaryStore.setButtonVisible(false);
      }); // <-- This is it.
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: SidebarLeft(
          selectedIndex: selectedIndex, personalInfoStore: _personalInfoStore),
      key: _scaffoldKey,
      body: Observer(builder: (_) {
        return Row(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (Responsive.isDesktop(context))
              SidebarLeft(
                selectedIndex: selectedIndex,
                personalInfoStore: _personalInfoStore,
              ),
            Expanded(
                child: Container(
              height: MediaQuery.of(context).size.height,
              padding: EdgeInsets.all(32),
              color: AppColors.orangeBgColor,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    _buildTopContent(context),
                    _buildBodyContent(),
                  ],
                ),
              ),
            ))
          ],
        );
      }),
    );
  }

  Widget _buildTopContent(BuildContext context) {
    return Row(
      children: [
        if (!Responsive.isDesktop(context))
          (MenuButtonSideBar(
            onTap: () {
              _scaffoldKey.currentState!.openDrawer();
            },
          ))
        else
          (SizedBox(
            width: 32,
          )),
      ],
    );
  }

  Widget _buildBodyContent() {
    return Stack(
      children: [
        Container(
          width: double.infinity,
          height: MediaQuery.of(context).size.height * 0.9,
          padding: EdgeInsets.all(32),
          child: SingleChildScrollView(
            controller: _scrollController,
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                   PopupMenuButton<TimeSalary>(             
                        initialValue: selectedTime,
                        tooltip: '',
                        child: Container(
                          padding: EdgeInsets.symmetric(
                              vertical: 10, horizontal: 10),
                          child: Row(children: [
                            Text(
                              'Thời gian: ',
                              style: TextStyle(
                                color: AppColors.orangeBtnColor,
                                fontSize: 16,
                                fontWeight: FontWeight.w700
                              ),
                            ),
                            _convertTimeRange()
                          ]),
                        ),
                        onSelected: (TimeSalary value) {
                       if (selectedTime != value){
                          setState(() {
                           selectedTime = value;
                          _salaryStore.setTimeRange(value, dateRange.start, dateRange.end);
                          _salaryStore.getSalaryInfo();
                        
                         });
                       }
                        },
                        itemBuilder: (context) =>
                            <PopupMenuEntry<TimeSalary>>[
                          const PopupMenuItem<TimeSalary>(
                            child: Text('Tháng này'),
                            value: TimeSalary.thisMonth,
                          ),
                          const PopupMenuItem<TimeSalary>(
                            child: Text('Tháng trước'),
                            value: TimeSalary.lastMonth,
                          ),
                           PopupMenuItem<TimeSalary>(
                            child: Text('Tháng ${Jiffy(dateRange.start).subtract(months:2).month}-${Jiffy(dateRange.start).subtract(months:2).year}'),
                            value: TimeSalary.last2Months,
                          ),
                           PopupMenuItem<TimeSalary>(
                            child: Text(' Tháng ${Jiffy(dateRange.start).subtract(months:3).month}-${Jiffy(dateRange.start).subtract(months:3).year}'),
                            value: TimeSalary.last3Months,
                          ),
                        ],
                      ),
                    SizedBox(
                      width: 10,
                    ),
                     Row(
                    children: [
                      PopupMenuButton<AttendanceStateFilter>(
                        tooltip: '',
                        initialValue: selectedAttendance,
                        child: Container(
                          padding: EdgeInsets.symmetric(
                              vertical: 10, horizontal: 10),
                          child: Row(children: [
                            Text(
                              'Điểm danh: ',
                              style: TextStyle(
                                color: AppColors.orangeBtnColor,
                                fontSize: 16,
                                fontWeight: FontWeight.w700
                              ),
                            ),
                            _convertAttendanceState()
                          ]),
                        ),
                        onSelected: (AttendanceStateFilter value) {
                     
                            setState(() {
                              selectedAttendance = value;
                              _salaryStore.setAttendanceState(value);
                              _salaryStore.getSalaryInfo();
                            });
                         
                        },
                        itemBuilder: (context) =>
                            <PopupMenuEntry<AttendanceStateFilter>>[
                          const PopupMenuItem<AttendanceStateFilter>(
                            child: Text('Tất cả'),
                            value: AttendanceStateFilter.all,
                          ),
                          const PopupMenuItem<AttendanceStateFilter>(
                            child: Text('Điểm danh đúng giờ'),
                            value: AttendanceStateFilter.good,
                          ),
                          const PopupMenuItem<AttendanceStateFilter>(
                            child: Text('Điểm danh muộn 24h'),
                            value: AttendanceStateFilter.late24,
                          ),
                          const PopupMenuItem<AttendanceStateFilter>(
                            child: Text('Điểm danh muộn 48h'),
                            value: AttendanceStateFilter.late48,
                          ),
                          const PopupMenuItem<AttendanceStateFilter>(
                            child: Text('Không có thông tin điểm danh'),
                            value: AttendanceStateFilter.noInfo,
                          ),
                          const PopupMenuItem<AttendanceStateFilter>(
                            child: Text('Điểm danh không hợp lệ'),
                            value: AttendanceStateFilter.notValid,
                          ),
                        ],
                      )
                    ],
                  ),
                  Row(
                    children: [
                      PopupMenuButton<CheckInStateFilter>(
                        initialValue: selectedCheckin,
                        tooltip: '',
                        child: Container(
                          padding: EdgeInsets.symmetric(
                              vertical: 10, horizontal: 10),
                          child: Row(children: [
                            Text(
                              'Checkin: ',
                              style: TextStyle(
                                color: AppColors.orangeBtnColor,
                                fontSize: 16,
                                fontWeight: FontWeight.w700
                              ),
                            ),
                            _convertCheckinState()
                          ]),
                        ),
                        onSelected: (CheckInStateFilter value) {
                      
                            setState(() {
                              selectedCheckin = value;
                              _salaryStore.setCheckInState(value);
                              _salaryStore.getSalaryInfo();
                            });
                         
                        },
                        itemBuilder: (context) =>
                            <PopupMenuEntry<CheckInStateFilter>>[
                          const PopupMenuItem<CheckInStateFilter>(
                            child: Text('Tất cả'),
                            value: CheckInStateFilter.all,
                          ),
                          const PopupMenuItem<CheckInStateFilter>(
                            child: Text('Checkin đúng giờ'),
                            value: CheckInStateFilter.good,
                          ),
                          const PopupMenuItem<CheckInStateFilter>(
                            child: Text('Checkin muộn'),
                            value: CheckInStateFilter.late,
                          ),
                        ],
                      )
                    ],
                  ),
                   
                  ],
                ),
                SizedBox(
                  height: 15,
                ),
                Column(
                    mainAxisSize: MainAxisSize.max,
                    children: _salaryStore.isLoadingComplete == false
                        ? [
                            Container(
                              margin: EdgeInsets.only(top: 32),
                              child: JumpingDots(
                                color: AppColors.orangeBtnColor,
                                radius: 10,
                                numberOfDots: 3,
                                animationDuration: Duration(milliseconds: 200),
                              ),
                            )
                          ]
                        : _salaryStore.listTeacherAttendanceInfo.isNotEmpty
                            ? _salaryStore.listTeacherAttendanceInfo.map((e) {
                                return ItemLesson(
                                  teacherAttendanceInfo: e,
                                );
                              }).toList()
                            : [
                                Container(
                                  margin: EdgeInsets.only(top: 32),
                                  alignment: Alignment.center,
                                  child: Text(
                                    'Chưa có dữ liệu',
                                    style: TextStyle(
                                        fontSize: 18,
                                        color: AppColors.secondTextColor,
                                        fontWeight: FontWeight.w600),
                                  ),
                                )
                              ]),
                if (_salaryStore.dataAttendanceInfo != null &&
                    _salaryStore.dataAttendanceInfo!.totalPage != null &&
                    _salaryStore.pageNumber <
                        _salaryStore.dataAttendanceInfo!.totalPage! &&
                    _salaryStore.isLoadingComplete)
                  InkWell(
                    borderRadius: BorderRadius.circular(99),
                    onTap: _salaryStore.dataAttendanceInfo != null &&
                            _salaryStore.dataAttendanceInfo!.totalPage !=
                                null &&
                            _salaryStore.pageNumber <
                                _salaryStore.dataAttendanceInfo!.totalPage! &&
                            _salaryStore.isLoadingMoreComplete == true
                        ? () {
                            _salaryStore.getMoreSalaryInfo(
                                dateRange.start, dateRange.end);
                          }
                        : null,
                    child: Container(
                        decoration: BoxDecoration(
                            color: _salaryStore.pageNumber <
                                        _salaryStore
                                            .dataAttendanceInfo!.totalPage! &&
                                    _salaryStore.isLoadingMoreComplete == true
                                ? AppColors.orangeBtnColor
                                : AppColors.thirdTextColor,
                            borderRadius: BorderRadius.circular(99)),
                        padding: EdgeInsets.all(10),
                        child: Text(
                          _salaryStore.isLoadingMoreComplete == true
                              ? 'Xem thêm'
                              : 'Đang tải...',
                          style: TextStyle(
                              fontSize: 18,
                              color: Colors.white,
                              fontWeight: FontWeight.w600),
                        )),
                  )
              ],
            ),
          ),
        ),
        if (_salaryStore.isButtonVisible == true)
          Positioned(
              bottom: 0,
              right: 0,
              child: Container(
                height: 50,
                width: 50,
                decoration: BoxDecoration(
                    color: AppColors.orangeBtnColor.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(99)),
                child: InkWell(
                  borderRadius: BorderRadius.circular(99),
                  onTap: () {
                    _scrollController.animateTo(0,
                        duration: Duration(milliseconds: 200),
                        curve: Curves.linear);
                  },
                  child: Icon(
                    Icons.arrow_upward_rounded,
                    color: Colors.white,
                  ),
                ),
              ))
      ],
    );
  }

  


  Widget _convertCheckinState() {
    switch (selectedCheckin) {
      case CheckInStateFilter.good:
        return Text(
          'Checkin đúng giờ',
          style: TextStyle(color: AppColors.thirdTextColor),
        );
      case CheckInStateFilter.late:
        return Text(
          'Checkin muộn',
          style: TextStyle(color: AppColors.thirdTextColor),
        );
      case CheckInStateFilter.all:
        return Text(
          'Tất cả',
          style: TextStyle(color: AppColors.thirdTextColor),
        );
      default:
        return Container();
    }
  }
  Widget _convertTimeRange(){
    switch (selectedTime) {
      
      case TimeSalary.thisMonth:
       return Text(
          'Tháng này',
          style: TextStyle(color: AppColors.thirdTextColor),
        );
      case TimeSalary.lastMonth:
         return Text(
          'Tháng trước',
          style: TextStyle(color: AppColors.thirdTextColor),
        );
      case TimeSalary.last2Months:
        return Text(
          'Tháng ${Jiffy(dateRange.start).subtract(months:2).month} - ${Jiffy(dateRange.start).subtract(months:2).year}',
          style: TextStyle(color: AppColors.thirdTextColor),
        );
      case TimeSalary.last3Months:
         return Text(
          'Tháng ${Jiffy(dateRange.start).subtract(months:3).month} - ${Jiffy(dateRange.start).subtract(months:3).year}',
          style: TextStyle(color: AppColors.thirdTextColor),
        );
        default:
        return Container();
    }
  }
  Widget _convertAttendanceState() {
    switch (selectedAttendance) {
      case AttendanceStateFilter.good:
        return Text(
          'Điểm danh đúng giờ',
          style: TextStyle(color: AppColors.thirdTextColor),
        );
      case AttendanceStateFilter.late24:
        return Text(
          'Điểm danh muộn 24h',
          style: TextStyle(color: AppColors.thirdTextColor),
        );
      case AttendanceStateFilter.late48:
        return Text(
          'Điểm danh muộn 48h',
          style: TextStyle(color: AppColors.thirdTextColor),
        );
      case AttendanceStateFilter.noInfo:
        return Text(
          'Không có thông tin điểm danh',
          style: TextStyle(color: AppColors.thirdTextColor),
        );
      case AttendanceStateFilter.notValid:
        return Text(
          'Điểm danh không hợp lệ',
          style: TextStyle(color: AppColors.thirdTextColor),
        );
      case AttendanceStateFilter.all:
        return Text(
          'Tất cả',
          style: TextStyle(color: AppColors.thirdTextColor),
        );
      default:
        return Container();
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}
