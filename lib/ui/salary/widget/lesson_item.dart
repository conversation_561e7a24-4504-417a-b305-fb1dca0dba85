import 'package:flutter/material.dart';
import 'package:flutter/src/foundation/key.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/placeholder.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:tutorO/constants/assets.dart';
import 'package:tutorO/constants/colors.dart';
import 'package:tutorO/models/salary/salary_model.dart';
import 'package:tutorO/stores/salary/salary_store.dart';
import 'package:tutorO/widgets/status_badges/status_badges.dart';

class ItemLesson extends StatefulWidget {
  const ItemLesson({Key? key, required this.teacherAttendanceInfo})
      : super(key: key);
  final ListTeacherAttendanceInfo teacherAttendanceInfo;
  @override
  State<ItemLesson> createState() => _ItemLessonState();
}

class _ItemLessonState extends State<ItemLesson> {
  bool isItemExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: EdgeInsets.only(bottom: 20),
        padding: EdgeInsets.symmetric(horizontal: 32),
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(15)),
        child: Stack(
          children: [
            Container(
              padding: EdgeInsets.only(top: 30, bottom: 15),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    alignment: Alignment.topLeft,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        color: AppColors.greenPositiveBgColor),
                    height: 50,
                    width: 50,
                    child: Center(
                      child: SvgPicture.asset(
                        Assets.money,
                        height: 20,
                        width: 20,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 10),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${widget.teacherAttendanceInfo.courseName}',
                              style: TextStyle(
                                  fontSize: 16, fontWeight: FontWeight.w600),
                            ),
                            Text(
                              '${widget.teacherAttendanceInfo.batchCode}',
                              style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.secondTextColor),
                            ),
                            Text(
                              convertSessionTime(
                                  widget.teacherAttendanceInfo
                                      .sessionStartDatetime!,
                                  widget.teacherAttendanceInfo
                                      .sessionEndDatetime!),
                              style: TextStyle(
                                  fontSize: 14,
                                  color: AppColors.secondTextColor),
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            if (isItemExpanded == true)
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Chi tiết',
                                    style: TextStyle(
                                        color: AppColors.orangeBtnColor,
                                        fontSize: 18,
                                        fontWeight: FontWeight.w600),
                                  ),
                                  Text(
                                      'GV: ${widget.teacherAttendanceInfo.facultyName}'),
                                  Text(
                                      'Cơ sở: ${widget.teacherAttendanceInfo.companyName}'),
                                  SizedBox(
                                    height: 10,
                                  ),
                                  _attendanceState(),
                                  SizedBox(
                                    height: 10,
                                  ),
                                  _checkinState(),
                                  SizedBox(
                                    height: 15,
                                  )
                                ],
                              )
                          ],
                        )),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                      top: 5,
                    ),
                    alignment: Alignment.topRight,
                    padding: EdgeInsets.all(10),
                    child: InkWell(
                        onTap: () {
                          setState(() {
                            isItemExpanded = !isItemExpanded;
                          });
                        },
                        child: Container(
                          padding: EdgeInsets.all(5),
                          child: !isItemExpanded
                              ? Text(
                                  'Xem chi tiết',
                                  style: TextStyle(
                                      fontSize: 16,
                                      color: AppColors.secondTextColor,
                                      fontWeight: FontWeight.w600),
                                )
                              : Text(
                                  'Ẩn',
                                  style: TextStyle(
                                      fontSize: 16,
                                      color: AppColors.secondTextColor,
                                      fontWeight: FontWeight.w600),
                                ),
                        )),
                  )
                ],
              ),
            ),
            if (!isItemExpanded)
              Positioned(
                right: 120,
                top: 47,
                child: Row(
                  children: [
                    if (attendanceStateConvert(
                            widget.teacherAttendanceInfo.attendanceState) !=
                        AttendanceState.good)
                      StatusBadge(
                        text: 'Lỗi điểm danh',
                        backgroundColor: AppColors.redNegativeBgColor,
                        textColor: AppColors.redNegativeTextColor,
                      ),
                    if (checkInStateConvert(
                            widget.teacherAttendanceInfo.checkInState) !=
                        CheckInState.good)
                      StatusBadge(
                        text: 'Lỗi Checkin',
                        backgroundColor: AppColors.redNegativeBgColor,
                        textColor: AppColors.redNegativeTextColor,
                      ),
                  ],
                ),
              )
          ],
        ));
  }

  Widget _attendanceState() {
    switch (
        attendanceStateConvert(widget.teacherAttendanceInfo.attendanceState)) {
      case AttendanceState.good:
        return Text(
          '- Điểm danh đúng giờ',
          style: TextStyle(
              fontWeight: FontWeight.w600,
              color: AppColors.greenPositiveTextColor),
        );
      case AttendanceState.late24:
        return Text(
          '- Điểm danh muộn 24h',
          style: TextStyle(
              fontWeight: FontWeight.w600,
              color: AppColors.redNegativeTextColor),
        );
      case AttendanceState.late48:
        return Text(
          '- Điểm danh muộn 48h',
          style: TextStyle(
              fontWeight: FontWeight.w600,
              color: AppColors.redNegativeTextColor),
        );
      case AttendanceState.noInfo:
        return Text(
          '- Không có thông tin Điểm danh',
          style: TextStyle(
              fontWeight: FontWeight.w600,
              color: AppColors.redNegativeTextColor),
        );
      case AttendanceState.notValid:
        return Text(
          '- Trạng thái điểm danh không hợp lệ',
          style: TextStyle(
              fontWeight: FontWeight.w600,
              color: AppColors.redNegativeTextColor),
        );
    }
  }

  Widget _checkinState() {
    switch (checkInStateConvert(widget.teacherAttendanceInfo.checkInState)) {
      case CheckInState.good:
        return Text(
          '- Checkin đúng giờ',
          style: TextStyle(
              fontWeight: FontWeight.w600,
              color: AppColors.greenPositiveTextColor),
        );

      case CheckInState.late:
        return Text(
          '- Checkin muộn',
          style: TextStyle(
              fontWeight: FontWeight.w600,
              color: AppColors.redNegativeTextColor),
        );
      default:
        return Text(
          '- Không có trạng thái checkin',
          style: TextStyle(
              fontWeight: FontWeight.w600,
              color: AppColors.redNegativeTextColor),
        );
    }
  }
}
