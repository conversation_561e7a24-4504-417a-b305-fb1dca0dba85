import 'package:flutter/material.dart';
import 'package:flutter/src/foundation/key.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/placeholder.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:provider/provider.dart';
import 'package:tutorO/constants/colors.dart';
import 'package:tutorO/constants/responsive.dart';
import 'package:tutorO/stores/personal_info/personal_info_store.dart';
import 'package:tutorO/ui/sidebar/menu_button.dart';
import 'package:tutorO/ui/sidebar/sidebar.dart';

class TemplateScreen extends StatefulWidget {
  const TemplateScreen({Key? key}) : super(key: key);

  @override
  State<TemplateScreen> createState() => _TemplateScreenState();
}

int selectedIndex = 3;

class _TemplateScreenState extends State<TemplateScreen> {
  late PersonalInfoStore _personalInfoStore;
  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  @override
  void didChangeDependencies() {
    // TODO: implement didChangeDependencies
    super.didChangeDependencies();
    _personalInfoStore = Provider.of<PersonalInfoStore>(context, listen: true);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: SidebarLeft(
          selectedIndex: selectedIndex, personalInfoStore: _personalInfoStore),
      key: _scaffoldKey,
      body: Observer(builder: (_) {
        return Row(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (Responsive.isDesktop(context))
              SidebarLeft(
                selectedIndex: selectedIndex,
                personalInfoStore: _personalInfoStore,
              ),
            Expanded(
                child: Container(
              height: MediaQuery.of(context).size.height,
              padding: EdgeInsets.all(32),
              color: AppColors.orangeBgColor,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    _buildTopContent(context),
                    // _buildBodyContent(),
                  ],
                ),
              ),
            ))
          ],
        );
      }),
    );
  }

  Widget _buildTopContent(BuildContext context) {
    return Row(
      children: [
        if (!Responsive.isDesktop(context))
          (MenuButtonSideBar(
            onTap: () {
              _scaffoldKey.currentState!.openDrawer();
            },
          ))
        else
          (SizedBox(
            width: 32,
          )),
      ],
    );
  }
}
