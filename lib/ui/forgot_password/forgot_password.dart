import 'package:another_flushbar/flushbar_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:tutorO/stores/user/authen/authen_store.dart';
import 'package:tutorO/widgets/empty_app_bar_widget.dart';
import '/constants/assets.dart';
import '/constants/colors.dart';
import '/stores/form/form_store.dart';
import '/utils/locale/app_localization.dart';
import '/widgets/rounded_button_widget.dart';
import '/widgets/textfield_widget.dart';
import 'package:skeletons/skeletons.dart';

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({Key? key}) : super(key: key);

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  // late AuthenStore _authStore;
  TextEditingController _userEmailController = TextEditingController();

  //stores:--------------------------------------------------------------------
  late AuthenStore _authStore;
  final store = FormStore();

  @override
  void initState() {
    store.setupValidations();
    super.initState();
    store.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // _themeStore = Provider.of<ThemeStore>(context);
    _authStore = Provider.of<AuthenStore>(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      primary: true,
      appBar: EmptyAppBar(),
      body: Observer(builder: (context) {
        return _buildBody();
      }),
    );
  }

  Widget _buildBody() {
    return Scaffold(
      body: Container(
        height: double.infinity,
        padding: EdgeInsets.symmetric(
          horizontal: 100,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildLeftSide(),
            _buildRightSide(),
          ],
        ),
      ),
    );
  }

  Widget _buildLeftSide() {
    return Container(
      width: MediaQuery.of(context).size.width * 0.4,
      height: 400,
      child: SvgPicture.asset(Assets.forgotPass, fit: BoxFit.cover,
          placeholderBuilder: (context) {
        return SkeletonAvatar(
          style: SkeletonAvatarStyle(borderRadius: BorderRadius.circular(15)),
        );
      }),
    );
  }

  Widget _buildRightSide() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 100),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
              color: AppColors.mainBorderColor,
              style: BorderStyle.solid,
              width: 1),
          boxShadow: [
            BoxShadow(
                color: AppColors.mainShadowColor,
                offset: Offset(1, 1),
                blurRadius: 10)
          ]),
      width: 400,
      height: 500,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            AppLocalizations.of(context)
                .translate('please_fill_email_to_reset_password'),
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 25),
            width: 300,
            child: Column(
              children: [
                _buildUserEmailField(),
                Container(
                    width: double.infinity,
                    child: Container(
                      padding: EdgeInsets.only(top: 5),
                      child: store.error.email == null
                          ? Text('')
                          : Text(
                              '${store.error.email}',
                              textAlign: TextAlign.left,
                              style: TextStyle(color: Colors.red),
                            ),
                    )),
              ],
            ),
          ),
          _buildConfirmButton(),
          SizedBox(height: 10),
          TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Padding(
                padding: const EdgeInsets.all(10.0),
                child: Text(AppLocalizations.of(context).translate('back'),
                    style: Theme.of(context)
                        .textTheme
                        .bodyMedium
                        ?.copyWith(color: AppColors.mainTextColor)),
              ))
        ],
      ),
    );
  }

  Widget _buildUserEmailField() {
    return RawKeyboardListener(
        focusNode: FocusNode(),
        onKey: (event) {
          if (event.isKeyPressed(LogicalKeyboardKey.enter) && store.canForgot) {
            _forgot();
          }
        },
        child: TextFieldWidget(
          hint: AppLocalizations.of(context).translate('login_et_user_email'),
          inputType: TextInputType.emailAddress,
          icon: Assets.singleUserGrayIcon,
          textController: _userEmailController,
          inputAction: TextInputAction.next,
          autoFocus: false,
          onChanged: (email) {
            store.setEmail(email);
          },
          maxLength: 50,
        ));
  }

  Widget _buildConfirmButton() {
    return RoundedButtonWidget(
      isEnable: store.canForgot,
      isLoading: _authStore.isLoading,
      buttonColorHover: AppColors.mainColor.withOpacity(0.7),
      buttonText: AppLocalizations.of(context).translate('confirm'),
      buttonColor: AppColors.mainColor,
      textColor: Colors.white,
      onPressed: () {
        if (store.canForgot) {
          _forgot();
        }
      },
    );
  }

  _forgot() {
    _authStore.forgot(store.email).then((value) {
      _authStore.newPasswordSent == true
          ? Future.delayed(Duration(milliseconds: 800)).then((value) {
              FlushbarHelper.createSuccess(
                message: _authStore.forgotResponseMessage,
                title: 'Thành công',
                duration: Duration(seconds: 3),
              )..show(context);
              Future.delayed(Duration(milliseconds: 800));
            })
          : () {};
      ;
    }).catchError((error) {
      _showErrorMessage('${error}');
    }).whenComplete(() {});

    // GoRouter.of(context).go('/');
  }

  _showErrorMessage(String message) {
    if (message.isNotEmpty) {
      Future.delayed(Duration(milliseconds: 0), () {
        if (message.isNotEmpty) {
          FlushbarHelper.createError(
            message: message,
            title: 'Lỗi',
            duration: Duration(seconds: 3),
          )..show(context);
        }
      });
    }

    return SizedBox.shrink();
  }

  @override
  void dispose() {
    // Clean up the controller when the Widget is removed from the Widget tree
    _userEmailController.dispose();
    super.dispose();
  }
}
