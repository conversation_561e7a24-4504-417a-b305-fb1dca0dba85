import 'package:flutter/material.dart';

import 'package:provider/provider.dart';
import 'package:tutorO/constants/assets.dart';
import 'package:tutorO/constants/colors.dart';
import 'package:tutorO/constants/styles.dart';
import 'package:tutorO/stores/personal_info/personal_info_store.dart';
import 'package:tutorO/ui/common/avatar.dart';
import 'package:tutorO/ui/settings/common/divider.dart';
import 'package:tutorO/ui/settings/common/section_item.dart';

class SidebarProfile extends StatefulWidget {
  SidebarProfile({Key? key}) : super(key: key);

  @override
  State<SidebarProfile> createState() => _SidebarProfileState();
}

class _SidebarProfileState extends State<SidebarProfile> {
  late PersonalInfoStore _personalInfoStore;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _personalInfoStore = Provider.of<PersonalInfoStore>(context, listen: true);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          left: BorderSide(
            color: Color(0xffCBD9E5).withOpacity(0.5),
            style: BorderStyle.solid,
            width: 1,
          ),
        ),
      ),
      width: 350,
      height: MediaQuery.of(context).size.height,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildAvatar(),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: Text(
              _personalInfoStore.personalInfoModel.fullName!,
              style: Styles.defaultFontStyle.copyWith(fontSize: 20),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountSection() {
    return Column(
      children: [
        _buildSectionItem(
          Assets.iconTeacherProfile,
          "Hồ sơ giáo viên",
          Container(
            margin: EdgeInsets.only(top: 4),
            child: Row(
              children: [
                Padding(
                  padding: const EdgeInsets.only(right: 4.0),
                  child: Image.asset(
                    Assets.unverifiedProfileIcon,
                    width: 15,
                    height: 15,
                  ),
                ),
                Text(
                  "Chờ duyệt",
                  style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                      color: AppColors.thirdTextColor),
                )
              ],
            ),
          ),
          false,
          callback: () {},
        ),
        SectionDivider()
      ],
    );
  }

  Widget _buildAvatar() {
    return Tooltip(
      message: 'Tài khoản',
      margin: EdgeInsets.only(top: 20),
      child: Container(
        height: 182,
        width: 192,
        child: _personalInfoStore.personalInfoModel.avatarUrl == null
            ? Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border:
                        Border.all(width: 3, color: AppColors.mainBorderColor)),
                child: Avatar(
                  width: 70,
                  padding: 0,
                  url: _personalInfoStore.personalInfoModel.avatarUrl,
                  callback: () {
                    // Navigator.of(context).pushNamed(Routes.profileInfo);
                  },
                ),
              )
            : Container(
                padding: EdgeInsets.all(50),
                child: Image.asset(
                  Assets.avatar2,
                  fit: BoxFit.cover,
                )),
        decoration: BoxDecoration(
            color: AppColors.orangeBgColor,
            borderRadius: BorderRadius.circular(16)),
      ),
    );
  }

  _buildSectionItem(String iconAssetPath, String title, Widget? subTitleWidget,
      bool rightArrow,
      {Function? callback, Color? colorText}) {
    return SectionItem(
      iconAssetPath,
      title,
      subTitleWidget,
      rightArrow,
      callback,
      null,
      colorText: colorText,
    );
  }
}
