import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:jumping_dot/jumping_dot.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sidebarx/sidebarx.dart';
import 'package:tutorO/constants/assets.dart';
import 'package:tutorO/constants/colors.dart';
import 'package:tutorO/constants/font_family.dart';
import 'package:tutorO/data/sharedpref/constants/preferences.dart';
import 'package:tutorO/stores/personal_info/personal_info_store.dart';
import 'package:tutorO/stores/user/authen/authen_store.dart';
import 'package:tutorO/ui/common/avatar.dart';
import 'package:tutorO/utils/routes/routes.dart';

class SidebarLeft extends StatefulWidget {
  SidebarLeft(
      {Key? key, this.selectedIndex = 0, required this.personalInfoStore})
      : super(key: key);
  final int selectedIndex;
  final PersonalInfoStore personalInfoStore;
  @override
  State<SidebarLeft> createState() => _SidebarLeftState();
}

class _SidebarLeftState extends State<SidebarLeft> {
  Color unSelectedColor = AppColors.orangeBtnColor;
  Color selectedColor = Colors.white;
  bool isFirst = true;
  late AuthenStore _authStore;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _authStore = Provider.of<AuthenStore>(context, listen: true);
  }

  @override
  Widget build(BuildContext context) {
    var personalInfo = widget.personalInfoStore.personalInfoModel;
    SidebarXController _controller =
        SidebarXController(selectedIndex: widget.selectedIndex, extended: true);
    return Container(
      child: SidebarX(
        headerBuilder: (context, extended) {
          return Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                margin: EdgeInsets.symmetric(horizontal: 50, vertical: 20),
                child: Image.asset(Assets.logo),
              ),
              Container(
                padding: EdgeInsets.only(top: 32),
                margin: EdgeInsets.only(bottom: 22, top: 10),
                height: 182,
                width: 192,
                decoration: BoxDecoration(
                  color: AppColors.orangeBgColor,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: widget.personalInfoStore.isSuccess == true
                    ? Column(
                        children: [
                          personalInfo.avatarUrl != null &&
                                  personalInfo.avatarUrl != ''
                              ? Avatar(
                                  url: personalInfo.avatarUrl,
                                  width: 85,
                                )
                              : personalInfo.gender == 0
                                  ? Image.asset(
                                      Assets.avatar7,
                                    )
                                  : Image.asset(
                                      Assets.avatar1,
                                    ),
                          SizedBox(
                            height: 6,
                          ),
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            child: Text(
                              personalInfo.fullName!,
                              style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.secondTextColor),
                            ),
                          )
                        ],
                      )
                    : SizedBox(
                        height: 20,
                        width: 20,
                        child: JumpingDots(
                          color: AppColors.orangeBtnColor,
                          radius: 10,
                          numberOfDots: 3,
                          animationDuration: Duration(milliseconds: 200),
                        ),
                      ),
              )
            ],
          );
        },
        footerBuilder: (context, extended) {
          return Container(
              child: InkWell(
            hoverColor: AppColors.secondTextColor,
            onTap: () {
              _dialogBuilder(context);
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 25),
              child: Row(
                children: [
                  Padding(
                    padding: const EdgeInsets.only(right: 20),
                    child: SvgPicture.asset(
                      Assets.signoutSidebar,
                      width: 22,
                      color: AppColors.orangeBtnColor,
                    ),
                  ),
                  Text(
                    'Đăng xuất',
                    style: TextStyle(
                        fontSize: 20, color: AppColors.secondTextColor),
                  )
                ],
              ),
            ),
          ));
        },
        animationDuration: Duration(milliseconds: 0),
        theme: SidebarXTheme(
          selectedItemDecoration: BoxDecoration(color: Colors.white),
          itemDecoration: BoxDecoration(color: Colors.white),
          itemPadding: EdgeInsets.symmetric(vertical: 12),
          selectedItemPadding: EdgeInsets.symmetric(vertical: 12),
          hoverColor: Colors.black.withOpacity(0.05),
          padding: EdgeInsets.symmetric(vertical: 50),
          width: 250,
          decoration: BoxDecoration(color: Colors.white),
          textStyle: TextStyle(
            color: AppColors.secondTextColor,
            fontSize: 18,
          ),
          selectedTextStyle: TextStyle(
            color: AppColors.secondTextColor,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        controller: _controller,
        showToggleButton: false,
        items: [
          SidebarXItem(
            onTap: () {
              Navigator.of(context).pushNamedAndRemoveUntil(
                Routes.home,
                (route) => false,
              );
            },
            label: 'Trang chủ',
            iconWidget: Container(
              margin: EdgeInsets.symmetric(horizontal: 20),
              child: SvgPicture.asset(
                Assets.homeSideBar,
                color: unSelectedColor,
                width: 22,
              ),
            ),
          ),
          SidebarXItem(
            onTap: () {
              Navigator.of(context).pushNamedAndRemoveUntil(
                Routes.schedule,
                (route) => false,
              );
            },
            label: 'Lịch giảng dạy',
            iconWidget: Container(
              margin: EdgeInsets.symmetric(horizontal: 20),
              child: SvgPicture.asset(
                Assets.calendarSidebar,
                width: 22,
                color: unSelectedColor,
              ),
            ),
          ),
          // SidebarXItem(
          //   onTap: () {
          //     Navigator.of(context).pushNamedAndRemoveUntil(
          //       Routes.report,
          //       (route) => false,
          //     );
          //   },
          //   label: 'Báo cáo',
          //   iconWidget: Container(
          //     margin: EdgeInsets.symmetric(horizontal: 20),
          //     child: SvgPicture.asset(
          //       Assets.reportSidebar,
          //       color: unSelectedColor,
          //       width: 22,
          //     ),
          //   ),
          // ),
          SidebarXItem(
            onTap: () {
              Navigator.of(context).pushNamedAndRemoveUntil(
                Routes.salary,
                (route) => false,
              );
            },
            label: 'Bảng lương dự kiến',
            iconWidget: Container(
              margin: EdgeInsets.symmetric(horizontal: 20),
              child: SvgPicture.asset(
                Assets.salary,
                color: unSelectedColor,
                width: 22,
              ),
            ),
          ),
          SidebarXItem(
            onTap: () {
              Navigator.of(context).pushNamedAndRemoveUntil(
                Routes.settings,
                (route) => false,
              );
            },
            label: 'Hồ sơ giáo viên',
            iconWidget: Container(
              margin: EdgeInsets.symmetric(horizontal: 20),
              child: Image.asset(
                Assets.threeDotsIcon,
                color: unSelectedColor,
                width: 22,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _dialogBuilder(BuildContext context) {
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: EdgeInsets.only(bottom: 20, right: 10, top: 10),
          titleTextStyle: TextStyle(
              fontFamily: FontFamily.quickSand,
              fontSize: 25,
              fontWeight: FontWeight.w600,
              color: AppColors.orangeBtnColor),
          titlePadding: EdgeInsets.only(left: 30, top: 30),
          contentPadding:
              EdgeInsets.only(left: 30, top: 20, bottom: 30, right: 30),
          title: const Text('Đăng xuất'),
          content: Text(
            'Bạn chắc chắn muốn đăng xuất ?',
            style: TextStyle(fontSize: 16, color: AppColors.secondTextColor),
          ),
          actions: <Widget>[
            TextButton(
              style: TextButton.styleFrom(
                textStyle: Theme.of(context).textTheme.labelLarge,
              ),
              child: const Text(
                'Hủy',
                style: TextStyle(color: AppColors.thirdTextColor, fontSize: 20),
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              style: TextButton.styleFrom(),
              child: Text('Xác nhận',
                  style: TextStyle(
                      fontFamily: FontFamily.quickSand,
                      fontSize: 20,
                      fontWeight: FontWeight.w500,
                      color: AppColors.orangeBtnColor)),
              onPressed: () {
                Navigator.of(context).pop();
                SharedPreferences.getInstance().then((preference) async {
                  await preference.setBool(Preferences.is_logged_in, false);
                  await _authStore.logout();
                  Navigator.of(context, rootNavigator: true)
                      .pushNamedAndRemoveUntil(Routes.login, (route) => false);
                });
              },
            ),
          ],
        );
      },
    );
  }
}
