import 'package:flutter/material.dart';

import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tutorO/constants/assets.dart';
import 'package:tutorO/constants/colors.dart';
import 'package:tutorO/data/sharedpref/constants/preferences.dart';
import 'package:tutorO/stores/user/authen/authen_store.dart';
import 'package:tutorO/utils/routes/routes.dart';

class DrawerSideBar extends StatefulWidget {
  DrawerSideBar(
      {Key? key,
      this.isTabActive1 = false,
      this.isTabActive2 = false,
      this.isTabActive3 = false,
      this.isTabActive4 = false})
      : super(key: key);
  final bool? isTabActive1;
  final bool? isTabActive2;
  final bool? isTabActive3;
  final bool? isTabActive4;
  @override
  State<DrawerSideBar> createState() => _DrawerSideBarState();
}

class _DrawerSideBarState extends State<DrawerSideBar> {
  double iconWidth = 24;
  late AuthenStore _authStore;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _authStore = Provider.of<AuthenStore>(context, listen: true);
  }

  @override
  Widget build(BuildContext context) {
    Color itemColor = AppColors.orangeBtnColor;
    Color itemTextColor = AppColors.secondTextColor;

    return Drawer(
      width: 250,
      child: ListView(
        padding: EdgeInsets.symmetric(vertical: 100),
        children: [
          DrawerHeader(
              child: Container(
            padding: EdgeInsets.symmetric(vertical: 30, horizontal: 5),
            height: 150,
            width: 150,
            child: Column(
              children: [
                Image.asset(
                  Assets.logo,
                  width: 150,
                ),
              ],
            ),
          )),
          ListTile(
            onTap: () {
              Navigator.pop(context);
              Navigator.of(context).pushNamedAndRemoveUntil(
                Routes.home,
                (route) => false,
              );
            },
            title: Container(
                padding: EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(right: 20),
                      child: SvgPicture.asset(
                        Assets.homeSideBar,
                        width: iconWidth,
                        color: itemColor,
                      ),
                    ),
                    Text(
                      'Trang chủ',
                      style: TextStyle(
                          fontSize: 16,
                          color: itemTextColor,
                          fontWeight: widget.isTabActive1 == true
                              ? FontWeight.w700
                              : FontWeight.normal),
                    )
                  ],
                )),
          ),
          ListTile(
            onTap: () {
              Navigator.pop(context);
              Navigator.of(context).pushNamedAndRemoveUntil(
                Routes.schedule,
                (route) => false,
              );
            },
            title: Container(
                padding: EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(right: 20),
                      child: SvgPicture.asset(
                        Assets.calendarSidebar,
                        width: iconWidth,
                        color: itemColor,
                      ),
                    ),
                    Text(
                      'Lịch giảng dạy',
                      style: TextStyle(
                        fontSize: 16,
                        color: itemTextColor,
                        fontWeight: widget.isTabActive2 == true
                            ? FontWeight.w700
                            : FontWeight.normal,
                      ),
                    )
                  ],
                )),
          ),
          ListTile(
            onTap: () {
              Navigator.pop(context);
              Navigator.of(context).pushNamedAndRemoveUntil(
                Routes.report,
                (route) => false,
              );
            },
            title: Container(
                padding: EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(right: 20),
                      child: SvgPicture.asset(
                        Assets.reportSidebar,
                        width: iconWidth,
                        color: itemColor,
                      ),
                    ),
                    Text(
                      'Báo cáo',
                      style: TextStyle(
                          fontSize: 16,
                          color: itemTextColor,
                          fontWeight: widget.isTabActive3 == true
                              ? FontWeight.w700
                              : FontWeight.normal),
                    )
                  ],
                )),
          ),
          ListTile(
            onTap: () {
              Navigator.pop(context);
              Navigator.of(context).pushNamedAndRemoveUntil(
                Routes.settings,
                (route) => false,
              );
            },
            title: Container(
                padding: EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(right: 20),
                      child: Image.asset(
                        Assets.threeDotsIcon,
                        width: iconWidth,
                        color: itemColor,
                      ),
                    ),
                    Text(
                      'Thêm',
                      style: TextStyle(
                          fontSize: 16,
                          color: itemTextColor,
                          fontWeight: widget.isTabActive4 == true
                              ? FontWeight.w700
                              : FontWeight.normal),
                    )
                  ],
                )),
          ),
          ListTile(
            onTap: () {
              Navigator.pop(context);
              SharedPreferences.getInstance().then((preference) async {
                await preference.setBool(Preferences.is_logged_in, false);
                await _authStore.logout();
                Navigator.of(context, rootNavigator: true)
                    .pushNamedAndRemoveUntil(Routes.login, (route) => false);
              });
            },
            title: Container(
                padding: EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(right: 20),
                      child: SvgPicture.asset(
                        Assets.signoutSidebar,
                        width: iconWidth,
                        color: itemColor,
                      ),
                    ),
                    Text(
                      'Đăng xuất',
                      style: TextStyle(fontSize: 16, color: itemTextColor),
                    )
                  ],
                )),
          ),
        ],
      ),
    );
  }
}
