import 'package:flutter/material.dart';

class MenuButtonSideBar extends StatefulWidget {
  MenuButtonSideBar({Key? key, this.onTap}) : super(key: key);
  final void Function()? onTap;
  @override
  State<MenuButtonSideBar> createState() => _MenuButtonSideBarState();
}

class _MenuButtonSideBarState extends State<MenuButtonSideBar> {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(right: 20),
      child: InkWell(
        borderRadius: BorderRadius.circular(99),
        child: Padding(
          padding: const EdgeInsets.all(15),
          child: Icon(Icons.menu),
        ),
        onTap: widget.onTap
        // _scaffoldKey.currentState!.openDrawer();
        ,
      ),
    );
  }
}
