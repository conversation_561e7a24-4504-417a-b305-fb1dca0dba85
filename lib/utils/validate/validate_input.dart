// ignore_for_file: constant_identifier_names

enum TypeValidate { EMPTY, DATE, PHONE, PASSWORD }

class ValidateInput {
  static bool validate(String value, List<TypeValidate> type) {
    if (type.contains(TypeValidate.EMPTY) && value.trim().isEmpty) {
      return false;
    }
    if (type.contains(TypeValidate.PHONE) && value.trim().length < 10) {
      return false;
    }
    if (type.contains(TypeValidate.PASSWORD)) {
      var pattern =
          r"^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[@!#$%^&*]).{8,}$";
      var regExp = RegExp(pattern);
      if (!regExp.hasMatch(value)) return false;
    }
    return true;
  }
}
