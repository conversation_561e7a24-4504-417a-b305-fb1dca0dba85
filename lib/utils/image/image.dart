import 'dart:io';

import 'package:flutter_image_compress/flutter_image_compress.dart';

class ImageConvert {
  static Future<File> compressFile(File file,
      {int maxFileSize = 1000000}) async {
    if (file.path.isEmpty) return file;
    final fileInputSize = file.lengthSync();
    if (fileInputSize < maxFileSize) return file;
    var result = file;
    try {
      final filePath = file.absolute.path;
      final lastIndex = filePath.lastIndexOf(RegExp(r'.jp'));
      final split = filePath.substring(0, (lastIndex));
      final outPath = "${split}_out${filePath.substring(lastIndex)}";
      var quality = ((maxFileSize / fileInputSize) * 100).round();
      result = (await FlutterImageCompress.compressAndGetFile(
          file.absolute.path, outPath,
          quality: quality))!;
      return result;
    } on Exception catch (_) {
      return file;
    }
  }
}
