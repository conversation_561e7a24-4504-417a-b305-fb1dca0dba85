import 'package:flutter/cupertino.dart';
import 'package:tutorO/ui/forgot_password/forgot_password.dart';
import 'package:tutorO/ui/dashboard/dashboard.dart';
import 'package:tutorO/ui/login/login.dart';
import 'package:tutorO/ui/report/report.dart';
import 'package:tutorO/ui/salary/salary.dart';
import 'package:tutorO/ui/settings/settings.dart';
import 'package:tutorO/ui/schedule/schedule_page.dart';
import 'package:tutorO/ui/splash/splash.dart';
import 'package:tutorO/ui/session/session_detail_screen.dart';
import 'package:tutorO/ui/session/lesson_plan_screen.dart';

class Routes {
  Routes._();

  //static variables
  static const String splash = '/splash';
  static const String login = '/login';
  static const String home = '/home';
  static const String forgotpassword = '/forgotpassword';
  static const String settings = '/settings';
  static const String socials = '/socials';
  static const String session_detail_screen = '/session_detail_screen';
  static const String lesson_plan_screen = '/lesson_plan_screen';
  static const String report = '/report';
  static const String schedule = '/schedules';
  static const String salary = '/salary';

  static final routes = <String, WidgetBuilder>{
    splash: (context) => SplashScreen(),
    login: (context) => LoginScreen(),
    home: (context) => HomeScreen(),
    forgotpassword: (context) => ForgotPasswordScreen(),
    settings: (context) => SettingScreen(),
    schedule: (context) => ScheduleScreen(),
    session_detail_screen: (context) => SessionDetailScreen(),
    lesson_plan_screen: (context) => LessonPlanScreen(),
    report: (context) => ReportScreen(),
    salary: (context) => SalaryScreen(),
  };
  static Route<dynamic>? onGenerateRoute(RouteSettings settings) {
    return CupertinoPageRoute(
        builder: (context) => routes[settings.name]!(context),
        settings: settings);
  }

  static bool isScreenNameExisted(String screenName) {
    return routes.containsKey(screenName);
  }

  static bool isRouteRequireAuthorization(String screenName) {
    return ![Routes.splash, Routes.login, Routes.forgotpassword]
        .contains(screenName);
  }
}
