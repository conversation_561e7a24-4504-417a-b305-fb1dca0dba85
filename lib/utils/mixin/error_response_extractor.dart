mixin ErrorResponseExtractor {
  String? detail;

  String? extractErrorString(Map<String, dynamic> errJSON, String key) {
    if (detail == null && errJSON.containsKey("detail")) {
      detail = errJSON["detail"];
    }
    if (errJSON.containsKey(key)) {
      final arrErrors = List<String>.from(errJ<PERSON><PERSON>[key]);
      return arrErrors.isNotEmpty ? arrErrors.first : null;
    }

    return null;
  }
}
