import 'package:intl/intl.dart';

class DateConvert {
  static String convertDate(String? date, String fromFormat, String toFormat) {
    if (date == null) {
      return "";
    }
    if (date.isEmpty) return "";
    var dateInput;
    try {
      var inputFormat = DateFormat(fromFormat);
      dateInput = inputFormat.parse(date.trim());
    } on Exception catch (_) {
      return date;
    }
    var outputFormat = DateFormat(toFormat);
    return outputFormat.format(dateInput);
  }

  static String formatDate(DateTime? date, {String format = 'yyyy/MM/dd'}) {
    if (date == null) return "";
    var formatter = DateFormat(format);
    return formatter.format(date);
  }
}
