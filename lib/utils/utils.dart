// Common utils
import 'package:tutorO/models/class/class.dart';
import 'package:tutorO/models/class_session/class_session.dart';

class Utils {
  static String convertSecondsToDurationTilMin(int seconds) {
    final duration = Duration(seconds: seconds);
    return "${duration.inMinutes.remainder(60).toString().padLeft(2, '0')}"
        ":${duration.inSeconds.remainder(60).toString().padLeft(2, '0')}";
  }

  static String? findNotEmptyFromArray(List<String?> strings) {
    return strings.firstWhere(
        (element) => element != null && element.isNotEmpty,
        orElse: () => null);
  }

  static bool isSuccessHttpStatus(int status) {
    return status >= 200 && status <= 299;
  }

  static T? enumFromString<T>(Iterable<T> values, String value) {
    try {
      return values
          .singleWhere((type) => type.toString().split(".").last == value);
      // ignore: avoid_catching_errors
    } on StateError catch (_) {
      return null;
    }
  }

  static bool equalDateRes(DateRes date1, DateRes date2) {
    return date1.day == date2.day &&
        date1.month == date2.month &&
        date1.year == date2.year;
  }

  static bool isStartLesson(ClassSession classSession) {
    try {
      var minBeforeClassStart = 15;
      var dateTime = DateTime.now();
      var houseStartTime =
          (classSession.date!.startTime! - minBeforeClassStart) ~/ 60;
      var minStartTime =
          ((classSession.date!.startTime! - minBeforeClassStart) % 60).toInt();
      var dateClassSession = DateTime(
          classSession.date!.year!,
          classSession.date!.month!,
          classSession.date!.day!,
          houseStartTime,
          minStartTime);
      if (dateTime.isAfter(dateClassSession)) {
        return true;
      }
      // ignore: avoid_catches_without_on_clauses
    } catch (_) {
      return false;
    }
    return false;
  }

  static bool isValidUrl(String? urlString) {
    if (urlString == null) {
      return false;
    }

    return Uri.tryParse(urlString)?.hasAbsolutePath ?? false;
  }
}

extension ToBool on String {
  /// Disclaimer: This is a stupid way I have to put on my code to handle shitty response from server
  bool toBoolStupidWay() {
    return ['yes', 'true', '1'].contains(toLowerCase());
  }
}
