import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';

// ignore: constant_identifier_names
enum MediaTypePicked { Picture, Video, None }

// ignore: avoid_classes_with_only_static_members
class FileHelper {
  static HttpClient httpClient = HttpClient();
  static Future<File> downloadFile(String url, String filename) async {
    var request = await httpClient.getUrl(Uri.parse(url));
    var response = await request.close();
    var bytes = await consolidateHttpClientResponseBytes(response);
    var dir = (await getApplicationDocumentsDirectory()).path;
    var file = File('$dir/$filename');
    await file.writeAsBytes(bytes);
    return file;
  }

  static String getFileName(File file) {
    return getFileNameFromPath(file.path);
  }

  static String getFileNameFromPath(String path) {
    return path.split("/").last;
  }

  static MediaTypePicked getMediaType(String typeFile) {
    if (["MP4", "MP4", "VIDEO"].contains(typeFile)) {
      return MediaTypePicked.Video;
    }
    if (["JPG", "JPGE", "PNG", "IMAGE"].contains(typeFile)) {
      return MediaTypePicked.Picture;
    }
    return MediaTypePicked.None;
  }

  static String getTypeFileFormatByServer(
      MediaTypePicked type, String fileName) {
    if (type == MediaTypePicked.Picture) {
      return "IMAGE";
    } else if (type == MediaTypePicked.Video) {
      return "VIDEO";
    }
    return "";
  }
}
