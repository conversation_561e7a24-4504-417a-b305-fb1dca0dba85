//
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';

import '../../data/local/constants/types.dart';

/// Helper class for device related operations.
///
class DeviceUtils {
  ///
  /// hides the keyboard if its already open
  ///
  static void hideKeyboard(BuildContext context) {
    FocusScope.of(context).unfocus();
  }

  ///
  /// accepts a double [scale] and returns scaled sized based on the screen
  /// orientation
  ///
  static double getScaledSize(BuildContext context, double scale) =>
      scale *
      (MediaQuery.of(context).orientation == Orientation.portrait
          ? MediaQuery.of(context).size.width
          : MediaQuery.of(context).size.height);

  ///
  /// accepts a double [scale] and returns scaled sized based on the screen
  /// width
  ///
  static double getScaledWidth(BuildContext context, double scale) =>
      scale * MediaQuery.of(context).size.width;

  ///
  /// accepts a double [scale] and returns scaled sized based on the screen
  /// height
  ///
  static double getScaledHeight(BuildContext context, double scale) =>
      scale * MediaQuery.of(context).size.height;

  static double convertPercentToDpi(
      BuildContext context, double percent, TypeConvertPx typeConvert) {
    if (typeConvert == TypeConvertPx.Height) {
      return (MediaQuery.of(context).size.height / 100) * percent;
    } else if (typeConvert == TypeConvertPx.Width) {
      return (MediaQuery.of(context).size.width / 100) * percent;
    }
    return 0;
  }

  static Future<String> getDeviceIdentifier() async {
    var deviceIdentifier = "unknown";
    var deviceInfo = DeviceInfoPlugin();

    if (Platform.isIOS) {
      var iosInfo = await deviceInfo.iosInfo;
      deviceIdentifier = iosInfo.identifierForVendor ?? "unknown";
    } else if (kIsWeb) {
      // The web doesnt have a device UID,
      // so use a combination fingerprint as an example
      var webInfo = await deviceInfo.webBrowserInfo;
      deviceIdentifier = webInfo.vendor! +
          webInfo.userAgent! +
          webInfo.hardwareConcurrency.toString();
    } else if (Platform.isLinux) {
      var linuxInfo = await deviceInfo.linuxInfo;
      deviceIdentifier = linuxInfo.machineId ?? "unknown";
    }
    return deviceIdentifier;
  }

  static int getIosVersion(IosDeviceInfo info) {
    var majorIos = info.systemVersion!.split(".")[0];
    return int.parse(majorIos);
  }
}
