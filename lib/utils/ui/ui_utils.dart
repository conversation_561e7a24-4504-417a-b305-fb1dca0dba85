import 'package:another_flushbar/flushbar_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screen_scaling/flutter_screen_scaling.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

import 'package:flutter_svg/flutter_svg.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../../constants/assets.dart';
import '../../constants/colors.dart';
import '../locale/app_localization.dart';

class UIUtils {
  static AppBar buildCommonAppBar(BuildContext context, String title,
      {TextStyle? titleStyle,
      bool backBtnVisible = true,
      Color backgroundColor = Colors.transparent,
      Widget? rightAction}) {
    final defaultTitleStyle = TextStyle(
        fontSize: ScreenScale.convertFontSize(18), fontWeight: FontWeight.bold);

    return AppBar(
      backgroundColor: backgroundColor,
      leading: backBtnVisible
          ? IconButton(
              icon: Icon(Icons.arrow_back_ios, color: Colors.black),
              onPressed: () => Navigator.of(context).pop(),
            )
          : Container(),
      title: Text(
        title,
        style: titleStyle ?? defaultTitleStyle,
      ),
      centerTitle: true,
      shadowColor: Colors.transparent,
      actions: rightAction != null ? [rightAction] : [],
    );
  }

  static String dummyImgPlaceholderUrl(int width, int height) {
    return 'https://via.placeholder.com/${width}x${height}?text=Avatar';
  }

  static String dummySquareImgPlaceholderUrl(int size) {
    return UIUtils.dummyImgPlaceholderUrl(size, size);
  }

  static Widget uploadImageButton(Function callback) {
    return GestureDetector(
      onTap: () {
        callback();
      },
      child: Container(
        width: double.infinity,
        alignment: Alignment.center,
        padding: EdgeInsets.symmetric(vertical: ScreenScale.convertHeight(18)),
        decoration: BoxDecoration(
          color: Color(0xff7B57FB),
          boxShadow: [
            BoxShadow(
              blurRadius: 20,
              offset: Offset(0, 10),
              color: Color(0xffb1bcc7).withOpacity(0.25),
              spreadRadius: -6,
            ),
          ],
          borderRadius: BorderRadius.circular(6),
        ),
        child: Text(
          "Upload",
          style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.w700,
              fontSize: ScreenScale.convertFontSize(18)),
        ),
      ),
    );
  }

  static Widget chooseImageUploadButton(String title, Function callback) {
    return GestureDetector(
      onTap: () {
        callback();
      },
      child: Container(
        width: double.infinity,
        alignment: Alignment.center,
        padding: EdgeInsets.symmetric(vertical: ScreenScale.convertHeight(11)),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: Color(0xffCBD9E5)),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(Assets.uploadIcon),
            Container(
              margin: EdgeInsets.only(left: ScreenScale.convertWidth(4)),
              child: Text(
                "Chọn ảnh tải lên",
                style: TextStyle(
                    color: AppColors.mainTextColor,
                    fontWeight: FontWeight.w700,
                    fontSize: ScreenScale.convertFontSize(18)),
              ),
            ),
          ],
        ),
      ),
    );
  }

  static void showErrorMessage(String? message, BuildContext context) {
    Future.delayed(Duration(milliseconds: 0), () {
      if (message != null && message.isNotEmpty) {
        FlushbarHelper.createError(
          message: message,
          title: 'Lỗi',
          duration: Duration(seconds: 3),
        ).show(context);
      }
    });
  }

  static void showSuccessMessage(String? message, BuildContext context) {
    Future.delayed(Duration(milliseconds: 0), () {
      if (message != null && message.isNotEmpty) {
        FlushbarHelper.createSuccess(
          message: message,
          title: 'Thành công',
          duration: Duration(seconds: 2),
        ).show(context);
      }
    });
  }

  static Widget commonPullToRefreshHeader(BuildContext context) {
    var _appLocalizations = AppLocalizations.of(context);
    return ClassicHeader(
      idleText: 'Kéo xuống để làm mới',
      refreshingText: 'Đang tải',
      releaseText: 'Thả để làm mới',
      completeText: 'Làm mới thành công',
      failedText: 'Thất bại',
    );
  }

  static Widget commonReleaseToLoadMoreFooter(BuildContext context) {
    var _appLocalizations = AppLocalizations.of(context);
    return ClassicFooter(
      idleText: 'Kéo lên để tải thêm',
      loadingText: 'Đang tải',
      canLoadingText: 'Thả để tải',
      noDataText: "Không có dữ liệu mới",
      failedText: "Tải thất bại",
    );
  }

  static Widget dotSeparator(BuildContext context,
      {Color color = const Color(0xffcbd9e5),
      double size = 4,
      double horizontalMargin = 8}) {
    return Container(
      width: size,
      height: size,
      margin: EdgeInsets.symmetric(horizontal: horizontalMargin),
      decoration: BoxDecoration(
          color: color, borderRadius: BorderRadius.circular(size * 0.5)),
    );
  }

  static Offset getWidgetLocalToGlobal(BuildContext context) {
    final box = context.findRenderObject() as RenderBox?;
    return box!.localToGlobal(Offset.zero);
  }

  static Widget commonHourGlassLoadingIndicator() {
    return SpinKitHourGlass(
      color: AppColors.mainColor,
      size: 50,
    );
  }
}
