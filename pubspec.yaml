name: tutorO
description: A flutter tutorO project created using MobX and Provider.
publish_to: none
# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# Read more about versioning at semver.org.
version: 1.0.1+20

environment:
  sdk: ">=2.12.0 <3.0.0"

dependencies:
  analyzer: ^5.2.0
  another_flushbar: ^1.12.30
  quiver: ^3.0.1
  back_button_interceptor: ^6.0.2
  better_player: ^0.0.63
  connectivity_plus: ^3.0.3
  cupertino_icons: ^1.0.3
  debounce_throttle: ^2.0.0
  desktop_window: ^0.4.0
  device_info_plus: ^8.0.0
  dio: ^4.0.0
  direct_link: ^0.1.7
  dotted_line: ^3.0.0
  dropdown_search: ^5.0.5
  emoji_picker_flutter: ^1.0.6
  file_picker: ^5.2.5
  firebase_core: ^2.17.0
  firebase_crashlytics: ^3.3.7
  firebase_messaging: ^14.6.9
  flutter:
    sdk: flutter
  flutter_config: ^2.0.0
  flutter_cupertino_datetime_picker: ^3.0.0
  # flutter_cupertino_localizations: ^1.0.1
  # flutter_facebook_auth: ^5.0.7
  flutter_image_compress: ^1.0.0
  flutter_keyboard_visibility: ^5.0.2
  flutter_localizations:
    sdk: flutter
  flutter_mobx: ^2.0.0
  flutter_rating_bar: ^4.0.0
  flutter_screen_scaling: 3.3.2+3
  flutter_slidable: ^2.0.0
  flutter_spinkit: ^5.1.0
  flutter_svg: ^2.0.0+1
  flutter_tab_indicator_styler: ^2.0.0
  flutter_video_info: ^1.2.0
  get_it: ^7.1.3
  go_router: ^6.0.6
  google_fonts: ^4.0.3
  google_sign_in: ^5.0.1
  http: ^0.13.3
  image_cropper: ^3.0.1
  image_picker: ^0.8.6+1
  injecteo: ^1.0.0
  injecteo_generator: ^1.0.1
  intl: ^0.18.0
  lehttp_overrides: ^1.0.1+3
  loader_overlay: ^2.0.1
  loading_indicator: ^3.1.0
  material_dialog: ^1.0.0-nullsafety.1
  mobx: ^2.0.1
  navigation_history_observer: ^1.1.0
  open_store: ^0.5.0
  package_info_plus: ^3.0.2
  page_transition: ^2.0.9
  path_provider: ^2.0.2
  percent_indicator: ^4.2.2
  provider: ^6.0.5
  pull_to_refresh: ^2.0.0
  qr_code_scanner: ^1.0.1
  random_color: ^1.0.5
  sembast: ^3.1.0+2
  sendbird_sdk: ^3.0.0
  share: ^2.0.4
  shared_preferences: ^2.0.6
  sidebarx: ^0.14.0
  sign_in_with_apple: ^4.3.0
  skeleton_text: ^3.0.0
  skeletons: ^0.0.3
  sliver_tools: ^0.2.5
  syncfusion_flutter_datepicker: ^20.4.49
  system_settings: ^2.0.0
  tuple: ^2.0.0
  uni_links: ^0.5.1
  update_available: ^2.1.0
  url_launcher: ^6.0.4
  uuid: ^3.0.4
  validators: ^3.0.0
  validators2: ^4.0.0
  webview_flutter: ^4.0.2
  window_manager: ^0.3.0
  webview_windows: ^0.2.2
  cached_network_image: ^3.3.1
  expand_widget: ^3.1.1
  xxtea: ^2.1.0
  table_calendar:
    path: "lib/src/table_calendar"
  popover:
    git:
      url: https://github.com/huytd2510/popover
      ref: main
  jumping_dot: ^0.0.4
  fl_chart: ^0.61.0
  buttons_tabbar: ^1.3.6
  firedart: ^0.9.0+1
  fluent_ui: ^4.4.1
  firebase_core_desktop: ^1.0.2
  firebase_auth: ^4.10.1
  desktop_webview_window: ^0.2.0
  platform_device_id: ^1.0.1
  carousel_slider: ^4.2.1
  jiffy: ^5.0.0
dev_dependencies:
  build_runner: ^2.2.0
  flutter_launcher_icons: ^0.11.0
  flutter_test:
    sdk: flutter

  logger: ^1.1.0
  mobx_codegen: ^2.0.1+3

flutter_icons:
  image_path: "assets/icons/ic_launcher.png"
  android: true
  ios: true

# For information on the generic Dart art of this file, see the
# following page: https://www.dartlang.org/tools/pub/pubspec
# The following section is specific to Flutter.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/lang/
    - lib/data/salary.json
    - .env
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.io/assets-and-images/#resolution-aware.
  # For details regarding adding assets from package dependencies, see
  # https://flutter.io/assets-and-images/#from-packages
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Quicksand
      fonts:
        - asset: assets/fonts/Quicksand-Regular.ttf
          weight: 400
        - asset: assets/fonts/Quicksand-Light.ttf
          weight: 300
        - asset: assets/fonts/Quicksand-Medium.ttf
          weight: 500
        - asset: assets/fonts/Quicksand-Semibold.ttf
          weight: 600
        - asset: assets/fonts/Quicksand-Bold.ttf
          weight: 700
  # For details regarding fonts from package dependencies,
  # see https://flutter.io/custom-fonts/#from-packages
