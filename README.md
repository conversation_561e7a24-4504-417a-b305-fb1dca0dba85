# tutorO Project

fvm use 3.10.0

A tutorO project created in flutter using MobX

## Getting Started

## How to Use

**Step 1:**

Download or clone this repo by using the link below:

```
https://gitlab.com/teky.vn/tutoro/tutoro-desktop
```

**Step 2:**

Do project sử dụng thư viện "inject" hoạt động dựa trên code gen, ch<PERSON>y các lệnh sau để setup project

```
flutter pub get
```

flutter packages pub run build_runner build --delete-conflicting-outputs

```
Hoặc chạy command sau để sync :

```

flutter packages pub run build_runner watch

```

## Ẩn generated files

Trong Android Studio, chọn `Android Studio` -> `Preferences` -> `Editor` -> `File Types` và dán các đuôi file dưới phần `ignore files and folders` :

```

_.inject.summary;_.inject.dart;\*.g.dart;

```

Trong Visual Studio Code, Chọn `Preferences` -> `Settings` và tìm tới `Files:Exclude`. Thêm các phần sau:

```

**/\*.inject.summary
**/_.inject.dart
\*\*/_.g.dart

```

## tutorO Features:
```
